extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

@onready var resource_type_label: Label = %ResourceTypeLabel
@onready var resource_amount_label: Label = %ResourceAmountLabel # If applicable (e.g., amount remaining in node)
@onready var resource_list_container: VBoxContainer = %ResourceListContainer

func _ready() -> void:
	super._ready()
	KokumeApi.resource_loaded.connect(_resource_data_loaded)

# Called when the modal is opened with specific resource data
func open_and_fill_modal(data: Dictionary):
	var data_properties = data.get("properties", {})
	if data_properties.is_empty():
		printerr("ResourceInfoModal: Received null resource data.")
		close_modal()
		return

	if data_properties.has('id'):
		KokumeApi.get_resource(int(data_properties.get('id')))

	if title_label:
		title_label.text = "Zdroj: %s" % data_properties.get("name")

	if resource_type_label:
		resource_type_label.text = "Typ: %s" % data_properties.get("resource", "N/A")

	if resource_amount_label:
		# Check if amount data is available
		if data_properties.has("amount"):
			resource_amount_label.text = "Amount Available: %s" % str(data_properties.get("amount"))
			resource_amount_label.visible = true
		else:
			resource_amount_label.visible = false

	open_modal()


func _resource_data_loaded(data: Dictionary):
	print(data)
	if resource_list_container:
		# Clear previous resource entries
		for child in resource_list_container.get_children():
			child.queue_free()

		var resources = data.get("resource_amounts", {})
		if resources is Array and not resources.is_empty():
			for resource:Dictionary in resources:
				var amount = resource.amount
				var resource_label = Label.new()
				# Capitalize resource name for display
				resource_label.text = "%s: %s" % [resource.type.name, amount]
				resource_list_container.add_child(resource_label)

				# Alternative: Instantiate a custom scene for each resource line
				# var resource_line = ResourceLineScene.instantiate()
				# resource_line.set_resource_data(resource_name, amount) # Assuming ResourceLine scene has this method
				# resource_list_container.add_child(resource_line)
		else:
			# Optional: Display a message if no resources
			var no_resource_label = Label.new()
			no_resource_label.text = "Nic tu není..."
			resource_list_container.add_child(no_resource_label)
