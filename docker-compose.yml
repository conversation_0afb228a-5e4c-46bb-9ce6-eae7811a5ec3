services:
  php:
    image: registry.kickme.cz/docker/ci-cd/php:8.2-dev
    user: "1000:1000"
    depends_on:
      - postgres
    env_file: dev.env
    volumes:
      - .:/app

  npm:
    image: registry.kickme.cz/docker/ci-cd/npm:20.3.1
    user: "1000:1000"
    volumes:
      - .:/app
    entrypoint: ["npm", "ci", "&&", "npm", "run", "build-dev"]

  composer:
    image: registry.kickme.cz/docker/ci-cd/php:8.2-dev
    user: "1000:1000"
    depends_on:
      - postgres
    env_file: dev.env
    volumes:
      - .:/app
    entrypoint: ["composer", "install"]

  web:
    image: nginx:stable-alpine
    container_name: nginx
    depends_on:
      - php
    ports:
      - "8000:80"
    volumes:
      - .:/app
      - .docker/nginx.conf:/etc/nginx/nginx.conf

  postgres:
    image: postgis/postgis:latest
    env_file: dev.env
    volumes:
      - .docker/data/postgres:/var/lib/postgresql/data

  osm2pgsql:
    image: iboates/osm2pgsql:latest
    env_file: dev.env
    scale: 0
    volumes:
      - .:/app

  adminer:
    image: dockette/adminer:dg
    ports:
      - 8080:80
