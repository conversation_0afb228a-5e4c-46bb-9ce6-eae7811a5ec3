{"name": "kokume-web", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"pinia": "^3.0.1", "vue": "^3.5.13", "vue-i18n": "^9.14.4"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.1", "terser": "^5.39.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-router": "^4.5.1", "vue-tsc": "^2.2.8"}}