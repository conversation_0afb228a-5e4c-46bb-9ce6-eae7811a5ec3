# Kokume Web

This is the web application for the Kokume game project. It's built with Vue 3 and Tailwind CSS, providing a modern and responsive landing page for the game.

## Features

- Responsive design optimized for mobile and desktop
- Game information and description
- Download links for Android and iOS
- Real-time game statistics from the backend API
- Modern UI with Tailwind CSS
- Multilingual support (English and Czech) with easy addition of more languages

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with ESLint

```sh
npm run lint
```

## Docker

The project includes a Dockerfile for containerized deployment. To build and run the Docker container:

```sh
# Build the Docker image
docker build -t kokume-web .

# Run the container
docker run -p 8080:80 kokume-web
```

## Environment Variables

The following environment variables can be configured:

- `VITE_API_URL`: The URL of the backend API (defaults to 'https://kokume.eu/')

Create a `.env.local` file in the project root to set these variables for local development.

## CI/CD

The project is configured for GitLab CI/CD. The pipeline will:

1. Install dependencies
2. Build the application
3. Create a Docker image
4. Push the image to the GitLab registry
5. Deploy to production via Portainer

## Project Structure

- `src/components/`: Vue components
- `src/assets/`: Static assets like images and CSS
- `src/services/`: API services for backend communication
- `src/i18n/`: Internationalization files and configuration
- `src/i18n/locales/`: Language JSON files (en.json, cs.json)
- `public/`: Public static files

## Internationalization

The web application supports multiple languages using Vue I18n. Currently, English and Czech are implemented, but more languages can be easily added:

1. Create a new JSON file in `src/i18n/locales/` (e.g., `de.json` for German)
2. Add the language to the i18n configuration in `src/i18n/index.ts`
3. Add the language option to the language switcher in `src/components/LanguageSwitcher.vue`

The application will:

- Detect the user's browser language and use it if supported
- Remember the user's language preference in localStorage
- Allow switching languages via the language switcher in the header
- Update page metadata (title, description) based on the selected language

## Backend API Integration

The web application communicates with the Kokume backend API to fetch game statistics and other data. The API service is implemented in `src/services/api.service.ts`.
