import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';

/**
 * API Service for communicating with the Kokume backend
 */
export class ApiService {
  private api: AxiosInstance;
  private static instance: ApiService;

  private constructor() {
    // Use the production URL by default, can be overridden for development
    const baseURL = import.meta.env.VITE_API_URL || 'https://kokume.eu';

    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
  }

  /**
   * Get singleton instance of ApiService
   */
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  /**
   * Get game statistics
   * @returns Promise with game statistics
   */
  public async getGameStats(): Promise<any> {
    try {
      // This endpoint needs to be implemented on the backend
      const response: AxiosResponse = await this.api.get('/api/v1/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching game statistics:', error);
      // Return mock data for now until the backend endpoint is implemented
      return this.getMockStats();
    }
  }

  /**
   * Get mock statistics for development
   * @returns Mock statistics object
   */
  private getMockStats(): any {
    return {
      players: 1250,
      activeRegions: 3789,
      buildings: 12567,
      battles: 4328,
      resources: {
        wood: 45678,
        stone: 34567,
        food: 78901,
        gold: 12345
      },
      topPlayers: [
        { name: 'Player1', score: 12500 },
        { name: 'Player2', score: 11200 },
        { name: 'Player3', score: 10800 },
        { name: 'Player4', score: 9700 },
        { name: 'Player5', score: 8900 }
      ]
    };
  }
}
