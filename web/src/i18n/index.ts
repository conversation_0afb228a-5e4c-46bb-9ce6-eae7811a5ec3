import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import cs from './locales/cs.json'

// Define supported locales type
type SupportedLocale = 'en' | 'cs';

// Detect browser language or use stored preference
const getBrowserLanguage = (): SupportedLocale => {
  // First check if there's a stored preference
  const storedLang = localStorage.getItem('kokume-language')
  if (storedLang && ['en', 'cs'].includes(storedLang)) {
    return storedLang as SupportedLocale
  }

  // Otherwise detect from browser
  const browserLang = navigator.language.split('-')[0]
  return ['en', 'cs'].includes(browserLang) ? browserLang as SupportedLocale : 'en'
}

// Create i18n instance
export const i18n = createI18n({
  legacy: false, // Use Composition API
  locale: getBrowserLanguage(),
  fallbackLocale: 'en',
  messages: {
    en,
    cs
  }
})

// Helper function to set HTML lang attribute
export const setHtmlLang = (locale: SupportedLocale): void => {
  document.documentElement.setAttribute('lang', locale)
}

// Helper function to set page metadata based on locale
export const updateMetadata = (locale: SupportedLocale): void => {
  const messages = i18n.global.getLocaleMessage(locale)

  // Update title and description
  document.title = messages.meta.title
  const metaDescription = document.querySelector('meta[name="description"]')
  if (metaDescription) {
    metaDescription.setAttribute('content', messages.meta.description)
  }
}

// Helper function to change locale
export const setLocale = (locale: string): void => {
  // Validate that the locale is supported
  if (['en', 'cs'].includes(locale)) {
    const typedLocale = locale as SupportedLocale
    i18n.global.locale.value = typedLocale
    localStorage.setItem('kokume-language', locale)
    setHtmlLang(typedLocale)
    updateMetadata(typedLocale)
  } else {
    console.warn(`Locale ${locale} is not supported. Falling back to English.`)
    i18n.global.locale.value = 'en'
    localStorage.setItem('kokume-language', 'en')
    setHtmlLang('en')
    updateMetadata('en')
  }
}

// Initialize HTML lang attribute and metadata
setHtmlLang(i18n.global.locale.value)
updateMetadata(i18n.global.locale.value)

export default i18n
