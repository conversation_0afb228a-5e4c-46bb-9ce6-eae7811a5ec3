<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { setLocale } from '../i18n'

const { t, locale } = useI18n()
const isOpen = ref(false)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const changeLanguage = (lang: string) => {
  setLocale(lang)
  isOpen.value = false
}

// Close dropdown when clicking outside
const closeDropdown = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.language-switcher')) {
    isOpen.value = false
  }
}

// Add event listener when component is mounted
document.addEventListener('click', closeDropdown)
</script>

<template>
  <div class="language-switcher relative">
    <button 
      @click="toggleDropdown" 
      class="flex items-center text-white hover:text-accent"
    >
      <span class="mr-1">{{ locale.toUpperCase() }}</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>
    
    <div v-if="isOpen" class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg z-10">
      <div class="py-1">
        <button 
          @click="changeLanguage('en')" 
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          :class="{ 'bg-gray-100': locale === 'en' }"
        >
          {{ t('language.en') }}
        </button>
        <button 
          @click="changeLanguage('cs')" 
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          :class="{ 'bg-gray-100': locale === 'cs' }"
        >
          {{ t('language.cs') }}
        </button>
      </div>
    </div>
  </div>
</template>
