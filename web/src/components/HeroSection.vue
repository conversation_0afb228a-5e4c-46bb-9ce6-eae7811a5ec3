<template>
  <section class="py-20 bg-gradient-to-b from-dark to-gray-800 text-white">
    <div class="container mx-auto px-4 flex flex-col items-center">
      <!-- Use a div with background color instead of an image -->
      <div class="w-64 h-64 mb-8 rounded-full bg-primary flex items-center justify-center">
        <img src="/images/logo.png" alt="Kokume" class="rounded-full">
      </div>
      <h1 class="text-5xl font-bold text-center mb-6">{{ $t('hero.title') }}</h1>
      <p class="text-xl text-center max-w-2xl mb-10">
        {{ $t('hero.subtitle') }}
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <a
          href="#download"
          class="bg-primary hover:bg-primary/80 text-white font-bold py-3 px-8 rounded-full transition-colors duration-300"
        >
          {{ $t('hero.download') }}
        </a>
        <a
          href="#about"
          class="bg-transparent border-2 border-white hover:bg-white/10 text-white font-bold py-3 px-8 rounded-full transition-colors duration-300"
        >
          {{ $t('hero.learnMore') }}
        </a>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Hero section component
</script>
