<template>
  <section id="stats" class="py-16 bg-dark text-white">
    <div class="container mx-auto px-4">
      <h2 class="text-4xl font-bold text-center mb-12">{{ $t('stats.title') }}</h2>

      <div v-if="loading" class="flex justify-center">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
        <p class="ml-4 text-xl">{{ $t('stats.loading') }}</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div class="bg-gray-800 p-6 rounded-lg text-center">
          <div class="text-4xl font-bold mb-2">{{ stats.players.toLocaleString() }}</div>
          <div class="text-xl">{{ $t('stats.players') }}</div>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg text-center">
          <div class="text-4xl font-bold mb-2">{{ stats.activeRegions.toLocaleString() }}</div>
          <div class="text-xl">{{ $t('stats.regions') }}</div>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg text-center">
          <div class="text-4xl font-bold mb-2">{{ stats.buildings.toLocaleString() }}</div>
          <div class="text-xl">{{ $t('stats.buildings') }}</div>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg text-center">
          <div class="text-4xl font-bold mb-2">{{ stats.battles.toLocaleString() }}</div>
          <div class="text-xl">{{ $t('stats.battles') }}</div>
        </div>
      </div>

      <div v-if="!loading" class="mt-12">
        <h3 class="text-2xl font-semibold text-center mb-6">{{ $t('stats.resources') }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="bg-gray-800 p-6 rounded-lg text-center">
            <div class="text-3xl font-bold mb-2">{{ stats.resources.wood.toLocaleString() }}</div>
            <div class="text-xl">Wood</div>
          </div>

          <div class="bg-gray-800 p-6 rounded-lg text-center">
            <div class="text-3xl font-bold mb-2">{{ stats.resources.stone.toLocaleString() }}</div>
            <div class="text-xl">Stone</div>
          </div>

          <div class="bg-gray-800 p-6 rounded-lg text-center">
            <div class="text-3xl font-bold mb-2">{{ stats.resources.food.toLocaleString() }}</div>
            <div class="text-xl">Food</div>
          </div>

          <div class="bg-gray-800 p-6 rounded-lg text-center">
            <div class="text-3xl font-bold mb-2">{{ stats.resources.gold.toLocaleString() }}</div>
            <div class="text-xl">Gold</div>
          </div>
        </div>
      </div>

      <div v-if="!loading" class="mt-12">
        <h3 class="text-2xl font-semibold text-center mb-6">{{ $t('stats.topPlayers') }}</h3>
        <div class="max-w-2xl mx-auto bg-gray-800 rounded-lg overflow-hidden">
          <table class="w-full">
            <thead>
              <tr class="bg-gray-700">
                <th class="py-3 px-4 text-left">Rank</th>
                <th class="py-3 px-4 text-left">Player</th>
                <th class="py-3 px-4 text-right">Score</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(player, index) in stats.topPlayers" :key="index" class="border-t border-gray-700">
                <td class="py-3 px-4">{{ index + 1 }}</td>
                <td class="py-3 px-4">{{ player.name }}</td>
                <td class="py-3 px-4 text-right">{{ player.score.toLocaleString() }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ApiService } from '../services/api.service';

const apiService = ApiService.getInstance();
const stats = ref<any>({
  players: 0,
  activeRegions: 0,
  buildings: 0,
  battles: 0,
  resources: {
    wood: 0,
    stone: 0,
    food: 0,
    gold: 0
  },
  topPlayers: []
});
const loading = ref(true);

onMounted(async () => {
  try {
    stats.value = await apiService.getGameStats();
  } catch (error) {
    console.error('Failed to load game statistics:', error);
  } finally {
    loading.value = false;
  }
});
</script>
