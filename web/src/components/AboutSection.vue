<template>
  <section id="about" class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <h2 class="text-4xl font-bold text-center mb-12">{{ $t('about.title') }}</h2>

      <div class="grid md:grid-cols-2 gap-12 items-center">
        <div>
          <h3 class="text-2xl font-semibold mb-4">{{ $t('about.exploreTitle') }}</h3>
          <p class="text-lg mb-6">
            {{ $t('about.exploreText') }}
          </p>

          <h3 class="text-2xl font-semibold mb-4">{{ $t('about.strategyTitle') }}</h3>
          <p class="text-lg mb-6">
            {{ $t('about.strategyText') }}
          </p>

          <h3 class="text-2xl font-semibold mb-4">{{ $t('about.competitionTitle') }} </h3>
          <p class="text-lg">
            {{ $t('about.competitionText') }}
          </p>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="rounded-lg overflow-hidden shadow-lg bg-gray-200 aspect-[4/3] flex items-center justify-center">
            <div class="text-gray-500 text-center p-4">Game Screenshot 1</div>
          </div>
          <div class="rounded-lg overflow-hidden shadow-lg bg-gray-200 aspect-[4/3] flex items-center justify-center">
            <div class="text-gray-500 text-center p-4">Game Screenshot 2</div>
          </div>
          <div class="rounded-lg overflow-hidden shadow-lg bg-gray-200 aspect-[4/3] flex items-center justify-center">
            <div class="text-gray-500 text-center p-4">Game Screenshot 3</div>
          </div>
          <div class="rounded-lg overflow-hidden shadow-lg bg-gray-200 aspect-[4/3] flex items-center justify-center">
            <div class="text-gray-500 text-center p-4">Game Screenshot 4</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// About section component
</script>
