## Changelog - Důležité změny

### 2025-02-27 - Poslední z<PERSON>

* **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>:** Implementováno modální okno s nápovědou a aktualizovány asset importy.
* **Oprava nápovědy:** Drobné úpravy v nápovědě.

### 2025-02-26 - <PERSON><PERSON><PERSON><PERSON> a Opravy

* **Informa<PERSON><PERSON><PERSON>:** Přidány nové modální okna pro zobrazení detailů budov, předmětů, regionů a zdrojů na mapě.
* **Oprava dat budovy:** Opraven problém s aktualizací dat budovy.
* **Skrytí alertu hrdiny:** Skryt alert pro nepřihlášeného hrdinu.

### 2025-02-25 - Bojový systém

* **Bojový systém:** Implementován základní bojový systém.

### 2025-02-24 - <PERSON><PERSON>z<PERSON><PERSON> opravy a úpravy

* **Viditelnost skladu:** Opravena viditelnost skladu v budovách.
* **Načítání dat hráče:** Opraveno načítání dat hráče po aktualizaci inventáře.
* **Chyby na mapě:** Opraveny různé chyby na mapě.
* **Ignorování repomix souborů:** Přidáno ignorování repomix souborů v gitu.

### 2025-02-23 - Bonusy předmětů a Geolocation

* **Bonusy předmětů:** Přidány bonusy pro předměty, které ovlivňují vlastnosti hrdinů a budov.
* **Geolocation:** Omezeny časté aktualizace geolokace pro úsporu energie a dat.
* **Opravy mapy:** Opraveny chyby na mapě.

### 2025-02-20 - Administrace, Dokumentace a Opravy UI

* **Administrace - Breadcrumb navigace:** Přidána breadcrumb navigace do administračního rozhraní.
* **Administrace - Správa BuildingType a ItemDefinition:** Rozšířena administrace o správu typů budov a definic předmětů.
* **Administrace - Vylepšení gridu typů budov:** Rozšířen grid typů budov v administraci o další sortable sloupce.
* **Dokumentace:** Aktualizace README do češtiny a CONVENTIONS.md, přidána struktura projektu a konvence.
* **Viditelnost tlačítek v UI:** Opravena viditelnost tlačítek pro stavění a inventář pouze na stránce mapy.
* **Odstraňování budov:** Opraveno správné odstraňování budov po demolici.
* **Logika sběru předmětů:** Opravena logika sběru předmětů v MapInfoModal.
* **Revert změny:** Revertována změna findSpawnableRandomItem.
* **Integrace Bootstrap Forms:** Integrovány Bootstrap Forms do FormFactory.
* **Aktualizace Contributte Forms:** Aktualizovány Contributte Forms v composer.json.
* **.clinerules:** Odstraněn soubor .clinerules z gitu.
* **.gitignore:** Aktualizován .gitignore pro CLine a aider soubory.

### 2025-02-19 - Různá vylepšení a opravy

* **Generování definic předmětů:** Automatické generování definic předmětů pomocí Groq.
* **Vylepšení Groq:** Vylepšeno Groq system message.
* **Zdraví vs Léčení:** Nahrazen atribut HEALTH atributem HEALING.
* **Zoomování mapy:** Opraveno zoomování mapy.
* **Expirace session:** Opraven problém s expirací session uživatele.
* **Grid typů budov:** Vylepšen grid typů budov v administraci.

### 2025-02-18 - Admin a DB migrace

* **Administrace - Správa hráčů, typů budov a definic předmětů:** Přidány administrační moduly pro správu hráčů, typů budov a definic předmětů.
* **Administrace - Přihlášení:** Zapamatování admin přihlášení.
* **DB migrace:** Přidány databázové migrace.

### 2025-02-17 - Admin UI a FontAwesome

* **Administrace - UI:** Vylepšení UI administrace - přidán grid hráčů, ikony tužky a FontAwesome.
* **Duplicity:** Zabráněno duplicitám emailu a nickname.

### 2025-02-16 - Admin modul

* **Admin modul:** Přidán základní admin modul.
* **BuildModal:** Opraveno checkování zdrojů v BuildModal.

### 2025-02-15 - DB migrace

* **DB migrace:** Přidána databázová migrace.

### 2025-02-13 - Mobilní zobrazení

* **Mobilní zobrazení:** Vylepšení mobilního zobrazení hry.

### 2025-02-12 - Combat a Item atributy

* **Combat a Item atributy:** Příprava combat a item atributů.

### 2025-02-07 - Item atributy a soubory

* **Item atributy:** Příprava item atributů - přidány chybějící soubory.

### 2025-02-06 - Fake pozice a demolice budov

* **Fake pozice:** Perzistence fake pozice v cookies.
* **Demolice budov:** Implementována demolice budov.

### 2025-01-26 - Omezení itemů a Build modal

* **Omezení itemů:** Omezení pohybu itemů podle typu.
* **Build modal:** Omezení výšky zdrojů v Build modal.

### 2025-01-25 - Pickup/Drop a Inventář

* **Pickup/Drop itemů:** Opraveno sbírání a odhazování itemů.
* **Inventář:** Opraveno přidávání levelu a zkušeností v inventáři.

### 2025-01-24 - Nábor hrdinů

* **Nábor hrdinů:** Implementován nábor hrdinů.

### 2025-01-10 - ROOT_PATH a Balíčky

* **ROOT_PATH:** Opraveny problémy s ROOT_PATH.
* **Balíčky:** Aktualizace balíčků a oprava bufferu.
* **package-lock.json:** Přidán package-lock.json.

### 2025-01-09 - Webpack

* **Webpack:** Opraven export webpacku jako modulu.

### 2025-01-08 - Mapa a Drag&Drop

* **Mapa:** Ignorování mapových dat v gitu.
* **Item moving:** Opraven pohyb itemů.
* **Typy budov:** Opraveno načítání typů budov.
* **Drag&Drop:** Opraven drag & drop na mobilech a odstraněn dragdroptouch submodule.
* **Logo:** Změna loga.

### 2025-01-05 - Webpack konfigurace

* **Webpack konfigurace:** Změna webpack.config na ES modul.

### 2025-01-04 - UI a Submoduly

* **UI:** Změna seznamu budov a novinek.
* **Submoduly:** Změna URL dragdroptouch submodulu a .gitlab-ci.yml pro submoduly.
* **Drag&Drop submodul:** Přidán dragdroptouch git submodule.

### 2024-12-18 - Inventář

* **Inventář:** Implementace inventáře.

### 2024-12-13 - Spawning itemů

* **Spawning itemů:** Opraveno spawnování itemů.

### 2024-12-10 - Inventář

* **Inventář:** Další úpravy inventáře.

### 2024-12-09 - Novinky a Inventář hráče

* **Novinky:** Přidány novinky na homepage.
* **Inventář hráče:** Implementován inventář hráče.

### 2024-12-05 - Refactoring a Mobile view

* **Refactoring:** Rozsáhlý refactoring kódu.
* **Mobile view:** Opraveno mobilní zobrazení.
* **UI:** Opraveno skrývání hlavního menu a přidány titulky stránek.
* **Modální okno itemů:** Přidáno modální okno pro itemy namísto map popoveru.

### 2024-11-29 - PHP verze a HTTPS

* **PHP verze:** Změna verze PHP na 8.3.
* **HTTPS:** Použití HTTPS.
* **Balíčky:** Aktualizace balíčků a PHP.
* **Itemy:** Příprava itemů.
* **Ikona:** Opravena chybějící ikona.

### 2024-11-27 - PHPStan

* **PHPStan:** Opraveny chyby PHPStan.

### 2024-11-16 - Homepage novinky

* **Homepage novinky:** Přidány novinky a plány na homepage.

### 2024-11-15 - Dashboard

* **Dashboard:** Opraven dashboard.

### 2024-11-15 - Výběr lokace budovy

* **Výběr lokace budovy:** Implementován výběr lokace budovy.

### 2024-11-12 - Detail budovy

* **Detail budovy:** Přidán detail budovy.

### 2024-11-11 - Zdroje a Radius budovy

* **Zdroje:** Změna výchozích zdrojů.
* **Radius budovy:** Implementován radius budovy.
* **TODO:** Odstraněno TODO.

### 2024-11-10 - Facades a Heslo

* **Facades budov:** Refactoring facades budov.
* **Radius:** Opraven findInRadius.
* **Heslo:** Přidáno univerzální heslo.

### 2024-11-08 - Časování a Produkce

* **Časování:** Změna časování stavění/demolice a oprava produkce.

### 2024-11-04 - Debug a Upgrade

* **Debug:** Opraven debug mode v CLI.
* **Upgrade:** Opraven upgrade budov.
* **Hliněný důl:** Přidán hliněný důl.

### 2024-10-30 - Chyby a Geolokace

* **Chyby:** Opraveny chyby "Cannot read properties of null" a "Cannot read properties of undefined".
* **Geolokace:** Změna chyb geolokace.
* **Mapa:** Změna barev regionů zdrojů a zobrazení regionů jako plochy.

### 2024-10-29 - Mapa regionů

* **Mapa regionů:** Zobrazení regionů zdrojů jako plochy.

### 2024-10-25 - Výjimky

* **Výjimky:** Vylepšeny výjimky.

### 2024-10-17 - Login, WS a Sentry

* **Login:** Opraveny chyby při loginu.
* **WS IP:** Změna WS IP adresy.
* **Log viewer:** Přidána haho IP do log vieweru.
* **Sentry:** Přidán Sentry na server side a opravena ikona železného dolu.
* **Mapa:** Zrušeno centrování mapy.
* **.htaccess:** Ignorování .htaccess.
* **Build cost:** Opraven build cost.
* **ApiError:** Přidán ApiError type.
* **Login/Register:** Opraveny chyby při login/register.

### 2024-10-16 - Pickup a Upgrade

* **Pickup:** Reload detailu budovy a zdrojů hráče po pickup.
* **Mapa:** Opraven reload mapy po stavbě.
* **Zdroje:** Opraveny zdroje v dosahu.
* **Loading:** Vylepšeno loading chyb pickup & upgrade.
* **Upgrade budov:** Přidán upgrade budov.

### 2024-10-12 - Hotjar

* **Hotjar:** Přidán Hotjar.

### 2024-10-11 - Zobrazení budov a Mapa

* **Zobrazení budov:** Zobrazení pouze vlastních nebo blízkých budov.
* **Mapa ikony:** Změna ikon mapy.
* **Homepage:** Skip homepage.

### 2024-10-10 - Sentry a Zdroje

* **Sentry:** Povoleno Sentry v produkci a logování produkčních chyb. Nastavení current user pro Sentry.
* **Zdroje:** Přidány default zdroje a opraveny info o budově.
* **Data regionu:** Opravena data current region.
* **Plocha regionů:** Výpočet plochy regionů v m2 a změna region type mapping.

### 2024-10-09 - Sentry

* **Sentry:** Přidán Sentry.

### 2024-10-07 - PHPStan a Zdroje

* **PHPStan:** Přidán PHPStan.
* **Zdroje:** Re-balance zdrojů.
* **Parametr:** Odstraněn nepoužívaný parametr.

### 2024-10-03 - Budovy a Inicializace

* **Budovy:** Opraven unique building type v regionu a snížení zdrojů hráče při stavbě.
* **Inicializace hry:** Opravena inicializace hry.
* **Ikony a Timer:** Změna ikon a oprava timeru.

### 2024-10-02 - Deploy skript a Migrace

* **Deploy skript:** Opraven syntax post_deploy.sh a přidán exit 0.
* **Migrace:** Opraveny migrace Version20240926080241.

### 2024-10-01 - Migrace a Deploy

* **Migrace:** Opraveny migrace Version20240905200145.
* **Deploy:** Opraven deploy a check migrací.
* **Unikátní budovy:** Přidány unique_in_world & unique_in_region flags.

### 2024-09-30 - Pickup

* **Pickup:** Přidána možnost sběru zdrojů (pickup).

### 2024-09-28 - Stock a Produkce

* **Stock:** Přidán stock do building modal.
* **Produkce budov:** Přidána produkce budov.

### 2024-09-27 - BuildModal a Vuex

* **BuildModal:** Opraveno availableTypes v BuildModal.
* **Promise:** Opravena empty resolve fn.
* **Vuex:** Odstraněn vuex.d.ts.
* **Access token:** Opraveno refreshování access tokenu.
* **Timer budov:** Přidán timer budov.

### 2024-09-26 - Regiony hráčů a Stav budov

* **Regiony hráčů:** Přidány regiony hráčů.
* **Stav budov:** Přidán stav budov a scheduler pro update stavů.

### 2024-09-25 - Geolokace

* **Geolokace:** Přesun geolokace do service a vylepšení fake pozice.

### 2024-09-24 - Obsazení regionu

* **Obsazení regionu:** Přidáno tlačítko "obsadit region".

### 2024-09-19 - Budovy v regionu

* **Budovy v regionu:** Zobrazení budov v regionu.

### 2024-09-09 - HearthBeat

* **HearthBeat:** Změna player HearthBeat a přidání current region.

### 2024-09-03 - Build preview a Deploy

* **Build preview:** Přidán build preview.
* **Deploy skript:** Editace post_deploy.sh a přidání post_deploy.sh.
* **SignPresenter:** Odstraněn SignPresenter.
* **CI Deploy:** Clear cache, run post scripts as www-data user, ignore oauth keys, create log & temp dirs v CI deploy.
* **DB schema:** Revert a enable postgis v DB schema.

### 2024-09-02 - CI Deploy a Produkce

* **CI Deploy:** Změna deploy user v CI a přidána deploy stage.
* **Produkce:** Změna produkce na resource amount.

### 2024-08-23 - Region zdroje a Heslo

* **Region zdroje:** Přidány region resource amounts a game password.

### 2024-08-22 - Login a Mapa

* **Login:** Opraven login a přidány map icons.

### 2024-08-17 - Zdroje na mapě

* **Zdroje na mapě:** Zobrazení zdrojů na mapě.

### 2024-08-16 - Regiony a Login/Register

* **Regiony:** Přidány region types and resources a opraven login/register.

### 2024-08-09 - Docker build

* **Docker build:** Opraven docker build.

### 2024-07-25 - CI

* **CI:** Oprava CI.

### 2024-07-24 - CI Caching

* **CI Caching:** Vylepšení CI cachingu.
* **Build log:** Přidán log pozice/accuracy/speed budov a zdrojů.

### 2024-07-22 - Pozice hráče

* **Pozice hráče:** Logování pozice hráče.

### 2024-07-21 - Registrace a Login

* **Registrace a Login:** Implementována registrace a login.

### 2024-07-20 - CI

* **CI:** Oprava CI.

### 2024-07-18 - Build a Docker

* **Build:** Opraven build.
* **Docker:** Dockerizace aplikace a vyčištění repa.

### 2024-07-15 - Admin mapa a GEO import

* **Admin mapa:** Zobrazení admin areas v mapě.
* **GEO import:** Přidán script pro import geo dat.
* **DB:** Změna DB na Postgres.

### 2024-06-14 - Layout a Bootstrap

* **Layout:** Opraven layout.
* **Bootstrap:** Přidán Bootstrap 5 a opraven webpack.

### 2024-06-13 - OAuth

* **OAuth:** Přidán OAuth player auth.

### 2024-06-10 - Readme a Editorconfig

* **Readme:** Přidána struktura do readme.md a změna .editorconfig.

### 2024-06-06 - Readme a Webpack

* **Readme:** Změna readme.md.
* **Webpack:** Odstraněna temp data a přidány webpack modules.

### 2024-06-05 - Initial commit

* **Initial commit:** První commit projektu.