# Logger.gd
extends Node

enum LogLevel {
	DEBUG,    # Detailed information, typically of interest only when diagnosing problems.
	INFO,     # Confirmation that things are working as expected.
	WARNING,  # An indication that something unexpected happened, or indicative of some problem in the near future.
	ERROR,    # Due to a more serious problem, the software has not been able to perform some function.
	CRITICAL  # A serious error, indicating that the program itself may be unable to continue running.
}
const LOG_LEVEL_STRINGS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

var _logging_enabled: bool = true # Global switch
var _min_log_level: LogLevel = LogLevel.INFO # Default
var _source_settings: Dictionary = {} # Stores { "SourceName": is_enabled }
var _default_source_enabled: bool = true
var _filtered_sources: Array = [] # Stores sources to be filtered out
var _timers: Dictionary = {} # Stores timers for each source

func _ready():
	_load_config()

func _load_config():
	# Load global enabled flag
	_logging_enabled = ProjectSettings.get_setting("logging/enabled", true)

	# Load minimum level
	var level_str = ProjectSettings.get_setting("logging/min_level", "DEBUG").to_upper()
	_min_log_level = _string_to_log_level(level_str)

	# Load default source enabled state
	_default_source_enabled = ProjectSettings.get_setting("logging/default_source_enabled", true)

	# Load specific source settings
	_source_settings.clear()
	var source_keys = ProjectSettings.get_setting("logging/sources", {})
	for source_name in source_keys:
		var enabled = ProjectSettings.get_setting("logging/sources/" + source_name + "/enabled", _default_source_enabled)
		_source_settings[source_name.to_upper()] = enabled

	# Log initialization only if logging is enabled overall
	if _logging_enabled:
		print("Logger initialized. Enabled: %s, Min Level: %s, Default Source Enabled: %s, Sources: %s" % [_logging_enabled, LOG_LEVEL_STRINGS[_min_log_level], _default_source_enabled, _source_settings])
	else:
		print("Logger initialized. Logging globally DISABLED.")


func _string_to_log_level(level_str: String) -> LogLevel:
	match level_str:
		"DEBUG": return LogLevel.DEBUG
		"INFO": return LogLevel.INFO
		"WARNING": return LogLevel.WARNING
		"ERROR": return LogLevel.ERROR
		"CRITICAL": return LogLevel.CRITICAL
		_:
			printerr("Invalid log level '%s' in Project Settings. Defaulting to INFO." % level_str)
			return LogLevel.INFO


# Internal function to handle level/source checks and formatting.
func _log(level: LogLevel, source: String, message: String):
	# Check global switch first
	if not _logging_enabled:
		return

	if level < _min_log_level:
		return # Skip logging if below minimum level

	var source_upper = source.to_upper()
	var is_source_enabled = _source_settings.get(source_upper, _default_source_enabled)

	if not is_source_enabled:
		return # Skip logging if source is disabled

	if source_upper in _filtered_sources:
		return # Skip logging if source is filtered out

	var timestamp = Time.get_datetime_string_from_system(false, true) # YYYY-MM-DD HH:MM:SS
	var level_str = LOG_LEVEL_STRINGS[level]
	var output = "[%s] [%s] [%s] %s" % [timestamp, level_str, source, message]

	if level >= LogLevel.ERROR:
		printerr(output) # Use printerr for ERROR and CRITICAL
	else:
		print(output)


# Public Logging Methods
func debug(source: String, message: String):
	_log(LogLevel.DEBUG, source, message)

func info(source: String, message: String):
	_log(LogLevel.INFO, source, message)

func warning(source: String, message: String):
	_log(LogLevel.WARNING, source, message)

func error(source: String, message: String):
	_log(LogLevel.ERROR, source, message)

func critical(source: String, message: String):
	_log(LogLevel.CRITICAL, source, message)


# Method to check if a specific source is enabled
func is_enabled(source: String) -> bool:
	return _source_settings.get(source.to_upper(), _default_source_enabled)


# Timer methods
func start_timer(source: String) -> void:
	if not _timers.has(source):
		_timers[source] = Time.get_ticks_usec()

func get_timer(source: String) -> float:
	if _timers.has(source):
		return Time.get_ticks_usec() - _timers[source]
	return 0.0

func stop_timer(source: String) -> float:
	if _timers.has(source):
		var start_time = _timers[source]
		var end_time = Time.get_ticks_usec()
		var duration = end_time - start_time
		_timers.erase(source)
		return duration
	return 0.0
