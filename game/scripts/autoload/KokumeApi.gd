extends Node

# --- Signals ---
signal logged_in(data: Dictionary)
signal login_failed(response_code: int, error_data: Dictionary)

signal registration_completed(data: Dictionary)
signal registration_failed(response_code: int, error_data: Dictionary)

signal password_reset_requested(data: Dictionary)
signal password_reset_failed(response_code: int, error_data: Dictionary)

signal player_info_loaded(data: Dictionary)
signal player_info_failed(response_code: int, error_data: Dictionary)

signal player_heartbeat_sent(data: Dictionary)
signal player_heartbeat_failed(response_code: int, error_data: Dictionary)

signal region_occupied(data: Dictionary)
signal region_occupation_failed(response_code: int, error_data: Dictionary)

signal region_loaded(data: Dictionary)
signal region_load_failed(response_code: int, error_data: Dictionary)

signal resource_loaded(data: Dictionary)
signal resource_load_failed(response_code: int, error_data: Dictionary)

signal building_loaded(data: Dictionary)
signal building_load_failed(response_code: int, error_data: Dictionary)

signal building_resources_picked_up(data: Dictionary)
signal building_resource_pickup_failed(response_code: int, error_data: Dictionary)

signal building_upgraded(data: Dictionary)
signal building_upgrade_failed(response_code: int, error_data: Dictionary)

signal building_demolished(data: Dictionary)
signal building_demolish_failed(response_code: int, error_data: Dictionary)

signal building_stolen(data: Dictionary)
signal building_steal_failed(response_code: int, error_data: Dictionary)

signal hero_hired(data: Dictionary)
signal hero_hire_failed(response_code: int, error_data: Dictionary)

signal unit_production_started(data: Dictionary)
signal unit_production_start_failed(response_code: int, error_data: Dictionary)

signal unit_production_canceled(data: Dictionary)
signal unit_production_cancel_failed(response_code: int, error_data: Dictionary)

signal item_picked_up(data: Dictionary)
signal item_pickup_failed(response_code: int, error_data: Dictionary)

signal map_data_loaded(data: Dictionary)
signal map_data_failed(response_code: int, error_data: Dictionary)

signal api_error(endpoint: String, response_code: int, error_data: Dictionary)

signal building_types_loaded(data: Dictionary)
signal building_types_failed(response_code: int, error_data: Dictionary)

signal building_created(data: Dictionary)
signal building_creation_failed(response_code: int, error_data: Dictionary)

# --- Constants ---
const API_URL_DEV = "http://kokume.loc"
const API_URL = "https://kokume.eu"
const API_BASE_V1 = "/api/v1"
const API_BASE_OAUTH = "/api"
const API_CLIENT_ID = "godot"
const API_CLIENT_SECRET = "ae6r854dbfg12bva6a85re68g" # TODO: Secure this better

# Endpoints (Relative paths)
const EP_TOKEN = "/oauth2/access_token"
const EP_PLAYER = "/player"
const EP_PLAYER_REGISTRATION = "/player-registration"
const EP_PASSWORD_RESET = "/password-reset" # This endpoint may need to be updated when implemented in backend
const EP_REGION = "/region"
const EP_RESOURCE = "/resource"
const EP_OCCUPY_REGION = "/occupy-region"
const EP_BUILDING = "/building"
const EP_BUILDING_DETAIL = "/building/%s"
const EP_BUILDING_TYPES = "/building-types"
const EP_BUILDING_PICKUP_RESOURCES_FORMAT = EP_BUILDING_DETAIL + "/pick-up-resources"
const EP_BUILDING_UPGRADE_FORMAT = EP_BUILDING_DETAIL + "/upgrade"
const EP_BUILDING_DEMOLISH_FORMAT = EP_BUILDING_DETAIL + "/demolish"
const EP_BUILDING_STEAL_FORMAT = EP_BUILDING_DETAIL + "/steal"
const EP_HERO_HIRE = "/hero/hire"
const EP_BUILDING_PRODUCTION_START_FORMAT = EP_BUILDING_DETAIL + "/production/start"
const EP_BUILDING_PRODUCTION_CANCEL_FORMAT = EP_BUILDING_DETAIL + "/production/%s/cancel"
const EP_ITEM_PICKUP_FORMAT = "/hero/item/%s/pick-up"
const EP_MAP_DATA = "/map/geojson"

# Response Codes
const RES_CODE_OK = 200
const RES_CODE_CREATED = 201
const RES_CODE_NO_CONTENT = 204
const RES_CODE_BAD_REQUEST = 400
const RES_CODE_UNAUTHORIZED = 401
const RES_CODE_FORBIDDEN = 403
const RES_CODE_NOT_FOUND = 404

# --- Server settings ---
const SERVER_TIMEZONE = 2.0

# --- State ---
var authorized: bool = false
var access_token: String = ""
var refresh_token: String = ""

# --- Public API Methods ---

func authorize(nickname: String, password: String) -> void:
	var body = {
		"grant_type": "password",
		"client_id": API_CLIENT_ID,
		"client_secret": API_CLIENT_SECRET,
		"scope": "game",
		"username": nickname,
		"password": password,
	}

	var error = _make_request(EP_TOKEN, _authorize_completed).request(
		_endpoint(EP_TOKEN, API_BASE_OAUTH), # Specify OAuth base
		["Content-Type: application/x-www-form-urlencoded"],
		HTTPClient.METHOD_POST,
		_get_form_data(body)
	)

	if error != OK:
		Logger.error("API", "An error occurred starting the authorization HTTP request.")
		api_error.emit(EP_TOKEN, -1, {"error": "request_failed", "message": "Failed to start request"})


func register(email: String, nickname: String, password: String, game_password: String) -> void:
	var body = {
		"email": email,
		"nickname": nickname,
		"password": password,
		"game_password": game_password
	}

	var error = _make_request(EP_PLAYER_REGISTRATION, _registration_completed).request(
		_endpoint(EP_PLAYER_REGISTRATION), # Uses default API_BASE_V1
		["Content-Type: application/json"],
		HTTPClient.METHOD_PUT,
		JSON.stringify(body)
	)

	if error != OK:
		Logger.error("API", "An error occurred starting the registration HTTP request.")
		api_error.emit(EP_PLAYER_REGISTRATION, -1, {"error": "request_failed", "message": "Failed to start request"})


func request_password_reset(email: String) -> void:
	# This is a placeholder for future implementation
	# The backend doesn't currently support password reset
	var body = {
		"email": email
	}

	# Simulate a response for now
	Logger.warning("API", "Password reset not implemented in backend yet.")
	password_reset_requested.emit({"message": "Password reset functionality is not yet implemented. Please contact support."})

	# When backend supports this feature, uncomment and adjust the following code:
	# var error = _make_request(EP_PASSWORD_RESET, _password_reset_completed).request(
	#	_endpoint(EP_PASSWORD_RESET),
	#	["Content-Type: application/json"],
	#	HTTPClient.METHOD_POST,
	#	JSON.stringify(body)
	# )
	#
	# if error != OK:
	#	Logger.error("API", "An error occurred starting the password reset HTTP request.")
	#	api_error.emit(EP_PASSWORD_RESET, -1, {"error": "request_failed", "message": "Failed to start request"})


func player_info() -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to fetch player info.")
		return
	_request_GET(EP_PLAYER, _player_info_completed)


# Replaces get_player_status
func update_player_status(position_data: Dictionary) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to send player heartbeat.")
		return
	# Expected position_data format: {"latLng": {"lat": float, "lng": float}, "accuracy": float, "speed": float}
	_request_POST(EP_PLAYER, position_data, _player_heartbeat_completed)


func occupy_region() -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to occupy region.")
		return
	# API call is implicit, no body needed
	_request_POST(EP_OCCUPY_REGION, {}, _region_occupied_completed)


func get_region(id: int) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized get region.")
		return
	_request_GET("%s/%s" % [EP_REGION, id], _get_region_completed)


func get_resource(id: int) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized get resource.")
		return
	_request_GET("%s/%s" % [EP_RESOURCE, id], _get_resource_completed)


func get_building(building_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized get building.")
		return
	_request_GET(EP_BUILDING_DETAIL % [building_id], _get_building_completed)


func get_building_types() -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to get building types.")
		return
	_request_GET(EP_BUILDING_TYPES, _get_building_types_completed)


func create_building(building_data: Dictionary) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to create building.")
		return
	_request_PUT(EP_BUILDING, building_data, _building_created_completed)


func pickup_building_resources(building_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to pick up building resources.")
		return
	_request_GET(EP_BUILDING_PICKUP_RESOURCES_FORMAT % [building_id], _building_resources_pickup_completed)


func upgrade_building(building_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to upgrade building.")
		return
	_request_GET(EP_BUILDING_UPGRADE_FORMAT % [building_id], _building_upgrade_completed)


func demolish_building(building_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to demolish building.")
		return
	_request_GET(EP_BUILDING_DEMOLISH_FORMAT % [building_id], _building_demolish_completed)


func steal_building(building_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to steal building.")
		return
	_request_GET(EP_BUILDING_STEAL_FORMAT % [building_id], _building_steal_completed)


func hire_hero(hero_data: Dictionary) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to hire hero.")
		return
	_request_POST(EP_HERO_HIRE, hero_data, _hero_hire_completed)


func start_unit_production(building_id: String, unit_definition_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to start unit production.")
		return
	var data = {"unit_definition_id": unit_definition_id}
	_request_POST(EP_BUILDING_PRODUCTION_START_FORMAT % [building_id], data, _unit_production_start_completed)


func cancel_unit_production(building_id: String, production_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to cancel unit production.")
		return
	_request_GET(EP_BUILDING_PRODUCTION_CANCEL_FORMAT % [building_id, production_id], _unit_production_cancel_completed)


func pickup_item(item_id: String) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to pick up item.")
		return
	_request_POST(EP_ITEM_PICKUP_FORMAT % [item_id], {}, _item_pickup_completed)


func get_map_data(bounding_box: Dictionary) -> void:
	if not authorized:
		Logger.warning("API", "Not authorized to fetch map data.")
		return

	# Validate the bounding box dictionary
	if not bounding_box.has_all(["min_lon", "min_lat", "max_lon", "max_lat"]):
		Logger.error("API", "Invalid bounding box dictionary provided to get_map_data: %s" % bounding_box)
		api_error.emit(EP_MAP_DATA, -1, {"error": "invalid_bbox_input", "message": "Bounding box dictionary is missing required keys"})
		return

	# Construct the bbox query string: minLon,minLat,maxLon,maxLat
	var bbox_string = "%f,%f,%f,%f" % [
		bounding_box.min_lon,
		bounding_box.min_lat,
		bounding_box.max_lon,
		bounding_box.max_lat
	]

	var path_with_query = "%s?bbox=%s" % [EP_MAP_DATA, bbox_string]
	# Zoom parameter removed, simplification is now server-side based on bbox
	_request_GET(path_with_query, _map_data_completed)


func logout() -> void:
	Logger.info("API", "Logging out user")
	authorized = false
	access_token = ""
	refresh_token = ""

# --- Request Handling ---

func _make_request(request_key: String, completed_callback: Callable) -> HTTPRequest:
	# Sanitize request key for node name (remove query params for uniqueness)
	var base_key = request_key.split("?")[0]
	var request_node_name = base_key.replace("/", "_").lstrip("_") + str(completed_callback.hash()) + "_req"

	var request: HTTPRequest
	if has_node(request_node_name):
		request = get_node(request_node_name)
		# Ensure previous connection is removed if callback changes
		if request.is_connected("request_completed", completed_callback):
			pass # Already connected or re-connecting below
		else:
			# Disconnect any old callbacks if they exist
			for connection in request.get_signal_connection_list("request_completed"):
				if connection.has("callable"):
					request.request_completed.disconnect(connection["callable"])
	else:
		request = HTTPRequest.new()
		request.name = request_node_name
		add_child(request)
		# Add to group for easier cleanup on completion/error
		request.add_to_group("http_requests")

	# Connect the potentially new callback
	if not request.is_connected("request_completed", completed_callback):
		request.request_completed.connect(completed_callback)
	
	return request


func _request_GET(apiPathWithQuery: String, completed_callback: Callable) -> void:
	var request = _make_request(apiPathWithQuery, completed_callback)
	var error = request.request(
		_endpoint(apiPathWithQuery),
		_secured_headers(),
		HTTPClient.METHOD_GET
	)

	if error != OK:
		Logger.error("API", "An error(%s) occurred starting the GET request for %s." % [error, apiPathWithQuery])
		var base_path = apiPathWithQuery.split("?")[0]
		api_error.emit(base_path, -1, {"error": "request_failed", "message": "Failed to start GET request"})
		request.queue_free()


func _request_POST(apiPath: String, body: Dictionary, completed_callback: Callable) -> void:
	var request = _make_request(apiPath, completed_callback)
	var body_string = ""
	if not body.is_empty():
		body_string = JSON.stringify(body)

	var error = request.request(
		_endpoint(apiPath),
		_secured_headers(["Content-Type: application/json"]),
		HTTPClient.METHOD_POST,
		body_string
	)

	if error != OK:
		Logger.error("API", "An error(%s) occurred starting the POST request for %s." % [error, apiPath])
		api_error.emit(apiPath, -1, {"error": "request_failed", "message": "Failed to start POST request"})
		request.queue_free()


func _request_PUT(apiPath: String, body: Dictionary, completed_callback: Callable) -> void:
	var request = _make_request(apiPath, completed_callback)
	var body_string = ""
	if not body.is_empty():
		body_string = JSON.stringify(body)

	var error = request.request(
		_endpoint(apiPath),
		_secured_headers(["Content-Type: application/json"]),
		HTTPClient.METHOD_PUT,
		body_string
	)

	if error != OK:
		Logger.error("API", "An error(%s) occurred starting the PUT request for %s." % [error, apiPath])
		api_error.emit(apiPath, -1, {"error": "request_failed", "message": "Failed to start PUT request"})
		request.queue_free()


# --- Response Callbacks ---

func _authorize_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_TOKEN.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Authorization failed. Code: %d, Body: %s" % [response_code, data])
		authorized = false
		access_token = ""
		refresh_token = ""
		login_failed.emit(response_code, data)
		api_error.emit(EP_TOKEN, response_code, data)
		if request_node: request_node.queue_free() 
		return

	access_token = data.get("access_token", "")
	refresh_token = data.get("refresh_token", "")
	authorized = true
	Logger.info("API", "Authorization successful.")
	logged_in.emit(data)
	if request_node: request_node.queue_free()


func _registration_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_PLAYER_REGISTRATION.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)

	# Registration endpoint returns 201 Created on success
	if response_code != RES_CODE_CREATED:
		Logger.error("API", "Registration failed. Code: %d, Body: %s" % [response_code, data])
		registration_failed.emit(response_code, data)
		api_error.emit(EP_PLAYER_REGISTRATION, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Registration successful.")
	registration_completed.emit(data)
	if request_node: request_node.queue_free()


func _password_reset_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_PASSWORD_RESET.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)

	# Assuming password reset endpoint returns 200 OK on success
	if response_code != RES_CODE_OK:
		Logger.error("API", "Password reset request failed. Code: %d, Body: %s" % [response_code, data])
		password_reset_failed.emit(response_code, data)
		api_error.emit(EP_PASSWORD_RESET, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Password reset request successful.")
	password_reset_requested.emit(data)
	if request_node: request_node.queue_free()


func _player_info_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_PLAYER.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get player info. Code: %d, Body: %s" % [response_code, data])
		player_info_failed.emit(response_code, data)
		api_error.emit(EP_PLAYER, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Player info loaded.")
	player_info_loaded.emit(data)
	if request_node: request_node.queue_free()


# Renamed from _player_status_completed
func _player_heartbeat_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_PLAYER.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to send player heartbeat. Code: %d, Body: %s" % [response_code, data])
		player_heartbeat_failed.emit(response_code, data)
		api_error.emit(EP_PLAYER, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Player heartbeat sent.")
	player_heartbeat_sent.emit(data)
	player_info_loaded.emit(data.player)
	# Example: if GameState and data.has("resources"): GameState.update_player_resources(data.resources)
	if request_node: request_node.queue_free()


# Renamed from _capture_territory_completed
func _region_occupied_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_OCCUPY_REGION.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	# Expecting 200 OK based on backend controller return type
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to occupy region. Code: %d, Body: %s" % [response_code, data])
		region_occupation_failed.emit(response_code, data)
		api_error.emit(EP_OCCUPY_REGION, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Region occupied successfully.")
	region_occupied.emit(data)
	# Example: if GameState and data.has("region_id"): GameState.update_occupied_region(data)
	if request_node: request_node.queue_free()


# Renamed from _capture_territory_completed
func _get_region_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_REGION.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	# Expecting 200 OK based on backend controller return type
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get region. Code: %d, Body: %s" % [response_code, data])
		region_load_failed.emit(response_code, data)
		api_error.emit(EP_REGION, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Region get successfully.")
	region_loaded.emit(data)
	if request_node: request_node.queue_free()


# Renamed from _capture_territory_completed
func _get_resource_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_RESOURCE.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	# Expecting 200 OK based on backend controller return type
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get resource. Code: %d, Body: %s" % [response_code, data])
		resource_load_failed.emit(response_code, data)
		api_error.emit(EP_RESOURCE, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Resource get successfully.")
	resource_loaded.emit(data)
	if request_node: request_node.queue_free()


# Renamed from _capture_territory_completed
func _get_building_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_BUILDING.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	# Expecting 200 OK based on backend controller return type
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get building. Code: %d, Body: %s" % [response_code, data])
		building_load_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building get successfully.")
	building_loaded.emit(data)
	if request_node: request_node.queue_free()


# Renamed from _gather_resource_completed
func _building_resources_pickup_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	# This assumes the callback is only connected once per unique path format instance
	# A more robust solution might pass the request node itself to the callback
	var request_node_name_guess = EP_BUILDING_PICKUP_RESOURCES_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req" # Simplified guess
	var request_node = get_node_or_null(request_node_name_guess) # May not find the correct node if multiple requests use this format

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to pick up building resources. Code: %d, Body: %s" % [response_code, data])
		building_resource_pickup_failed.emit(response_code, data)
		# Use a generic key for error reporting as the exact path varies
		api_error.emit(EP_BUILDING_PICKUP_RESOURCES_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building resources picked up successfully.")
	building_resources_picked_up.emit(data)
	# Example: if GameState and data.has("resources"): GameState.update_player_resources(data.resources)
	if request_node: request_node.queue_free()


# Building upgrade callback
func _building_upgrade_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	# This assumes the callback is only connected once per unique path format instance
	var request_node_name_guess = EP_BUILDING_UPGRADE_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req" # Simplified guess
	var request_node = get_node_or_null(request_node_name_guess) # May not find the correct node if multiple requests use this format

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to upgrade building. Code: %d, Body: %s" % [response_code, data])
		building_upgrade_failed.emit(response_code, data)
		# Use a generic key for error reporting as the exact path varies
		api_error.emit(EP_BUILDING_UPGRADE_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building upgraded successfully.")
	building_upgraded.emit(data)
	# Update player resources if needed
	# if GameState and data.has("resources"): GameState.update_player_resources(data.resources)
	if request_node: request_node.queue_free()


# Building demolish callback
func _building_demolish_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	var request_node_name_guess = EP_BUILDING_DEMOLISH_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req"
	var request_node = get_node_or_null(request_node_name_guess)

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to demolish building. Code: %d, Body: %s" % [response_code, data])
		building_demolish_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING_DEMOLISH_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building demolished successfully.")
	building_demolished.emit(data)
	if request_node: request_node.queue_free()


# Building steal callback
func _building_steal_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	var request_node_name_guess = EP_BUILDING_STEAL_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req"
	var request_node = get_node_or_null(request_node_name_guess)

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to steal building. Code: %d, Body: %s" % [response_code, data])
		building_steal_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING_STEAL_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building stolen successfully.")
	building_stolen.emit(data)
	if request_node: request_node.queue_free()


# Hero hire callback
func _hero_hire_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_HERO_HIRE.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to hire hero. Code: %d, Body: %s" % [response_code, data])
		hero_hire_failed.emit(response_code, data)
		api_error.emit(EP_HERO_HIRE, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Hero hired successfully.")
	hero_hired.emit(data)
	if request_node: request_node.queue_free()


# Unit production start callback
func _unit_production_start_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	var request_node_name_guess = EP_BUILDING_PRODUCTION_START_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req"
	var request_node = get_node_or_null(request_node_name_guess)

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to start unit production. Code: %d, Body: %s" % [response_code, data])
		unit_production_start_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING_PRODUCTION_START_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Unit production started successfully.")
	unit_production_started.emit(data)
	if request_node: request_node.queue_free()


# Unit production cancel callback
func _unit_production_cancel_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	var request_node_name_guess = EP_BUILDING_PRODUCTION_CANCEL_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req"
	var request_node = get_node_or_null(request_node_name_guess)

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to cancel unit production. Code: %d, Body: %s" % [response_code, data])
		unit_production_cancel_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING_PRODUCTION_CANCEL_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Unit production canceled successfully.")
	unit_production_canceled.emit(data)
	if request_node: request_node.queue_free()


# Building types callback
func _get_building_types_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_BUILDING_TYPES.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get building types. Code: %d, Body: %s" % [response_code, data])
		building_types_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING_TYPES, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building types loaded successfully.")
	building_types_loaded.emit(data.value)
	if request_node: request_node.queue_free()


# Building creation callback
func _building_created_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_BUILDING.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_CREATED and response_code != RES_CODE_OK:
		Logger.error("API", "Failed to create building. Code: %d, Body: %s" % [response_code, data])
		building_creation_failed.emit(response_code, data)
		api_error.emit(EP_BUILDING, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Building created successfully.")
	building_created.emit(data)
	if request_node: request_node.queue_free()


# Item pickup callback
func _item_pickup_completed(_result, response_code, _headers, body):
	# Need to reconstruct the request key used in _make_request to find the node
	var request_node_name_guess = EP_ITEM_PICKUP_FORMAT.split("/%s/")[0].replace("/", "_").lstrip("_") + "_req"
	var request_node = get_node_or_null(request_node_name_guess)

	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to pick up item. Code: %d, Body: %s" % [response_code, data])
		item_pickup_failed.emit(response_code, data)
		api_error.emit(EP_ITEM_PICKUP_FORMAT, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Item picked up successfully.")
	item_picked_up.emit(data)
	# Update inventory if needed
	# if GameState and data.has("inventory"): GameState.update_inventory(data.inventory)
	if request_node: request_node.queue_free()


# Renamed from _nearby_territories_completed
func _map_data_completed(_result, response_code, _headers, body):
	var request_node = get_node_or_null(EP_MAP_DATA.replace("/", "_").lstrip("_") + "_req")
	var data = _parse_body(body)
	if response_code != RES_CODE_OK:
		Logger.error("API", "Failed to get map data. Code: %d, Body: %s" % [response_code, data])
		map_data_failed.emit(response_code, data)
		api_error.emit(EP_MAP_DATA, response_code, data)
		if request_node: request_node.queue_free()
		return

	Logger.info("API", "Map data loaded.")
	map_data_loaded.emit(data) # Emit single signal with all map data

	# Update GameState with the new data structure
	if GameState:
		# Update territories
		if data.has("territories"):
			GameState.update_nearby_territories(data.territories)

		# Update resources
		if data.has("resources"):
			GameState.update_nearby_resources(data.resources)

		# Update buildings and items if GameState has methods for them
		# This can be extended as needed

	if request_node: request_node.queue_free()


# Removed _nearby_resources_completed callback

# --- Helpers ---

# Constructs the full URL endpoint
func _endpoint(path: String, base: String = API_BASE_V1) -> String:
	if not path.begins_with("/"):
		path = "/" + path
	# Ensure no double slashes between base URL and base path
	var base_url = API_URL.rstrip("/")

	# Use development URL if running in editor
	if OS.get_name() == "Linux":
		base_url = API_URL_DEV.rstrip("/")

	var base_path = base.rstrip("/")
	return "%s%s%s" % [base_url, base_path, path]


func _secured_headers(headers: Array[String] = []) -> Array[String]:
	if authorized and not access_token.is_empty():
		headers.append("Authorization: Bearer %s" % [access_token])
	elif not authorized:
		# Allow unsecured requests (like login) to proceed without error spam
		pass
		# printerr("Attempted to make secured request without authorization.")
	headers.append("Accept: application/json")
	return headers


# Kept for authorize function (assuming form-urlencoded is accepted)
func _get_form_data(data: Dictionary) -> String:
	var parts: Array[String] = []

	for key in data.keys():
		var value: String = str(key).uri_encode() + "=" + str(data[key]).uri_encode()
		parts.append(value)

	return "&".join(parts)


func _parse_body(body: PackedByteArray) -> Dictionary:
	var text = body.get_string_from_utf8()
	if text.is_empty():
		return {}
	var json = JSON.parse_string(text)
	if typeof(json) == TYPE_DICTIONARY:
		return json
	elif json == null:
		Logger.error("API", "Failed to parse JSON response: %s" % text)
		return {"error": "invalid_json", "raw_body": text}
	else:
		# Handle cases where response might be a JSON array or primitive
		return {"value": json}


func is_authorized() -> bool:
	return authorized
