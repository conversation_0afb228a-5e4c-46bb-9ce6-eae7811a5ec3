extends Node

# Signal emitted when the currently displayed info modal changes
# Passes the data dictionary of the selected object, or null if no modal is open
signal selected_object_changed(object_data: Dictionary)

# Paths to main scenes
const LOGIN_SCENE = "res://scenes/ui/Login.tscn"
const MAIN_GAME_SCENE = "res://scenes/main/MainGame.tscn" # Assuming game.tscn is the main game view

# Preload modal scenes
@export var building_info_modal_scene: PackedScene = preload("res://scenes/ui/BuildingInfoModal.tscn")
@export var inventory_modal_scene: PackedScene = preload("res://scenes/ui/InventoryModal.tscn")
@export var help_modal_scene: PackedScene = preload("res://scenes/ui/HelpModal.tscn")
@export var map_info_modal_scene: PackedScene = preload("res://scenes/ui/MapInfoModal.tscn")
@export var region_info_modal_scene: PackedScene = preload("res://scenes/ui/RegionInfoModal.tscn")
@export var resource_info_modal_scene: PackedScene = preload("res://scenes/ui/ResourceInfoModal.tscn")
@export var item_info_modal_scene: PackedScene = preload("res://scenes/ui/ItemInfoModal.tscn")
@export var build_modal_scene: PackedScene = preload("res://scenes/ui/BuildModal.tscn")
@export var hero_modal_scene: PackedScene = preload("res://scenes/ui/HeroModal.tscn")
@export var dashboard_modal_scene: PackedScene = preload("res://scenes/ui/DashboardModal.tscn")

# Reference to the currently active modal (if any)
var current_modal: Control = null

func _ready():
	# Connect signals if needed
	pass


func switch_main_scene(scene_path: String) -> void:
	if get_tree().current_scene and get_tree().current_scene.scene_file_path == scene_path:
		print("Already in scene: ", scene_path)
		Logger.warning("UIManager", "Already in scene: %s" % scene_path)
		return

	Logger.info("UIManager", "Switching main scene to: %s" % scene_path)

	# Close any open modal before switching main scene
	_close_current_modal()

	var result = get_tree().change_scene_to_file(scene_path)
	if result != OK:
		Logger.error("UIManager", "Failed to switch scene to: %s Error code: %s" % [scene_path, result])


func show_login_screen() -> void:
	switch_main_scene(LOGIN_SCENE)


func show_main_game_screen() -> void:
	switch_main_scene(MAIN_GAME_SCENE)


# --- Modal Management ---

func _show_modal(modal_scene: PackedScene, data = null):
	if not modal_scene:
		Logger.error("UIManager", "Invalid modal scene provided.")
		return

	# Close any existing modal first
	_close_current_modal()

	var current_scene = get_tree().current_scene
	if not current_scene:
		Logger.error("UIManager", "Cannot show modal, no current scene found.")
		return

	var modal_instance = modal_scene.instantiate()
	if not modal_instance is Control:
		Logger.error("UIManager", "Instantiated modal is not a Control node.")
		modal_instance.queue_free() # Clean up invalid instance
		return

	current_modal = modal_instance # Store reference
	current_scene.add_child(current_modal) # Add to the current main scene
	current_modal.closed.connect(_on_modal_closed.bind(current_modal))

	# Call the specific show function on the modal's script
	if data != null and current_modal.has_method("open_and_fill_modal"):
		current_modal.call("open_and_fill_modal", data)
	elif current_modal.has_method("open_modal"): # Fallback for simple modals
		current_modal.open_modal()
	else:
		Logger.error("UIManager", "UIManager: Modal script for %s does not have a 'open_and_fill_modal' or 'open_modal' function." % modal_instance.name)
		# Still show the modal visually if it exists
		current_modal.visible = true

	# Emit signal that selection changed
	selected_object_changed.emit(data)


func _on_modal_closed(modal_instance: Control):
	if modal_instance == current_modal:
		current_modal = null # Clear reference
		selected_object_changed.emit(null) # Emit signal that selection is cleared
	# The modal instance will free itself if queue_free was called in its close_modal
	# If not, we might need to queue_free it here:
	if is_instance_valid(modal_instance):
		modal_instance.queue_free()


func _close_current_modal():
	if is_instance_valid(current_modal):
		current_modal.close_modal() # Trigger close animation/logic
		# _on_modal_closed will handle queue_free via the signal


# --- Public Functions to Show Specific Modals ---

func show_building_info(building_data: Dictionary):
	_show_modal(building_info_modal_scene, building_data)

func show_inventory():
	_show_modal(inventory_modal_scene)

func show_help():
	_show_modal(help_modal_scene)

func show_map_info(map_data: Dictionary):
	_show_modal(map_info_modal_scene, map_data)

func show_region_info(region_data: Dictionary):
	_show_modal(region_info_modal_scene, region_data)

func show_resource_info(resource_data: Dictionary):
	_show_modal(resource_info_modal_scene, resource_data)

func show_item_info(item_data: Dictionary):
	# Add the Map instance to the data so the modal can check if item is in range
	var map_node = get_tree().get_first_node_in_group("Map")
	if map_node:
		# Create a new dictionary with both the item data and Map reference
		var combined_data = item_data.duplicate()
		combined_data["Map"] = map_node
		_show_modal(item_info_modal_scene, combined_data)
	else:
		# Fallback if Map node not found
		Logger.warning("UIManager", "Map node not found when showing item info")
		_show_modal(item_info_modal_scene, item_data)


func show_build_modal():
	# Get the Map instance to pass to the build modal
	var map_node = get_tree().get_first_node_in_group("Map")
	if map_node:
		var data = {"Map": map_node}
		_show_modal(build_modal_scene, data)
	else:
		Logger.warning("UIManager", "Map node not found when showing build modal")
		_show_modal(build_modal_scene)


func show_hero_modal():
	_show_modal(hero_modal_scene, GameState.player_hero)


func show_dashboard_modal():
	# Get player data from GameState
	var player_data = GameState.get_player_state()
	_show_modal(dashboard_modal_scene, player_data)


func logout():
	# Clear credentials and return to login screen
	if KokumeApi:
		KokumeApi.logout()

	# Reset game state
	if GameState:
		GameState.reset_state()

	# Show login screen
	show_login_screen()
