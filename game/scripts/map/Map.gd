extends Control
class_name MapViewer

const TILE_WIDTH:float = 256.0
const TILE_HEIGHT:float = 256.0

const MIN_ZOOM:float = 0.1
const MAX_ZOOM:float = 20.0

@export var base_url:String = 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'

@export_range(1,1000,1) var max_concurrent_requests:int = 8 # Increased concurrent requests
@export_range(100,100000,1) var max_cached_tiles:int = 500 # Increased cache size
@export_range(0,25,1) var max_zoom_level:int = 19 # Adjusted practical max zoom level for OSM

# Map state
var _xyz:Vector3 = Vector3(0,0,15.0) # Start at a reasonable zoom level

# Tile management
var _cache:Dictionary = {}
var _queue:Dictionary = {}
var _error:Dictionary = {}
var _http_requests: Array[HTTPRequest] = []

# Input state
var _dragging:bool = false
var _drag_start_pos_world:Vector2 = Vector2.ZERO
var _drag_start_mouse:Vector2 = Vector2.ZERO
var _rollover:bool = false
var _cursor:Vector2 = Vector2.ZERO

# Multi-touch gesture state
var _active_touches: Dictionary = {} # Stores active touch points by index
var _is_pinching: bool = false
var _pinch_start_distance: float = 0.0
var _pinch_start_zoom: float = 0.0
var _pinch_center: Vector2 = Vector2.ZERO

# Touch gesture detection - simplified approach
var _touch_start_time: float = 0.0
var _was_pinch_gesture: bool = false
var _pinch_end_time: float = 0.0 # Time when pinch gesture ended

# Touch thresholds
const TOUCH_DRAG_THRESHOLD: float = 400.0 # Minimum squared distance to consider as drag (20px squared)
const TOUCH_TIME_THRESHOLD: float = 0.6 # Maximum time for tap in seconds
const PINCH_COOLDOWN_TIME: float = 0.2 # Time to wait after pinch before allowing tap

# Timing
var _last_error_check:int = 0
var _last_cache_check:int = 0
var _last_data_fetch_time: float = 0.0

const DATA_FETCH_INTERVAL: float = 5.0 # Fetch data every 5 seconds if location changed significantly

# Player representation
var player_latitude: float = 0.0
var player_longitude: float = 0.0
var has_gps_fix: bool = false



var _geojson: Dictionary = {}

# Icon Textures (Ideally preload or make exports)
var _icon_textures: Dictionary = {}
var _default_icon_size = Vector2(36, 36) # Increased for better touch targets
var _base_icon_size = Vector2(48, 48) # Increased for better touch targets
var _item_icon_size = Vector2(32, 32) # Increased for better touch targets
var _icon_anchor = Vector2(12, 12) # Offset from top-left (adjust based on icon design)

# Debounce Timer
var _fetch_debounce_timer: Timer
const FETCH_DEBOUNCE_WAIT_TIME: float = 0.5 # Seconds to wait after last input before fetching
var _clickable_features: Array = [] # Stores {bounds, feature_data, type} for hit detection

# Selected Object State
var _selected_object_data: Dictionary = {} # Stores the 'properties' of the selected feature
var _selected_object_distance_meters: float = INF # Distance from player to selected object
var _selected_object_in_range: bool = false # Flag if selected object is within player range

# New state for highlighting based on player position and range
var _current_region_id = null # ID of the region the player is currently in
var _features_in_range: Dictionary = {} # Stores { feature_id: true } for features in range

# --- Initialization ---

func _ready():
	# Add self to the Map group so it can be found by UIManager
	add_to_group("Map")

	# Connect mouse signals
	mouse_entered.connect(func(): _rollover = true; queue_redraw())
	mouse_exited.connect(func(): _rollover = false; queue_redraw())

	# Connect to API signals
	if KokumeApi:
		KokumeApi.map_data_loaded.connect(_on_map_data_loaded)
		KokumeApi.map_data_failed.connect(_on_map_data_failed)
	else:
		Logger.critical("Map", "KokumeApi autoload not found!")

	# Connect to GPS Service signals
	if GpsService:
		GpsService.location_updated.connect(_on_gps_location_updated)
		GpsService.gps_status_changed.connect(_on_gps_status_changed)
		
		if GpsService.get_status() == 'ACTIVE':
			GpsService.force_update_location()
		
		# Initialize GPS status
		_on_gps_status_changed(GpsService.get_status())
	else:
		Logger.critical("Map", "GpsService autoload not found!")

	# Connect to UIManager for selection changes
	if UIManager:
		UIManager.selected_object_changed.connect(_on_selected_object_changed)
	else:
		Logger.critical("Map", "UIManager autoload not found!")

	# Initialize HTTP request pool for tiles
	for i in range(max_concurrent_requests):
		var req = HTTPRequest.new()
		add_child(req)
		req.request_completed.connect(_on_tile_response.bind(req))
		req.set_meta("tile", null)
		_http_requests.append(req)

	# Initial fetch if GPS is already active
	if has_gps_fix:
		_execute_fetch_nearby_data() # Call directly, skip debounce for initial load
		if GameState.follow_gps: # Added: Center on initial fix if following
			center_on_lonlat(player_longitude, player_latitude)

	# Preload icon textures (Example - adjust paths as needed)
	# Consider making these @export Texture2D vars for easier assignment in editor
	_icon_textures = {
		"base": load("res://assets/icons/base.svg"),
		"sub_base": load("res://assets/icons/sub_base.svg"),
		"farm": load("res://assets/icons/farm.svg"),
		"fishing_hut": load("res://assets/icons/fishing_hut.svg"),
		"iron_mine": load("res://assets/icons/mine.svg"), # Assuming same icon for iron/clay
		"clay_mine": load("res://assets/icons/mine.svg"),
		"sawmill": load("res://assets/icons/woodcutters.svg"),
		"quarry": load("res://assets/icons/stones.svg"),
		"item": load("res://assets/icons/treasure.svg"),
		# Add other icons as needed
	}
	# Check if loading failed for any icon
	for key in _icon_textures:
		if not _icon_textures[key] is Texture2D:
			Logger.error("Map", "Failed to load icon texture: %s" % key)
			# Optionally remove the key or assign a default placeholder texture


# --- Input Handling ---
func _input(event: InputEvent):
	if Engine.is_editor_hint(): return

	# Check if a modal is currently open - if so, don't process map input
	if UIManager and UIManager.current_modal and is_instance_valid(UIManager.current_modal):
		return

	if event is InputEventMouseButton: handle_mouse_button(event)
	elif event is InputEventMouseMotion: handle_mouse_motion(event)
	elif event is InputEventScreenTouch: handle_screen_touch(event)
	elif event is InputEventScreenDrag: handle_screen_drag(event)

func handle_mouse_button(event: InputEventMouseButton):
	# ignore touches
	if _active_touches.size() > 0:
		return
	
	if event.button_index == MOUSE_BUTTON_LEFT:
		if event.pressed and _rollover:
			_dragging = true
			_drag_start_mouse = event.position
			_drag_start_pos_world = screen_to_world(event.position.x, event.position.y)
		else: # released
			var was_dragging = _dragging
			_dragging = false
			if was_dragging and _drag_start_mouse.distance_squared_to(event.position) > 100: # Threshold to differentiate click from drag
				_fetch_nearby_data() # Fetch data after dragging stops
			elif _rollover: # It was a click inside the map
				_handle_map_click(event.position)
	elif _rollover:
		if event.button_index == MOUSE_BUTTON_WHEEL_DOWN and event.pressed:
			apply_zoom(0.9, event.position)
		elif event.button_index == MOUSE_BUTTON_WHEEL_UP and event.pressed:
			apply_zoom(1.1, event.position)

func handle_mouse_motion(event: InputEventMouseMotion):
	_cursor = event.position
	if _dragging:
		var current_world_pos = screen_to_world(event.position.x, event.position.y)
		var diff = _drag_start_pos_world - current_world_pos
		_xyz.x += diff.x; _xyz.y += diff.y

		if GameState.follow_gps:
			GameState.follow_gps = false

		queue_redraw()

	elif _rollover:
		queue_redraw()

func handle_screen_touch(event: InputEventScreenTouch):
	if event.pressed:
		# Add touch to active touches
		_active_touches[event.index] = event.position

		if _active_touches.size() == 1:
			# Single touch - initialize touch tracking
			_dragging = true
			_drag_start_mouse = event.position
			_drag_start_pos_world = screen_to_world(event.position.x, event.position.y)
			_is_pinching = false

			# Reset touch gesture detection
			_touch_start_time = Time.get_ticks_msec() / 1000.0
			var current_time = Time.get_ticks_msec() / 1000.0
			var time_since_pinch = current_time - _pinch_end_time

			# Only reset pinch flag if enough time has passed since last pinch
			if time_since_pinch > PINCH_COOLDOWN_TIME:
				_was_pinch_gesture = false
		elif _active_touches.size() == 2:
			# Two touches - start pinch gesture
			_start_pinch_gesture()
			_dragging = false
			_was_pinch_gesture = true # Mark that we had a pinch gesture
	else: # released
		# Remove touch from active touches
		if _active_touches.has(event.index):
			_active_touches.erase(event.index)

		if _active_touches.size() == 0:
			# No more touches - determine what happened
			var touch_duration = (Time.get_ticks_msec() / 1000.0) - _touch_start_time
			var current_time = Time.get_ticks_msec() / 1000.0

			var was_dragging = _dragging

			_dragging = false
			_is_pinching = false

			# Mark pinch end time if this was a pinch gesture
			if _was_pinch_gesture:
				_pinch_end_time = current_time

			# Enhanced tap detection - prevent tap after pinch gesture
			var time_since_pinch = current_time - _pinch_end_time

			# For tap detection, only use distance if it wasn't a pinch gesture
			var end_distance_squared = 0.0
			if not _was_pinch_gesture:
				end_distance_squared = _drag_start_mouse.distance_squared_to(event.position)

			var is_tap = not _was_pinch_gesture and \
						 touch_duration <= TOUCH_TIME_THRESHOLD and \
						 end_distance_squared <= TOUCH_DRAG_THRESHOLD and \
						 time_since_pinch > PINCH_COOLDOWN_TIME

			if is_tap:
				# It was a tap - handle click
				_handle_map_click(event.position)
				
			elif was_dragging and not _was_pinch_gesture and end_distance_squared > TOUCH_DRAG_THRESHOLD:
				# It was a drag - fetch new data (only if not pinch)
				_fetch_nearby_data()
				
			elif _was_pinch_gesture:
				# It was a pinch - fetch new data
				_fetch_nearby_data()
				
		elif _active_touches.size() == 1:
			# One touch remaining - switch back to dragging
			_is_pinching = false
			if not _dragging:
				var remaining_touch_pos = _active_touches.values()[0]
				_dragging = true
				_drag_start_mouse = remaining_touch_pos
				_drag_start_pos_world = screen_to_world(remaining_touch_pos.x, remaining_touch_pos.y)

				# DON'T reset timing here - this would break tap detection after pinch
				# _touch_start_time should only be reset at the start of a new gesture
				Logger.debug("TouchDebug", "Switched to single touch after pinch - keeping original timing")

func handle_screen_drag(event: InputEventScreenDrag):
	# Update touch position in active touches
	if _active_touches.has(event.index):
		_active_touches[event.index] = event.position

	if _is_pinching and _active_touches.size() >= 2:
		# Handle pinch gesture
		_handle_pinch_gesture()
		
	elif _dragging and _active_touches.size() == 1:
		# Handle single touch dragging
		var current_world_pos = screen_to_world(event.position.x, event.position.y)
		var diff = _drag_start_pos_world - current_world_pos
		_xyz.x += diff.x; _xyz.y += diff.y
		_drag_start_pos_world = current_world_pos # Update start for next delta
		
		if GameState.follow_gps: # Added: Check if currently following
			GameState.follow_gps = false
			
		queue_redraw()

func apply_zoom(multiplier: float, pivot_screen_pos: Vector2):
	var p1_world = screen_to_world(pivot_screen_pos.x, pivot_screen_pos.y)
	var new_zoom = clamp(_xyz.z * multiplier, MIN_ZOOM, MAX_ZOOM)
	
	if abs(new_zoom - _xyz.z) < 0.001: return
	
	_xyz.z = new_zoom
	
	var p2_world = screen_to_world(pivot_screen_pos.x, pivot_screen_pos.y)
	_xyz.x -= p2_world.x - p1_world.x
	_xyz.y -= p2_world.y - p1_world.y
	
	queue_redraw()
	_fetch_nearby_data()


# --- Pinch Gesture Handling ---

func _start_pinch_gesture():
	if _active_touches.size() < 2:
		return

	var touch_positions = _active_touches.values()
	var touch1 = touch_positions[0]
	var touch2 = touch_positions[1]

	_is_pinching = true
	_pinch_start_distance = touch1.distance_to(touch2)
	_pinch_start_zoom = _xyz.z
	_pinch_center = (touch1 + touch2) / 2.0

	Logger.debug("Map", "Started pinch gesture - Distance: %s, Zoom: %s, Center: %s" % [_pinch_start_distance, _pinch_start_zoom, _pinch_center])


func _handle_pinch_gesture():
	if _active_touches.size() < 2 or not _is_pinching:
		return

	var touch_positions = _active_touches.values()
	var touch1 = touch_positions[0]
	var touch2 = touch_positions[1]

	var current_distance = touch1.distance_to(touch2)
	var current_center = (touch1 + touch2) / 2.0

	# Avoid division by zero
	if _pinch_start_distance < 1.0:
		return

	# Calculate zoom multiplier based on distance change with reduced sensitivity
	var distance_ratio = current_distance / _pinch_start_distance
	# Reduce sensitivity by dampening the ratio change
	var zoom_sensitivity = 0.5 # Lower values = less sensitive
	var dampened_ratio = 1.0 + (distance_ratio - 1.0) * zoom_sensitivity
	var new_zoom = clamp(_pinch_start_zoom * dampened_ratio, MIN_ZOOM, MAX_ZOOM)

	# Apply zoom with the pinch center as pivot
	var p1_world = screen_to_world(_pinch_center.x, _pinch_center.y)
	_xyz.z = new_zoom
	var p2_world = screen_to_world(_pinch_center.x, _pinch_center.y)
	_xyz.x -= p2_world.x - p1_world.x
	_xyz.y -= p2_world.y - p1_world.y

	# Handle center movement (panning during pinch)
	var center_diff = _pinch_center - current_center
	if center_diff.length_squared() > 1.0: # Only apply if significant movement
		var world_diff = screen_to_world(center_diff.x, center_diff.y) - screen_to_world(0, 0)
		_xyz.x += world_diff.x
		_xyz.y += world_diff.y
		_pinch_center = current_center # Update center for next frame

	# Disable GPS following if user is manipulating the map
	if GameState.follow_gps:
		GameState.follow_gps = false

	queue_redraw()

func _handle_map_click(screen_pos: Vector2):
	Logger.debug("MapClick", "Handling map click at: %s" % screen_pos)

	# Iterate through clickable features in reverse draw order (topmost first)
	for i in range(_clickable_features.size() - 1, -1, -1):
		var clickable = _clickable_features[i]
		var bounds = clickable.bounds
		var feature_data = clickable.feature_data
		var hit = false

		if bounds is Rect2:
			if bounds.has_point(screen_pos):
				hit = true
		elif bounds is PackedVector2Array:
			if Geometry2D.is_point_in_polygon(screen_pos, bounds):
				hit = true

		if hit:
			var properties = feature_data.get("properties", {})
			var feature_type = properties.get("type", "unknown")
			Logger.info("MapClick", "Clicked on feature type: %s, Data: %s" % [feature_type, properties])

			if UIManager:
				match feature_type:
					"building":
						UIManager.show_building_info(feature_data)
					"region":
						UIManager.show_region_info(feature_data)
					"resource":
						UIManager.show_resource_info(feature_data)
					"item":
						UIManager.show_item_info(feature_data)
					_:
						Logger.warning("MapClick", "Clicked on unhandled feature type: %s" % feature_type)
			else:
				Logger.error("MapClick", "UIManager not available to show modal for feature type: %s" % feature_type)

			return # Stop after handling the first hit

	Logger.debug("MapClick", "Click did not hit any registered feature.")
	# Optional: Call UIManager.show_map_info here if clicking empty space should show general info


# --- GPS Handling ---
func _on_gps_location_updated(lat: float, lon: float, accuracy: float, speed: float):
	var previous_lat = player_latitude
	var previous_lon = player_longitude

	player_latitude = lat
	player_longitude = lon

	has_gps_fix = true
	
	# Update API
	KokumeApi.update_player_status({
		"latLng": {
			"lat": lat,
			"lng": lon
		},
		"accuracy": accuracy,
		"speed": speed,
	})

	# Update marker position regardless of follow state
	# Player marker drawing is now handled in _draw()

	# Center map only if follow_player is true
	if GameState.follow_gps:
		center_on_lonlat(lon, lat) # This already calls queue_redraw and fetch

	var now = Time.get_ticks_msec() / 1000.0
	var dist_sq = (lat - previous_lat)**2 + (lon - previous_lon)**2
	# Fetch data if location changed significantly OR if enough time passed OR if we just started following
	# Note: center_on_lonlat already triggers a fetch, so we might fetch twice if following.
	# Consider refining this logic if performance becomes an issue.
	if dist_sq > 0.00001 or now - _last_data_fetch_time > DATA_FETCH_INTERVAL:
		_fetch_nearby_data()

	# Redraw if not following (center_on_lonlat handles redraw if following)
	# Update current region and in-range features based on new player position
	_update_feature_states() # This will also queue_redraw if needed

	# Update distance to selected object if player moved (Keep this for potential UI display)
	_update_selected_object_range_status()


func _on_gps_status_changed(status: String):
	Logger.info("Map", "Received GPS status: %s" % status)
	
	# Player marker visibility is handled in _draw() based on has_gps_fix
	if status.begins_with("ERROR"):
		has_gps_fix = false
	elif status == "ACTIVE":
		has_gps_fix = true 
	else: 
		has_gps_fix = false

	queue_redraw()


func center_on_lonlat(lon: float, lat: float):
	var target_world_pos = lonlat_to_world(lon, lat)
	_xyz.x = target_world_pos.x; _xyz.y = target_world_pos.y
	Logger.debug("Map", "Centering map on Lon: %s, Lat: %s" % [lon, lat])
	queue_redraw(); _fetch_nearby_data()


# --- Data Fetching ---
func _fetch_nearby_data():
	if not _fetch_debounce_timer:
		_fetch_debounce_timer = Timer.new()
		_fetch_debounce_timer.wait_time = FETCH_DEBOUNCE_WAIT_TIME
		_fetch_debounce_timer.one_shot = true
		_fetch_debounce_timer.timeout.connect(_execute_fetch_nearby_data)
		add_child(_fetch_debounce_timer)
	
	_fetch_debounce_timer.start()

func _execute_fetch_nearby_data(): # Renamed: This now contains the actual fetch logic
	if not KokumeApi: return # No need for GPS fix if fetching based on view
	var now = Time.get_ticks_msec() / 1000.0
	# Optional: Add throttling if needed, e.g., check if now - _last_data_fetch_time > SOME_INTERVAL
	_last_data_fetch_time = now

	# Calculate bounding box of the current view
	var view_size = get_size()
	var top_left_lonlat = screen_to_lonlat(0, 0)
	var bottom_right_lonlat = screen_to_lonlat(view_size.x, view_size.y)

	# Ensure coordinates are valid before proceeding
	if not is_finite(top_left_lonlat.x) or not is_finite(top_left_lonlat.y) or \
	   not is_finite(bottom_right_lonlat.x) or not is_finite(bottom_right_lonlat.y):
		Logger.error("Map", "Invalid coordinates calculated for bounding box. TopLeft: %s, BottomRight: %s" % [top_left_lonlat, bottom_right_lonlat])
		return

	# Create the bounding box dictionary
	# Note: min_lat corresponds to the bottom edge (higher screen Y), max_lat to the top edge (lower screen Y)
	var bounding_box = {
		"min_lon": min(top_left_lonlat.x, bottom_right_lonlat.x), # Handle potential view wrapping/inversion
		"min_lat": min(top_left_lonlat.y, bottom_right_lonlat.y),
		"max_lon": max(top_left_lonlat.x, bottom_right_lonlat.x),
		"max_lat": max(top_left_lonlat.y, bottom_right_lonlat.y)
	}

	Logger.debug("Map", "Fetching data for bounding box: %s" % bounding_box)
	# Call the API with the bounding box (assuming KokumeApi.get_map_data is updated)
	Logger.start_timer("MapApiCall") # Start API call timer
	KokumeApi.get_map_data(bounding_box) # Zoom parameter removed


# --- API Callbacks ---
func _on_map_data_loaded(data: Dictionary):
	var api_duration = Logger.stop_timer("MapApiCall")
	if api_duration > 0: # Only log if timer was actually started
		Logger.debug("Map", "API call duration (success): %s usec" % api_duration)

	Logger.info("Map", "Received map data.")
	_geojson = data
	# Update current region and in-range features based on new map data
	_update_feature_states() # This will also queue_redraw if needed

func _on_map_data_failed(response_code: int, error_data: Dictionary):
	var api_duration = Logger.stop_timer("MapApiCall")
	if api_duration > 0: # Only log if timer was actually started
		Logger.debug("Map", "API call duration (failure): %s usec" % api_duration)

	Logger.error("Map", "Failed to load map data. Code: %s, Error: %s" % [response_code, error_data])
	_geojson = {} # Clear old data on failure
	queue_redraw()


# Public method to refresh map data - can be called from other scripts
func refresh_map_data():
	Logger.info("Map", "Refreshing map data")
	_fetch_nearby_data()


# --- Selection Handling ---
func _on_selected_object_changed(object_data):
	# object_data is expected to be the full GeoJSON feature dictionary (including geometry)
	# as passed from _handle_map_click via UIManager signal.
	# It can also be null when selection is cleared
	if object_data == null:
		_selected_object_data = {}
	else:
		_selected_object_data = object_data
	Logger.debug("Map", "Selected object changed: %s" % _selected_object_data)
	_update_selected_object_range_status() # Check range immediately on selection change

# --- Selected Object Range Status ---

func _update_selected_object_range_status():
	if _selected_object_data == null or _selected_object_data.is_empty():
		_selected_object_distance_meters = INF
		_selected_object_in_range = false
	elif not has_gps_fix:
		_selected_object_distance_meters = INF # Cannot calculate distance without player fix
		_selected_object_in_range = false
	else:
		# Extract object position
		# The selected data should contain geometry if it came from a map click feature
		var geometry = _selected_object_data.get("geometry")
		# Check if geometry exists and is a Point
		if geometry and typeof(geometry) == TYPE_DICTIONARY and geometry.get("type") == 'Point':
			var coordinates = geometry.get("coordinates")
			if coordinates and typeof(coordinates) == TYPE_ARRAY and coordinates.size() == 2:
				_selected_object_in_range = GameState.is_in_range(coordinates[0], coordinates[1])
				Logger.debug("Map", "Selected object in range: %s" % [_selected_object_in_range])
			else:
				Logger.warning("Map", "Selected Point object has invalid coordinates.")
				_selected_object_distance_meters = INF
				_selected_object_in_range = false
		elif geometry and typeof(geometry) == TYPE_DICTIONARY and geometry.get("type") in ['Polygon', 'MultiPolygon']:
			# Handle Polygons and MultiPolygons (use centroid of the first polygon for MultiPolygon)
			var coordinates = geometry.get("coordinates")
			if coordinates and typeof(coordinates) == TYPE_ARRAY and not coordinates.is_empty():
				var polygon_coords = coordinates[0] # Use the first ring (outer boundary)
				if geometry.get("type") == 'MultiPolygon' and typeof(coordinates[0][0]) == TYPE_ARRAY:
					polygon_coords = coordinates[0][0] # Use the first ring of the first polygon in MultiPolygon

				var centroid_lonlat = _calculate_polygon_centroid(polygon_coords)
				if centroid_lonlat != Vector2.INF: # Check if centroid calculation was successful
					_selected_object_in_range = GameState.is_in_range(centroid_lonlat.y, centroid_lonlat.x)
					Logger.debug("Map", "Selected Polygon centroid in range: %s" % [_selected_object_in_range])
				else:
					Logger.warning("Map", "Could not calculate centroid for selected Polygon/MultiPolygon.")
					_selected_object_distance_meters = INF
					_selected_object_in_range = false
			else:
				Logger.warning("Map", "Selected Polygon/MultiPolygon object has invalid coordinates.")
				_selected_object_distance_meters = INF
				_selected_object_in_range = false
		else:
			# Handle cases where geometry is missing or not a supported type
			# This might happen if the selected object data comes from somewhere else
			# or if it's a polygon and centroid calculation isn't implemented yet.
			Logger.warning("Map", "Selected object has no valid Point geometry to calculate distance. Data: %s" % _selected_object_data)
			_selected_object_distance_meters = INF
			_selected_object_in_range = false

	queue_redraw() # Redraw to update visual indicator for selected object (if still needed elsewhere)


# --- Drawing ---
func _draw():
	Logger.start_timer("MapDraw") # Start overall draw timer
	draw_rect(Rect2(Vector2.ZERO, size), Color.DARK_GRAY)
	var current_zoom_level = floor(_xyz.z); var clamped_zoom = min(max_zoom_level, current_zoom_level)
	var t1 = screen_to_tile(0, 0, _xyz.z, true); var t2 = screen_to_tile(size.x, size.y, _xyz.z, true)

	# DEBUG: Log tile drawing details
	var draw_tile_calls = 0
	Logger.start_timer("MapDraw_Tiles") # Start tile drawing timer
	Logger.debug("MapDraw", "Drawing tiles for zoom levels 0 to %d" % clamped_zoom) # Use Logger

	for z in range(0, clamped_zoom + 1):
		var scale_factor = pow(2.0, clamped_zoom - z)
		var view_tx1 = floor(t1.x / scale_factor); var view_ty1 = floor(t1.y / scale_factor)
		var view_tx2 = floor(t2.x / scale_factor); var view_ty2 = floor(t2.y / scale_factor)
		for tx in range(view_tx1, view_tx2 + 1):
			for ty in range(view_ty1, view_ty2 + 1):
				_draw_or_request_tile(tx, ty, z)
				draw_tile_calls += 1 # DEBUG: Increment counter

	var tile_draw_elapsed = Logger.stop_timer("MapDraw_Tiles")
	if tile_draw_elapsed > 0:
		Logger.debug("MapDraw", "Total _draw_or_request_tile calls: %d | Tile drawing loop time: %s usec" % [draw_tile_calls, tile_draw_elapsed])

	# DEBUG: Time GeoJSON drawing
	Logger.start_timer("MapDraw_GeoJSON") # Start GeoJSON timer
	_draw_geojson() # Updated drawing logic
	var geojson_elapsed_time = Logger.stop_timer("MapDraw_GeoJSON")
	if geojson_elapsed_time > 0:
		Logger.debug("MapDraw", "GeoJSON drawing time: %s usec" % geojson_elapsed_time)

	# --- Draw Player Position and Range ---
	if has_gps_fix:
		var player_screen_pos = lonlat_to_screen(player_longitude, player_latitude)

		# Draw player dot - larger for better visibility on mobile
		var player_dot_radius = 8.0
		var player_dot_color = Color.RED
		draw_circle(player_screen_pos, player_dot_radius, player_dot_color)

		# Draw range circle
		var player_range_meters = 0.0 # Default range
		if GameState.player_range:
			player_range_meters = float(GameState.player_range)

		if player_range_meters > 0:
			var range_pixel_radius = meters_to_pixels(player_range_meters, player_latitude, _xyz.z)
			if range_pixel_radius > 1.0: # Only draw if it's reasonably large
				var range_circle_color = Color(0.0, 0.5, 1.0, 0.3) # Semi-transparent blue
				# Draw unfilled circle using draw_polyline for antialiasing
				var segments = 32 # Number of segments for the circle approximation
				var points = PackedVector2Array()
				for i in range(segments + 1):
					var angle = TAU * i / segments
					points.append(player_screen_pos + Vector2(cos(angle), sin(angle)) * range_pixel_radius)
				draw_polyline(points, range_circle_color, 2.0, true) # Antialiased line

	if _rollover:
		var font = get_theme_font("font", "Label"); var font_size = get_theme_font_size("font_size", "Label")
		var ll = screen_to_lonlat(_cursor.x, _cursor.y); var text = lonlat_to_dms(ll.x, ll.y)
		draw_string(font, _cursor + Vector2(10, font_size + 2), text, HORIZONTAL_ALIGNMENT_LEFT, -1, font_size, Color.WHITE)

	var total_draw_time = Logger.stop_timer("MapDraw")
	if total_draw_time > 0:
		Logger.debug("MapDraw", "Total draw time: %s usec" % total_draw_time)

func _draw_or_request_tile(tx: int, ty: int, z: int):
	var tile = get_tile(tx, ty, z)
	if tile and tile.texture:
		var p1 = tile_to_screen(tx, ty, z); var p2 = tile_to_screen(tx + 1, ty + 1, z)
		draw_texture_rect(tile.texture, Rect2(p1, p2 - p1), false)

func _draw_geojson():
	_clickable_features.clear() # Clear clickable areas for this frame

	# Define highlight colors
	var current_region_border_color = Color.GREEN
	var resource_in_range_border_color = Color.ORANGE
	var item_building_in_range_overlay_color = Color(Color.GREEN_YELLOW, 0.8)
	var item_building_overlay_color = Color.WHITE

	if not _geojson.has('features') or not _geojson.features:
		return

	var viewport_rect = Rect2(Vector2.ZERO, size) # Optimization: Get viewport bounds

	for feature in _geojson.features:
		var geometry: Dictionary = feature.get("geometry")
		if not geometry or not geometry.has("type"): continue # Skip if geometry is invalid

		var properties: Dictionary = feature.get("properties", {})
		var feature_type = properties.get("type", "unknown")
		var feature_id = properties.get("id") # Assuming all relevant features have an ID

		if geometry.type == 'Polygon' or geometry.type == 'MultiPolygon': # Handle both Polygon and MultiPolygon consistently
			# Extract the outer ring coordinates, handling both Polygon and MultiPolygon
			var polygon_coords_list = []
			if geometry.type == 'Polygon':
				if geometry.coordinates and typeof(geometry.coordinates) == TYPE_ARRAY and not geometry.coordinates.is_empty():
					polygon_coords_list = [geometry.coordinates[0]] # Use the first ring (outer boundary)
			elif geometry.type == 'MultiPolygon':
				if geometry.coordinates and typeof(geometry.coordinates) == TYPE_ARRAY and not geometry.coordinates.is_empty():
					# Draw each polygon within the MultiPolygon
					for poly in geometry.coordinates:
						if poly and typeof(poly) == TYPE_ARRAY and not poly.is_empty():
							polygon_coords_list.append(poly[0]) # Use the first ring of each polygon

			if polygon_coords_list.is_empty():
				continue # Skip if no valid coordinates found

			# --- Draw each polygon (handles both single and multi) ---
			for coordinates in polygon_coords_list:
				var points: PackedVector2Array = []
				# --- Start of indented block ---
				var valid_poly = true
				for coord in coordinates:
					if typeof(coord) == TYPE_ARRAY and coord.size() == 2 and typeof(coord[0]) in [TYPE_INT, TYPE_FLOAT] and typeof(coord[1]) in [TYPE_INT, TYPE_FLOAT]:
						points.append(lonlat_to_screen(coord[0], coord[1]))
					else:
						Logger.error("MapDraw", "Invalid coordinate in Polygon: %s" % coord)
						valid_poly = false; break

				if valid_poly and not points.is_empty():
					# Optimization: Basic Bounding Box Check
					var poly_bounds = Rect2()
					poly_bounds.position = points[0]
					for i in range(1, points.size()):
						poly_bounds = poly_bounds.expand(points[i])

					if not viewport_rect.intersects(poly_bounds):
						continue # Skip drawing if bounding box is entirely off-screen

					# Client-side polygon simplification removed (now handled by backend based on zoom)
					# Check again if points array became too small after simplification
					if points.is_empty():
						continue

					var fill_color: Color = Color("#cecece") # Default fill
					var border_color: Color = Color.BLACK # Default border
					var border_weight: float = 1.0 # Default border weight
					var fill_opacity: float = 0.5 # Default opacity

					if feature_type == "resource":
						# Set default resource border
						border_color = Color.BURLYWOOD

						var resource_kind = properties.get("resource", "unknown")
						match resource_kind:
							"forest", "meadow": fill_color = Color("#00ff00") # Green
							"field": fill_color = Color("#ffff00") # Yellow
							"quarry", "mine": fill_color = Color("#646469") # Grey
							"water", "reservoir", "pond": fill_color = Color("#0000ff") # Blue
							_: fill_color = Color("#cecece") # Default grey
					elif feature_type == "region":
						var region_owner = properties.get("owner") # Expecting a Dictionary like { "id": player_id }
						if region_owner and typeof(region_owner) == TYPE_DICTIONARY and region_owner.has("id") and GameState and GameState.player_id != null:
							# Ensure owner.id is comparable (might be string or int)
							var owner_id_str = str(region_owner.id)
							var player_id_str = str(GameState.player_id)
							if owner_id_str == player_id_str:
								fill_color = Color("#20b720") # Player owned (Green)
							else:
								fill_color = Color("#b72020") # Enemy owned (Red)
						else:
							fill_color = Color("#cecece") # Unowned region (Grey)

					fill_color.a = fill_opacity
					draw_polygon(points, PackedColorArray([fill_color]))
					# Close the polyline loop by appending the start point
					if points.size() > 1:
						var closed_points = points.duplicate()
						closed_points.append(points[0])
						# Draw outline using draw_polyline for antialiasing
						draw_polyline(closed_points, border_color, border_weight, true)
						# Store clickable area for polygon
						var clickable_poly_data = { "bounds": points, "feature_data": feature, "type": feature_type }
						_clickable_features.append(clickable_poly_data)
				# --- End of indented block ---
		elif geometry.type == 'Point':
			var coordinates = geometry.coordinates
			if typeof(coordinates) == TYPE_ARRAY and coordinates.size() == 2 and typeof(coordinates[0]) in [TYPE_INT, TYPE_FLOAT] and typeof(coordinates[1]) in [TYPE_INT, TYPE_FLOAT]:
				var screen_pos = lonlat_to_screen(coordinates[0], coordinates[1])

				# Optimization: Check if point is within viewport before drawing
				# Add a small buffer to account for icon size/anchor
				var draw_bounds = Rect2(screen_pos - _icon_anchor - _base_icon_size, _base_icon_size * 2) # Generous buffer
				if not viewport_rect.intersects(draw_bounds):
					continue # Skip drawing if point is off-screen

				var icon_texture: Texture2D = null
				var icon_size = _default_icon_size
				var anchor = _icon_anchor # Use the class member

				if feature_type == "building":
					var building_type = properties.get("building_type", "unknown")
					icon_texture = _icon_textures.get(building_type) # Use preloaded texture
					if building_type == "base":
						icon_size = _base_icon_size
						# Adjust anchor if needed for base specifically, e.g., anchor = _base_icon_size / 2.0
				elif feature_type == "item":
					icon_texture = _icon_textures.get("item") # Use preloaded texture
					icon_size = _item_icon_size

				if icon_texture:
					var icon_rect = Rect2(screen_pos, icon_size)
					var background_pos = screen_pos + (icon_size/2)

					var overlay_padding = 2.0 # Pixels around the icon
					var overlay_rect = icon_rect.grow(overlay_padding)

					var overlay_radius = max(icon_size.x, icon_size.y) / 2.0 + overlay_padding
					# NEW: Draw background overlay if in range
					if feature_id != null and _features_in_range.has(feature_id):
						draw_circle(background_pos, overlay_radius, item_building_in_range_overlay_color)
					else:
						draw_circle(background_pos, overlay_radius, item_building_overlay_color)

					# Draw the actual icon on top
					draw_texture_rect(icon_texture, icon_rect, false)

					# Store clickable area for icon (using original icon_rect)
					var clickable_icon_data = { "bounds": icon_rect, "feature_data": feature, "type": feature_type }
					_clickable_features.append(clickable_icon_data)
				else:
					# Draw a default marker if no specific icon or type matched
					# Or if texture failed to load
					if feature_type == "building" or feature_type == "item":
						Logger.error("MapDraw", "Icon texture not found or invalid for type: %s, properties: %s" % [feature_type, properties])
					var default_marker_radius = 8.0 # Larger for better touch targets on mobile
					draw_circle(screen_pos, default_marker_radius, Color.PURPLE) # Default point marker
					# Store clickable area for default marker
					var default_marker_rect = Rect2(screen_pos - Vector2(default_marker_radius, default_marker_radius), Vector2(default_marker_radius, default_marker_radius) * 2)
					var clickable_default_data = { "bounds": default_marker_rect, "feature_data": feature, "type": feature_type }
					_clickable_features.append(clickable_default_data)
			else:
				Logger.error("MapDraw", "Invalid coordinate in Point: %s" % coordinates)

# --- Tile Fetching & Caching ---
func get_tile(x: int, y: int, z: int) -> Tile:
	z = min(z, max_zoom_level)
	var n = pow(2, z)
	if z < 0 or x < 0 or x >= n or y < 0 or y >= n: return null
	var idx = xyz_to_idx(x, y, z)
	var now = Time.get_unix_time_from_system()
	var tile: Tile = _cache.get(idx)
	if tile: tile.t = now; return tile
	for req in _http_requests:
		if req.has_meta("tile"): # Check if meta exists first
			var req_tile = req.get_meta("tile")
			# Check if req_tile is not null AND if it matches the index
			if req_tile != null and req_tile.i == idx:
				req_tile.t = now
				return req_tile
	if _error.has(idx): return null
	tile = _queue.get(idx)
	if tile: tile.t = now; return tile
	tile = Tile.new(idx, x, y, z)
	tile.url = base_url.replace('{x}', str(x)).replace('{y}', str(y)).replace('{z}', str(z))
	tile.t = now
	_queue[idx] = tile
	return tile

func _process(delta: float):
	var now = Time.get_unix_time_from_system()
	if not _last_error_check or now - _last_error_check > 10: _last_error_check = now; _clean_errors()
	if not _last_cache_check or now - _last_cache_check > 5: _last_cache_check = now; _clean_cache()
	if not _queue.is_empty():
		var available_req = _get_available_request()
		if available_req:
			var tile_to_fetch = _get_next_in_queue()
			if tile_to_fetch:
				_queue.erase(tile_to_fetch.i)
				available_req.set_meta("tile", tile_to_fetch)
				var error = available_req.request(tile_to_fetch.url)
				if error != OK:
					Logger.error("MapTileFetch", "HTTP request failed immediately for tile %s. Error code: %s" % [tile_to_fetch.i, error])
					tile_to_fetch.t = now
					_error[tile_to_fetch.i] = tile_to_fetch
					available_req.set_meta("tile", null)

func _get_available_request() -> HTTPRequest:
	for req in _http_requests:
		# Check if meta exists AND if it's null, or if meta doesn't exist at all
		if not req.has_meta("tile") or req.get_meta("tile") == null:
			return req
	return null

func _get_next_in_queue() -> Tile:
	if _queue.is_empty(): return null
	var best_tile: Tile = null
	for idx in _queue:
		var tile = _queue[idx]
		if not best_tile or tile.t > best_tile.t: best_tile = tile
	return best_tile

func _on_tile_response(result: int, code: int, headers: PackedStringArray, body: PackedByteArray, req: HTTPRequest):
	Logger.start_timer("MapTileProcess") # Start tile processing timer
	var tile: Tile = req.get_meta("tile")
	req.set_meta("tile", null)
	if not tile: Logger.error("MapTileProcess", "Received response for a request with no associated tile metadata."); return
	if result != HTTPRequest.RESULT_SUCCESS or code < 200 or code >= 300:
		Logger.error("MapTileFetch", "Tile request failed: %s Result: %s Code: %s" % [tile.url, result, code])
		tile.t = Time.get_unix_time_from_system(); _error[tile.i] = tile; queue_redraw(); return
	var image = Image.new()
	# TODO: Check content-type header to determine load function (png, jpg, webp?)
	var load_error = image.load_png_from_buffer(body)
	if load_error != OK:
		# Try loading as JPG as a fallback
		load_error = image.load_jpg_from_buffer(body)
		if load_error != OK:
			Logger.error("MapTileProcess", "Failed to load image data (PNG/JPG) for tile %s URL: %s" % [tile.i, tile.url])
			tile.t = Time.get_unix_time_from_system(); _error[tile.i] = tile
			queue_redraw()
			return

	var texture = ImageTexture.create_from_image(image)
	if not texture:
		Logger.error("MapTileProcess", "Failed to create texture from image for tile %s" % tile.i)
		tile.t = Time.get_unix_time_from_system(); _error[tile.i] = tile
	else:
		tile.texture = texture; _cache[tile.i] = tile

	var elapsed_time = Logger.stop_timer("MapTileProcess")
	if elapsed_time > 0:
		Logger.debug("MapTileProcess", "Tile %s processing time: %s usec" % [tile.i, elapsed_time])

	queue_redraw()


func _clean_cache():
	var overflow = _cache.size() - max_cached_tiles
	if overflow <= 0: return
	var list = _cache.values()
	list.sort_custom(func(a: Tile, b: Tile): return a.t < b.t)
	for i in range(overflow): _cache.erase(list[i].i)

func _clean_errors():
	var now = Time.get_unix_time_from_system()
	var keys_to_remove = []
	for idx in _error:
		if now - _error[idx].t > 15: keys_to_remove.append(idx)
	for idx in keys_to_remove: _error.erase(idx)

func _clean_all():
	for req in _http_requests:
		if req.get_meta("tile") != null: req.cancel_request(); req.set_meta("tile", null)
	_queue.clear(); _cache.clear(); _error.clear()
	Logger.info("Map", "Map viewer cleaned.") # Use Logger
# Removed: log_queue_size function


# --- Coordinate Conversion & Formatting ---
# ... (Keep existing conversion functions) ...
func lonlat_to_world(lon: float, lat: float) -> Vector2:
	var x = lon / 180.0 # longitude to -1..1 range
	# Mercator projection for latitude
	var y = log(tan(deg_to_rad(lat) / 2.0 + PI / 4.0)) / PI
	return Vector2(x, y) # Keep positive Y for North

func lonlat_to_screen(lon: float, lat: float) -> Vector2:
	return world_to_screen(lonlat_to_world(lon, lat).x, lonlat_to_world(lon, lat).y)

func lonlat_to_tile(lon: float, lat: float, z: float, do_floor: bool = false) -> Vector2:
	return world_to_tile(lonlat_to_world(lon, lat).x, lonlat_to_world(lon, lat).y, z, do_floor)
func world_to_lonlat(wx: float, wy: float) -> Vector2:
	var lon = wx * 180.0
	# Inverse Mercator projection for latitude (using positive y for North)
	var lat = rad_to_deg(2.0 * atan(exp(wy * PI)) - PI / 2.0)
	return Vector2(lon, lat)

func screen_to_lonlat(sx: float, sy: float) -> Vector2:
	return world_to_lonlat(screen_to_world(sx, sy).x, screen_to_world(sx, sy).y)

func tile_to_lonlat(tx: float, ty: float, tz: float) -> Vector2:
	return world_to_lonlat(tile_to_world(tx, ty, tz).x, tile_to_world(tx, ty, tz).y)

func screen_to_world(sx: float, sy: float) -> Vector2:
	var n = pow(2.0, _xyz.z)
	var scale = (n * TILE_WIDTH) / 2.0
	if scale == 0: return Vector2.ZERO # Avoid division by zero at extreme zooms
	var center_offset_x = (sx - size.x / 2.0) / scale
	var center_offset_y = (sy - size.y / 2.0) / scale
	var x = _xyz.x + center_offset_x
	var y = _xyz.y - center_offset_y # Positive Y is North, Screen Y increases downwards
	return Vector2(x, y)

func screen_to_tile(sx: float, sy: float, z: float, do_floor: bool = false) -> Vector2:
	return world_to_tile(screen_to_world(sx, sy).x, screen_to_world(sx, sy).y, z, do_floor)

func tile_to_screen(tx: float, ty: float, tz: float) -> Vector2:
	return world_to_screen(tile_to_world(tx, ty, tz).x, tile_to_world(tx, ty, tz).y)

func tile_to_world(tx: float, ty: float, tz: float) -> Vector2:
	var n = pow(2.0, floor(tz))
	if n == 0: return Vector2(-1, 1) # Avoid division by zero, return top-left world coord
	var x = (tx / n) * 2.0 - 1.0
	var y = -((ty / n) * 2.0 - 1.0) # Positive Y is North
	return Vector2(x, y)

func world_to_tile(wx: float, wy: float, z: float, do_floor: bool = false) -> Vector2:
	var n = pow(2.0, floor(z))
	var tx = ((wx + 1.0) / 2.0) * n
	var ty = ((-wy + 1.0) / 2.0) * n # Positive Y is North
	if do_floor:
		tx = floor(tx)
		ty = floor(ty)
	return Vector2(tx, ty)

func world_to_screen(wx: float, wy: float) -> Vector2:
	var n = pow(2.0, _xyz.z)
	var scale = (n * TILE_WIDTH) / 2.0
	var screen_x = (wx - _xyz.x) * scale + size.x / 2.0
	# Positive world Y (North) should result in smaller screen Y (up).
	var screen_y = (_xyz.y - wy) * scale + size.y / 2.0
	return Vector2(screen_x, screen_y)

func xyz_to_idx(x: int, y: int, z: int) -> int:
	if z < 0:
		return -1
	var level_start_index = (pow(4, z) - 1) / 3 if z > 0 else 0
	var n = pow(2, z)
	var index_in_level = y * n + x
	return int(level_start_index + index_in_level)

func lonlat_to_dms(lon: float, lat: float) -> String:
	var lat_dir = 'N' if lat >= 0 else 'S'
	lat = abs(lat)
	var lat_deg = floor(lat)
	var lat_min_float = (lat - lat_deg) * 60.0
	var lat_min = floor(lat_min_float)
	var lat_sec = floor((lat_min_float - lat_min) * 60.0)
	var lon_dir = 'E' if lon >= 0 else 'W'
	lon = abs(lon)
	var lon_deg = floor(lon)
	var lon_min_float = (lon - lon_deg) * 60.0
	var lon_min = floor(lon_min_float)
	var lon_sec = floor((lon_min_float - lon_min) * 60.0)
	return "%02d°%02d'%02d\"%s %03d°%02d'%02d\"%s" % [lat_deg, lat_min, lat_sec, lat_dir, lon_deg, lon_min, lon_sec, lon_dir]

const EARTH_RADIUS_METERS: float = 6378137.0 # WGS84 equatorial radius

# Calculates the approximate centroid of a polygon (simple average of vertices)
# Input: PackedVector2Array or Array of lon/lat coordinate pairs (e.g., [[lon1, lat1], [lon2, lat2], ...])
# Returns: Vector2(longitude, latitude) of the centroid, or Vector2.INF on error
func _calculate_polygon_centroid(polygon_coordinates) -> Vector2:
	if not polygon_coordinates or typeof(polygon_coordinates) != TYPE_ARRAY or polygon_coordinates.is_empty():
		Logger.error("Map", "Invalid coordinates provided for centroid calculation.")
		return Vector2.INF

	var sum_lon: float = 0.0
	var sum_lat: float = 0.0
	var count: int = 0

	for coord in polygon_coordinates:
		if typeof(coord) == TYPE_ARRAY and coord.size() == 2 and typeof(coord[0]) in [TYPE_INT, TYPE_FLOAT] and typeof(coord[1]) in [TYPE_INT, TYPE_FLOAT]:
			sum_lon += coord[0]
			sum_lat += coord[1]
			count += 1
		elif typeof(coord) == TYPE_VECTOR2: # Handle PackedVector2Array if passed directly (though unlikely from GeoJSON)
			sum_lon += coord.x
			sum_lat += coord.y
			count += 1
		# else: Skip invalid coordinate pairs within the array

	if count == 0:
		Logger.error("Map", "No valid coordinate pairs found for centroid calculation.")
		return Vector2.INF

	return Vector2(sum_lon / count, sum_lat / count)


# Converts a distance in meters to screen pixels at a given latitude and map zoom level
func meters_to_pixels(meters: float, lat: float, zoom: float) -> float:
	# Clamp latitude to avoid extreme projection issues near poles
	lat = clamp(lat, -85.0, 85.0)
	var lat_rad = deg_to_rad(lat)
	var cos_lat = cos(lat_rad)

	# Avoid division by zero at the poles (cosine is zero)
	if abs(cos_lat) < 0.00001:
		return 0.0

	# Calculate the number of pixels representing the entire map width at this zoom level
	var n = pow(2.0, zoom)
	var map_pixel_width = n * TILE_WIDTH

	# Calculate the circumference of the Earth at this latitude in meters
	var circumference_at_lat = 2.0 * PI * EARTH_RADIUS_METERS * cos_lat

	# Calculate how many pixels correspond to one meter at this latitude and zoom
	var pixels_per_meter = map_pixel_width / circumference_at_lat

	return meters * pixels_per_meter


# --- Feature State Update ---
# Updates _current_region_id and _features_in_range based on player position and geojson data
func _update_feature_states():
	# Reset states
	_current_region_id = null
	_features_in_range.clear()

	if not has_gps_fix or not _geojson.has('features') or not _geojson.features:
		queue_redraw() # Redraw to clear old highlights if GPS lost or no data
		return

	var player_screen_pos = lonlat_to_screen(player_longitude, player_latitude)
	var player_interaction_range = float(GameState.player_range) if GameState.player_range else 0.0

	for feature in _geojson.features:
		var properties = feature.get("properties", {})
		var feature_type = properties.get("type", "unknown")
		var feature_id = properties.get("id")
		var geometry = feature.get("geometry")

		if not geometry or not feature_id: continue # Skip features without geometry or ID

		# 1. Check if player is inside this region
		if feature_type == "region" and _current_region_id == null: # Only find the first containing region
			if geometry.type == 'Polygon' or geometry.type == 'MultiPolygon':
				var polygon_coords_list = []
				if geometry.type == 'Polygon':
					if geometry.coordinates and typeof(geometry.coordinates) == TYPE_ARRAY and not geometry.coordinates.is_empty():
						polygon_coords_list = [geometry.coordinates[0]]
				elif geometry.type == 'MultiPolygon':
					if geometry.coordinates and typeof(geometry.coordinates) == TYPE_ARRAY and not geometry.coordinates.is_empty():
						for poly in geometry.coordinates:
							if poly and typeof(poly) == TYPE_ARRAY and not poly.is_empty():
								polygon_coords_list.append(poly[0])

				for coordinates in polygon_coords_list:
					var screen_points: PackedVector2Array = []
					var valid_poly = true
					for coord in coordinates:
						if typeof(coord) == TYPE_ARRAY and coord.size() == 2 and typeof(coord[0]) in [TYPE_INT, TYPE_FLOAT] and typeof(coord[1]) in [TYPE_INT, TYPE_FLOAT]:
							screen_points.append(lonlat_to_screen(coord[0], coord[1]))
						else:
							valid_poly = false; break
					if valid_poly and not screen_points.is_empty():
						if Geometry2D.is_point_in_polygon(player_screen_pos, screen_points):
							_current_region_id = feature_id
							break # Found the region, stop checking regions
				if _current_region_id != null: # If found in MultiPolygon, stop checking other features for region
					pass # Continue to check range for this feature if it's also a resource etc.

		# 2. Check if feature is in player range (Items, Buildings, Resources)
		if feature_type in ["item", "building", "resource"]:
			if geometry.type == 'Point':
				var coordinates = geometry.get("coordinates")
				if coordinates and typeof(coordinates) == TYPE_ARRAY and coordinates.size() == 2:
					if GameState.is_in_range(coordinates[1], coordinates[0]):
						_features_in_range[feature_id] = true

			elif geometry.type in ['Polygon', 'MultiPolygon'] and feature_type == "resource": # Only calculate centroid distance for resources
				var coordinates = geometry.get("coordinates")
				if coordinates and typeof(coordinates) == TYPE_ARRAY and not coordinates.is_empty():
					var polygon_coords = coordinates[0]
					if geometry.get("type") == 'MultiPolygon' and typeof(coordinates[0][0]) == TYPE_ARRAY:
						polygon_coords = coordinates[0][0] # Use first ring of first polygon for MultiPolygon centroid approx.
					var centroid_lonlat = _calculate_polygon_centroid(polygon_coords)
					if centroid_lonlat != Vector2.INF and GameState.is_in_range(centroid_lonlat.y, centroid_lonlat.x):
						_features_in_range[feature_id] = true

	queue_redraw()


# --- Tile Class ---
class Tile:
	var i:int = 0; var x:int = 0; var y:int = 0; var z:int = 0; var t:int = 0
	var url:String = ''; var texture:Texture2D = null
	func _init(i:int, x:int, y:int, z:int): self.i = i; self.x = x; self.y = y; self.z = z
