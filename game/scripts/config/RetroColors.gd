extends Node
class_name RetroColors

# Retro color palette for Kokume game
# Based on the tower-defense style

# Background Colors
const BACKGROUND_DARK = Color("#111111")
const BACKGROUND_MEDIUM = Color("#222222")
const BACKGROUND_LIGHT = Color("#333333")
const BACKGROUND_PANEL = Color("#2a2a2a")
const BACKGROUND_MODAL = Color("#1a1a1a")

# UI Element Colors
const UI_PANEL_DARK = Color("#333333")
const UI_PANEL_MEDIUM = Color("#444444")
const UI_PANEL_LIGHT = Color("#555555")
const UI_BORDER = Color("#666666")
const UI_BORDER_HIGHLIGHT = Color("#888888")

# Button Colors
const BUTTON_NORMAL = Color("#666666")
const BUTTON_HOVER = Color("#777777")
const BUTTON_PRESSED = Color("#444444")
const BUTTON_DISABLED = Color("#333333")
const BUTTON_SELECTED = Color("#008800")

# Action Button Colors
const ACTION_BUTTON_NORMAL = Color("#4CAF50")
const ACTION_BUTTON_HOVER = Color("#66BB6A")
const ACTION_BUTTON_PRESSED = Color("#2E7D32")
const ACTION_BUTTON_DISABLED = Color("#aaaaaa")

# Text Colors
const TEXT_NORMAL = Color("#ffffff")
const TEXT_MUTED = Color("#aaaaaa")
const TEXT_DISABLED = Color("#666666")
const TEXT_HIGHLIGHT = Color("#ffff00")
const TEXT_ERROR = Color("#ff5555")
const TEXT_SUCCESS = Color("#55ff55")

# Map Colors
const MAP_BACKGROUND = Color("#222222")
const PLAYER_MARKER = Color("#ff0000")
const PLAYER_RANGE = Color(0.0, 0.5, 1.0, 0.3)

# Resource Colors
const RESOURCE_FOREST = Color("#00ff00")
const RESOURCE_FIELD = Color("#ffff00")
const RESOURCE_QUARRY = Color("#646469")
const RESOURCE_WATER = Color("#0000ff")
const RESOURCE_DEFAULT = Color("#cecece")

# Region Colors
const REGION_OWNED = Color("#20b720")
const REGION_ENEMY = Color("#b72020")
const REGION_NEUTRAL = Color("#cecece")

# Item/Building Highlight Colors
const ITEM_IN_RANGE = Color(0.8, 0.8, 0.2, 0.8)
const ITEM_OUT_OF_RANGE = Color(1.0, 1.0, 1.0, 0.5)

# Border Styles
static func create_panel_stylebox(color: Color = UI_PANEL_MEDIUM, border_color: Color = UI_BORDER, border_width: int = 1) -> StyleBoxFlat:
	var style = StyleBoxFlat.new()
	style.bg_color = color
	style.border_color = border_color
	style.border_width_left = border_width
	style.border_width_top = border_width
	style.border_width_right = border_width
	style.border_width_bottom = border_width
	style.corner_radius_top_left = 2
	style.corner_radius_top_right = 2
	style.corner_radius_bottom_left = 2
	style.corner_radius_bottom_right = 2
	return style

# Button Styles
static func create_button_stylebox(color: Color = BUTTON_NORMAL, border_color: Color = UI_BORDER, border_width: int = 1) -> StyleBoxFlat:
	var style = StyleBoxFlat.new()
	style.bg_color = color
	style.border_color = border_color
	style.border_width_left = border_width
	style.border_width_top = border_width
	style.border_width_right = border_width
	style.border_width_bottom = border_width
	style.corner_radius_top_left = 2
	style.corner_radius_top_right = 2
	style.corner_radius_bottom_left = 2
	style.corner_radius_bottom_right = 2
	return style

# Action Button Styles
static func create_action_button_stylebox(color: Color = ACTION_BUTTON_NORMAL, border_color: Color = UI_BORDER, border_width: int = 1) -> StyleBoxFlat:
	var style = StyleBoxFlat.new()
	style.bg_color = color
	style.border_color = border_color
	style.border_width_left = border_width
	style.border_width_top = border_width
	style.border_width_right = border_width
	style.border_width_bottom = border_width
	style.corner_radius_top_left = 2
	style.corner_radius_top_right = 2
	style.corner_radius_bottom_left = 2
	style.corner_radius_bottom_right = 2
	return style
