extends Node

const LOADING_SCENE_PATH = "res://scenes/ui/LoadingScreen.tscn"
const TARGET_SCENE_PATH = "res://scenes/ui/Login.tscn" # The scene to load after the loading screen

func _ready():
	Logger.info("Boot", "Scheduling switch to LoadingScreen.")
	# Defer the scene change to avoid issues during initial tree setup.
	# The LoadingScreen itself will handle loading the next scene (Login).
	get_tree().call_deferred("change_scene_to_file", LOADING_SCENE_PATH)
	# This node will be freed when the scene changes.
