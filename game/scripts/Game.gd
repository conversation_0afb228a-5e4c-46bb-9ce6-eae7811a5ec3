extends Control

# Preload the debug panel scene
const DebugPanelScene = preload("res://scenes/ui/DebugPanel.tscn")

var debug_panel_instance = null

func _ready():
	Logger.info("Game", "Game.gd _ready() called.") # Add this print
	# Instance and add the debug panel only in debug builds
	if OS.is_debug_build():
		Logger.debug("Game", "Debug build detected. Instancing DebugPanel.")
		debug_panel_instance = DebugPanelScene.instantiate()
		# Add it as a child. Ensure it draws on top if needed,
		# potentially by adding it to a CanvasLayer or setting z-index.
		# Adding directly to the root Control node should work.
		add_child(debug_panel_instance)
		# Initially hide it, let the user toggle it
		debug_panel_instance.hide()
	else:
		Logger.debug("Game", "Release build detected. DebugPanel not instanced.")


# Use _unhandled_input to catch events not handled by UI
func _unhandled_input(event):
	# --- Debug Print ---
	# print("Unhandled Input event received: ", event)

	# Check if the debug panel exists and if the toggle key (e.g., Semicolon) is pressed
	if event is InputEventKey and event.pressed:
		# --- Debug Print ---
		Logger.debug("Input", "Unhandled Key pressed: %s | Debug Panel Instance valid? %s" % [event.keycode, is_instance_valid(debug_panel_instance)])

		if is_instance_valid(debug_panel_instance) and event.keycode == KEY_SEMICOLON:
			print("Semicolon key pressed (unhandled), toggling DebugPanel visibility.")
			debug_panel_instance.toggle_visibility()
			# Prevent the event from propagating further if needed
			get_viewport().set_input_as_handled()