extends "res://scripts/ui/Modal.gd"

# References to UI nodes
@onready var hero_portrait: TextureRect = %HeroPortrait
@onready var hero_name_label: Label = %HeroNameLabel
@onready var hero_level_label: Label = %HeroLevelLabel
@onready var hero_description_label: Label = %HeroDescriptionLabel

# Progress bars for stats - using direct paths since unique names cause conflicts
@onready var health_progress: ProgressBar = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/HealthStat/ProgressBar
@onready var agility_progress: ProgressBar = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/AgilityStatStat/ProgressBar
@onready var strength_progress: ProgressBar = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/StrengthStat/ProgressBar
@onready var defense_progress: ProgressBar = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/DefenseStat/ProgressBar
@onready var experience_progress: ProgressBar = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/ExperienceStat/ProgressBar
@onready var experience_label: Label = $MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/ExperienceStat/Label

# Equipment container
@onready var equipment_container: GridContainer = %EquipmentContainer

# Default hero images
var hero_img_male = preload("res://assets/images/hero_m.webp")
var hero_img_female = preload("res://assets/images/hero_f.webp")
var hero_img_none = preload("res://assets/images/hero_n.webp")

func _ready():
	super._ready()
	# Additional setup if needed

func open_and_fill_modal(data: Dictionary):
	Logger.info("HeroModal", "Opening hero modal with data: %s" % str(data))

	var hero_data = data
	if hero_data.is_empty():
		# No hero, show message
		hero_name_label.text = "No Hero"
		hero_level_label.text = ""
		hero_description_label.text = "You don't have a hero yet. Visit a main building to hire one."

		# Hide stats and equipment
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer.visible = false
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/EquipmentContainer.visible = false
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsLabel.visible = false
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/EquipmentLabel.visible = false

		# Set default image
		hero_portrait.texture = hero_img_none
	else:
		# Show hero data
		hero_name_label.text = hero_data.get("name", "Unknown Hero")
		hero_level_label.text = "Level: %s" % hero_data.get("level", 1)
		hero_description_label.text = hero_data.get("description", "")

		# Show stats and equipment
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer.visible = true
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/EquipmentContainer.visible = true
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsLabel.visible = true
		$MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/EquipmentLabel.visible = true

		# Set hero portrait based on sex
		var sex = hero_data.get("sex", "none")
		if sex == "male":
			hero_portrait.texture = hero_img_male
		elif sex == "female":
			hero_portrait.texture = hero_img_female
		else:
			hero_portrait.texture = hero_img_none

		# Update stats
		var health = hero_data.get("health", 0)
		var max_health = hero_data.get("max_health", 100)
		var health_percent = (health / max_health) * 100 if max_health > 0 else 0
		health_progress.value = health_percent
		health_progress.get_parent().get_node("Label").text = "Health: %d/%d" % [health, max_health]

		agility_progress.value = hero_data.get("agility", 0)
		strength_progress.value = hero_data.get("strength", 0)
		defense_progress.value = hero_data.get("defense", 0)

		var exp = hero_data.get("experiences", 0)
		var exp_next = hero_data.get("experiences_next_level", 100)
		var exp_percent = (exp / exp_next) * 100 if exp_next > 0 else 0
		experience_progress.value = exp_percent
		experience_label.text = "Next Level (%d)" % exp_next

		# Update equipment
		_update_equipment(hero_data.get("equipment", {}))

	# Show the modal
	open_modal()

func _update_equipment(equipment_data: Dictionary):
	# Clear existing equipment
	for child in equipment_container.get_children():
		child.queue_free()

	if equipment_data.is_empty():
		var label = Label.new()
		label.text = "No equipment"
		equipment_container.add_child(label)
		return

	# Add equipment slots
	for slot_name in equipment_data:
		var item = equipment_data[slot_name]

		var slot_label = Label.new()
		slot_label.text = slot_name.capitalize() + ":"
		equipment_container.add_child(slot_label)

		var item_label = Label.new()
		if item:
			item_label.text = item.get("name", "Unknown Item")
		else:
			item_label.text = "Empty"
		equipment_container.add_child(item_label)
