extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

# References to specific nodes within this modal's content
@onready var building_level_label: Label = %BuildingLevelLabel
@onready var state_label: Label = %StateLabel
@onready var production_section: VBoxContainer = %ProductionSection
@onready var production_container: VBoxContainer = %ProductionContainer
@onready var storage_section: VBoxContainer = %StorageSection
@onready var storage_container: VBoxContainer = %StorageContainer
@onready var pickup_resources_button: Button = %PickupResourcesButton
@onready var upgrade_section: VBoxContainer = %UpgradeSection
@onready var upgrade_cost_container: VBoxContainer = %UpgradeCostContainer
@onready var upgrade_button: Button = %UpgradeButton
@onready var demolish_section: VBoxContainer = %DemolishSection
@onready var demolish_button: Button = %DemolishButton
@onready var steal_section: VBoxContainer = %StealSection
@onready var steal_button: Button = %StealButton
@onready var steal_result_container: VBoxContainer = %StealResultContainer
@onready var steal_result_label: Label = %StealResultLabel
@onready var steal_loot_container: VBoxContainer = %StealLootContainer
@onready var hire_hero_section: VBoxContainer = %HireHeroSection
@onready var hero_name_input: LineEdit = %HeroNameInput
@onready var hire_hero_button: Button = %HireHeroButton
@onready var queue_section: VBoxContainer = %QueueSection
@onready var queue_container: VBoxContainer = %QueueContainer

# Store current building data
var current_building_id: String = ""
var current_building_data: Dictionary = {}
var is_building_owner: bool = false
var is_in_range: bool = false
var has_hero: bool = false
var steal_result: Dictionary = {}

# Timer for updating the countdown
var countdown_timer: Timer = null
var state_end_time: Dictionary = {}

func _ready():
	super._ready()

	# Hide all sections by default
	production_section.hide()
	storage_section.hide()
	upgrade_section.hide()
	demolish_section.hide()
	steal_section.hide()
	hire_hero_section.hide()
	queue_section.hide()
	steal_result_container.hide()

	# Setup countdown timer
	countdown_timer = Timer.new()
	countdown_timer.wait_time = 1.0  # Update every second
	countdown_timer.autostart = false
	countdown_timer.connect("timeout", _on_countdown_timer_timeout)
	add_child(countdown_timer)

	# Connect API signals
	if KokumeApi:
		KokumeApi.building_loaded.connect(_on_building_loaded)
		KokumeApi.building_load_failed.connect(_on_building_load_failed)
		KokumeApi.building_resources_picked_up.connect(_on_building_resources_picked_up)
		KokumeApi.building_resource_pickup_failed.connect(_on_building_resource_pickup_failed)
		KokumeApi.building_upgraded.connect(_on_building_upgraded)
		KokumeApi.building_upgrade_failed.connect(_on_building_upgrade_failed)
		KokumeApi.building_demolished.connect(_on_building_demolished)
		KokumeApi.building_demolish_failed.connect(_on_building_demolish_failed)
		KokumeApi.building_stolen.connect(_on_building_stolen)
		KokumeApi.building_steal_failed.connect(_on_building_steal_failed)
		KokumeApi.hero_hired.connect(_on_hero_hired)
		KokumeApi.hero_hire_failed.connect(_on_hero_hire_failed)
		KokumeApi.unit_production_started.connect(_on_unit_production_started)
		KokumeApi.unit_production_start_failed.connect(_on_unit_production_start_failed)
		KokumeApi.unit_production_canceled.connect(_on_unit_production_canceled)
		KokumeApi.unit_production_cancel_failed.connect(_on_unit_production_cancel_failed)

# Called when the modal is opened with specific building data
func open_and_fill_modal(data: Dictionary):
	var properties = data.get("properties", {})
	current_building_id = properties.get("id", "")
	steal_result = {}

	# Reset all sections
	production_section.hide()
	storage_section.hide()
	upgrade_section.hide()
	demolish_section.hide()
	steal_section.hide()
	hire_hero_section.hide()
	queue_section.hide()
	steal_result_container.hide()

	# Disconnect any existing button connections
	_disconnect_button_signals()

	# Check if player is the owner of the building
	var owner_id = properties.get("owner", {}).get("id", -1)
	is_building_owner = int(owner_id) == GameState.player_id

	# Check if player has a hero
	has_hero = GameState.player_has_hero()

	# Check if building is in range
	var coords = data.geometry.coordinates
	is_in_range = GameState.is_in_range(coords[1], coords[0])

	# Update basic building info
	if title_label:
		title_label.text = properties.get("name", "Unknown Building")

	if building_level_label:
		building_level_label.text = "Level: %s" % str(properties.get("level", 1))

	if state_label:
		state_label.text = "State: %s" % properties.get("state", "Unknown")

	# Connect button signals based on ownership and range
	if is_building_owner:
		if pickup_resources_button:
			pickup_resources_button.pressed.connect(_on_pickup_resources_button_pressed)

		if upgrade_button:
			upgrade_button.pressed.connect(_on_upgrade_button_pressed)

		if demolish_button:
			demolish_button.pressed.connect(_on_demolish_button_pressed)

		if hire_hero_button:
			hire_hero_button.pressed.connect(_on_hire_hero_button_pressed)
	else:
		if steal_button:
			steal_button.pressed.connect(_on_steal_button_pressed)

	# Fetch building data from API
	KokumeApi.get_building(current_building_id)

	open_modal()

# Disconnect all button signals to prevent multiple connections
func _disconnect_button_signals():
	if pickup_resources_button and pickup_resources_button.pressed.is_connected(_on_pickup_resources_button_pressed):
		pickup_resources_button.pressed.disconnect(_on_pickup_resources_button_pressed)

	if upgrade_button and upgrade_button.pressed.is_connected(_on_upgrade_button_pressed):
		upgrade_button.pressed.disconnect(_on_upgrade_button_pressed)

	if demolish_button and demolish_button.pressed.is_connected(_on_demolish_button_pressed):
		demolish_button.pressed.disconnect(_on_demolish_button_pressed)

	if steal_button and steal_button.pressed.is_connected(_on_steal_button_pressed):
		steal_button.pressed.disconnect(_on_steal_button_pressed)

	if hire_hero_button and hire_hero_button.pressed.is_connected(_on_hire_hero_button_pressed):
		hire_hero_button.pressed.disconnect(_on_hire_hero_button_pressed)

# Update the production section with current building production data
func _update_production_section():
	# Clear existing production items
	for child in production_container.get_children():
		child.queue_free()

	# Get production data from building
	var productions = current_building_data.get("productions", [])

	if productions.is_empty():
		var no_production_label = Label.new()
		no_production_label.text = "This building does not produce resources."
		production_container.add_child(no_production_label)
		return

	# Get current building level
	var current_level = current_building_data.get("level", 1)

	# Add production items
	for production in productions:
		var resource_type = production.get("type", {})
		var levels = production.get("levels", [])

		if levels.size() >= current_level:
			var production_label = Label.new()
			var resource_name = resource_type.get("name", "Unknown")
			var amount_per_hour = levels[current_level - 1]
			production_label.text = "%s: %d per hour" % [resource_name, amount_per_hour]
			production_container.add_child(production_label)

# Update the storage section with current building storage data
func _update_storage_section():
	# Get storage data from building
	var stock = current_building_data.get("stock", [])
	
	storage_section.visible = (is_building_owner and current_building_data.state == 'built') or (current_building_data.state == 'demolished' and not stock.is_empty())
	
	%StorageLabel.text = 'Sklad' if current_building_data.state != 'demolished' else 'Trosky'
	
	# Clear existing storage items
	for child in storage_container.get_children():
		child.queue_free()

	if stock.is_empty():
		var empty_storage_label = Label.new()
		empty_storage_label.text = "Storage is empty."
		storage_container.add_child(empty_storage_label)
		pickup_resources_button.visible = false
		return

	# Add storage items
	for item in stock:
		var resource_type = item.get("type", {})
		var amount = item.get("amount", 0)

		var storage_label = Label.new()
		storage_label.text = "%s: %d" % [resource_type.get("name", "Unknown"), amount]
		storage_container.add_child(storage_label)

	# Show pickup button only if player is owner and building is in range
	pickup_resources_button.visible = is_building_owner and is_in_range and not stock.is_empty()

# Update the upgrade section with current building upgrade data
func _update_upgrade_section():
	# Clear existing upgrade cost items
	for child in upgrade_cost_container.get_children():
		child.queue_free()

	# Get building type and max level
	var building_type = current_building_data.type
	var max_level = building_type.max_level
	var current_level = current_building_data.level

	# Hide upgrade section if building is at max level or player is not the owner
	if current_level >= max_level or not is_building_owner or not is_in_range or not current_building_data.state == 'built':
		upgrade_section.visible = false
		return

	upgrade_section.visible = true

	# Get upgrade costs
	var upgrade_costs = current_building_data.upgrade_cost

	if upgrade_costs.is_empty():
		var no_cost_label = Label.new()
		no_cost_label.text = "No resources required for upgrade."
		upgrade_cost_container.add_child(no_cost_label)
	else:
		# Add upgrade cost items
		for cost in upgrade_costs:
			var resource_type = cost.type
			var amount = cost.amount

			var cost_label = Label.new()
			cost_label.text = "%s: %d" % [resource_type.get("name", "Unknown"), amount]

			# Check if player has enough resources
			var player_has_enough = GameState.check_player_has_enough_resources(resource_type.slug, amount)
			if not player_has_enough:
				cost_label.add_theme_color_override("font_color", Color(1, 0, 0)) # Red color for insufficient resources

			upgrade_cost_container.add_child(cost_label)

	# Enable upgrade button only if player has enough resources and building is in range
	upgrade_button.disabled = not _check_player_has_enough_resources_for_upgrade()


# Check if player has enough resources for the upgrade
func _check_player_has_enough_resources_for_upgrade() -> bool:
	var upgrade_costs = current_building_data.upgrade_cost

	for cost in upgrade_costs:
		var resource_type = cost.type
		var amount = cost.amount

		if not GameState.check_player_has_enough_resources(resource_type.slug, amount):
			return false

	return true

# Override close_modal to clean up timer and disconnect signals
func close_modal():
	# Stop the countdown timer when closing the modal
	if countdown_timer and countdown_timer.is_inside_tree():
		countdown_timer.stop()

	# Clear state end time
	state_end_time = {}

	# Disconnect button signals
	_disconnect_button_signals()

	super.close_modal()

# Button handlers
func _on_pickup_resources_button_pressed():
	if current_building_id.is_empty() or not is_in_range:
		return

	if KokumeApi:
		KokumeApi.pickup_building_resources(current_building_id)
		# Disable button while request is in progress
		pickup_resources_button.disabled = true

func _on_upgrade_button_pressed():
	if current_building_id.is_empty() or not is_in_range:
		return

	if not _check_player_has_enough_resources_for_upgrade():
		return

	if KokumeApi:
		KokumeApi.upgrade_building(current_building_id)
		# Disable button while request is in progress
		upgrade_button.disabled = true

func _on_demolish_button_pressed():
	if current_building_id.is_empty() or not is_in_range:
		return

	if KokumeApi:
		KokumeApi.demolish_building(current_building_id)
		# Disable button while request is in progress
		demolish_button.disabled = true

func _on_steal_button_pressed():
	if current_building_id.is_empty() or not is_in_range or not has_hero:
		return

	if KokumeApi:
		KokumeApi.steal_building(current_building_id)
		# Disable button while request is in progress
		steal_button.disabled = true

func _on_hire_hero_button_pressed():
	if not is_in_range or hero_name_input.text.is_empty():
		return

	if KokumeApi:
		var hero_data = {"name": hero_name_input.text}
		KokumeApi.hire_hero(hero_data)
		# Disable button while request is in progress
		hire_hero_button.disabled = true

func _on_start_production_button_pressed(unit_definition_id: String):
	if current_building_id.is_empty() or not is_in_range:
		return

	if KokumeApi:
		KokumeApi.start_unit_production(current_building_id, unit_definition_id)

func _on_cancel_production_button_pressed(production_id: String):
	if current_building_id.is_empty() or not is_in_range:
		return

	if KokumeApi:
		KokumeApi.cancel_unit_production(current_building_id, production_id)

# API response handlers
func _on_building_resources_picked_up(data: Dictionary):
	Logger.info("BuildingInfoModal", "Resources picked up successfully")

	# Update the current building data with the new data
	if data.get("id", "") == current_building_id:
		current_building_data = data
		_update_storage_section()

func _on_building_resource_pickup_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to pick up resources. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable the pickup button
	pickup_resources_button.disabled = false

# Building upgrade API response handlers
func _on_building_upgraded(data: Dictionary):
	Logger.info("BuildingInfoModal", "Building upgraded successfully")

	# Update the current building data with the new data
	if data.get("id", "") == current_building_id:
		current_building_data = data
		_update_production_section()
		_update_upgrade_section()

	# Re-enable the upgrade button
	upgrade_button.disabled = false

func _on_building_upgrade_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to upgrade building. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable the upgrade button
	upgrade_button.disabled = false


func _on_building_loaded(data: Dictionary):
	Logger.info("BuildingInfoModal", "Building loaded successfully")

	# Update the current building data with the new data
	if data.uuid == current_building_id:
		current_building_data = data
		_update_basic_info()
		_update_production_section()
		_update_storage_section()
		_update_upgrade_section()
		_update_demolish_section()
		_update_steal_section()
		_update_hire_hero_section()
		_update_queue_section()

func _on_building_load_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to load building. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable all buttons
	if pickup_resources_button:
		pickup_resources_button.disabled = false
	if upgrade_button:
		upgrade_button.disabled = false
	if demolish_button:
		demolish_button.disabled = false
	if steal_button:
		steal_button.disabled = false
	if hire_hero_button:
		hire_hero_button.disabled = false

# Building demolish API response handlers
func _on_building_demolished(data: Dictionary):
	Logger.info("BuildingInfoModal", "Building demolished successfully")

	# Update the current building data with the new data
	if data.get("id", "") == current_building_id:
		current_building_data = data
		_update_basic_info()
		_update_storage_section()
		_update_demolish_section()

	# Re-enable the demolish button
	demolish_button.disabled = false

func _on_building_demolish_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to demolish building. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable the demolish button
	demolish_button.disabled = false

# Building steal API response handlers
func _on_building_stolen(data: Dictionary):
	Logger.info("BuildingInfoModal", "Building stolen successfully")

	# Store the steal result
	steal_result = data

	# Update the steal result section
	_update_steal_result_section()

	# Re-enable the steal button
	steal_button.disabled = false

func _on_building_steal_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to steal building. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable the steal button
	steal_button.disabled = false

# Hero hire API response handlers
func _on_hero_hired(_data: Dictionary):
	Logger.info("BuildingInfoModal", "Hero hired successfully")

	# Update player has hero status
	has_hero = true

	# Hide the hire hero section
	hire_hero_section.hide()

	# Re-enable the hire hero button
	hire_hero_button.disabled = false

func _on_hero_hire_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to hire hero. Code: %d, Error: %s" % [response_code, error_data])

	# Re-enable the hire hero button
	hire_hero_button.disabled = false

# Unit production API response handlers
func _on_unit_production_started(data: Dictionary):
	Logger.info("BuildingInfoModal", "Unit production started successfully")

	# Update the current building data with the new data
	if data.get("id", "") == current_building_id:
		current_building_data = data
		_update_queue_section()

func _on_unit_production_start_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to start unit production. Code: %d, Error: %s" % [response_code, error_data])

func _on_unit_production_canceled(data: Dictionary):
	Logger.info("BuildingInfoModal", "Unit production canceled successfully")

	# Update the current building data with the new data
	if data.get("id", "") == current_building_id:
		current_building_data = data
		_update_queue_section()

func _on_unit_production_cancel_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildingInfoModal", "Failed to cancel unit production. Code: %d, Error: %s" % [response_code, error_data])

# Timer callback to update the countdown
func _on_countdown_timer_timeout():
	if state_end_time.is_empty() or not state_label:
		return

	var now = Time.get_unix_time_from_system() * 1000  # Current time in milliseconds
	var end_time = state_end_time.time_ms
	var time_remaining = end_time - now

	if time_remaining <= 0:
		state_label.text = "Stav: %s (hotovo)" % state_end_time.state
		countdown_timer.stop()

		# Refresh building data after state expires
		if current_building_id:
			KokumeApi.get_building(current_building_id)
	else:
		state_label.text = "Stav: %s (%s)" % [state_end_time.state, _format_time_remaining(time_remaining)]

# Update basic building info
func _update_basic_info():
	if not current_building_data:
		return

	building_level_label.text = "Level: %d" % [current_building_data.level]
	state_label.text = "Stav: %s" % current_building_data.get("state", "Unknown")

	# Update state timer if needed
	if current_building_data.has("state_valid_to") and current_building_data.state != 'demolished':
		var state_valid_to = current_building_data.state_valid_to
		var date_time = Time.get_datetime_dict_from_datetime_string(state_valid_to, false)
		var unix_time = Time.get_unix_time_from_datetime_dict(date_time) * 1000

		# Apply Czech Republic timezone offset (server is in Czech timezone)
		# The server timestamp is in Czech timezone, but we need to adjust it for the local time
		# calculation since Time.get_unix_time_from_system() returns local time
		var timezone_offset_ms = KokumeApi.SERVER_TIMEZONE * 60 * 60 * 1000
		unix_time += timezone_offset_ms

		state_end_time = {
			"state": current_building_data.state,
			"time_ms": unix_time
		}

		countdown_timer.start()


func _update_demolish_section():
	# Hide demolish section if player is not the owner or building is not in built state
	if not is_building_owner or current_building_data.state != "built":
		demolish_section.hide()
		return

	demolish_section.show()

	# Enable demolish button only if building is in range
	demolish_button.disabled = not is_in_range


func _update_steal_section():
	# Hide steal section if player is the owner or player has no hero or building is not in built state
	if is_building_owner or not has_hero or current_building_data.get("state", "") != "built":
		steal_section.hide()
		return

	steal_section.show()

	# Enable steal button only if building is in range
	steal_button.disabled = not is_in_range

	# Update steal result if available
	if not steal_result.is_empty():
		_update_steal_result_section()

# Update the steal result section
func _update_steal_result_section():
	if steal_result.is_empty():
		steal_result_container.hide()
		return

	steal_result_container.show()

	# Clear existing steal loot items
	for child in steal_loot_container.get_children():
		child.queue_free()

	# Update steal result label
	var combat_result = steal_result.get("combat_result", {})
	steal_result_label.text = "Combat Result: %s won, %s lost" % [
		combat_result.get("winner", "Unknown"),
		combat_result.get("loser", "Unknown")
	]

	# Add loot items if player won
	var is_win = combat_result.get("is_win", false)
	var loot = steal_result.get("loot", [])

	if is_win and not loot.is_empty():
		for loot_item in loot:
			var resource_type = loot_item.get("type", {})
			var amount = loot_item.get("amount", 0)

			var loot_label = Label.new()
			loot_label.text = "%s: %d" % [resource_type.get("name", "Unknown"), amount]
			steal_loot_container.add_child(loot_label)
	else:
		var no_loot_label = Label.new()
		no_loot_label.text = "No loot found."
		steal_loot_container.add_child(no_loot_label)

# Update the hire hero section
func _update_hire_hero_section():
	# Hide hire hero section if player already has a hero or player is not the owner
	# or building type is not a base or building is not in built state
	if has_hero or not is_building_owner or current_building_data.get("type", {}).get("slug", "") != "base" or current_building_data.get("state", "") != "built":
		hire_hero_section.hide()
		return

	hire_hero_section.show()

	# Enable hire hero button only if building is in range and name is not empty
	hire_hero_button.disabled = not is_in_range or hero_name_input.text.is_empty()

# Update the queue section
func _update_queue_section():
	# Clear existing queue items
	for child in queue_container.get_children():
		child.queue_free()

	# Hide queue section if player is not the owner or building is not in built state
	if not is_building_owner or current_building_data.get("state", "") != "built":
		queue_section.hide()
		return

	# Get production queue data
	var production_queue = current_building_data.get("production_queue", [])

	if production_queue.is_empty():
		queue_section.hide()
		return

	queue_section.show()

	# Add queue items
	for queue_item in production_queue:
		var unit_definition = queue_item.get("unit_definition", {})
		var status = queue_item.get("status", "Unknown")
		var queue_id = queue_item.get("id", "")

		var queue_hbox = HBoxContainer.new()

		var queue_label = Label.new()
		queue_label.text = "%s - %s" % [unit_definition.get("name", "Unknown"), status]
		queue_hbox.add_child(queue_label)

		# Add cancel button if status is "in_progress"
		if status == "in_progress" and is_in_range:
			var cancel_button = Button.new()
			cancel_button.text = "Cancel"
			cancel_button.pressed.connect(func(): _on_cancel_production_button_pressed(queue_id))
			queue_hbox.add_child(cancel_button)

		queue_container.add_child(queue_hbox)

# Update the available units section
func _update_available_units_section():
	# Clear existing production items
	for child in production_container.get_children():
		child.queue_free()

	# Hide production section if player is not the owner or building is not in built state
	if not is_building_owner or current_building_data.get("state", "") != "built":
		production_section.hide()
		return

	# Get unit productions data
	var unit_productions = current_building_data.get("type", {}).get("unit_productions", [])

	if unit_productions.is_empty():
		production_section.hide()
		return

	production_section.show()

	# Add unit production items
	for production in unit_productions:
		var unit_definition = production.get("unit_definition", {})
		var cost = production.get("cost", [])

		var unit_vbox = VBoxContainer.new()

		var unit_name_label = Label.new()
		unit_name_label.text = unit_definition.get("name", "Unknown")
		unit_vbox.add_child(unit_name_label)

		var unit_desc_label = Label.new()
		unit_desc_label.text = unit_definition.get("description", "")
		unit_vbox.add_child(unit_desc_label)

		var cost_label = Label.new()
		cost_label.text = "Cost:"
		unit_vbox.add_child(cost_label)

		var has_enough_resources = true

		# Add cost items
		for cost_item in cost:
			var resource_type = cost_item.get("type", {})
			var amount = cost_item.get("amount", 0)

			var resource_label = Label.new()
			resource_label.text = "%s: %d" % [resource_type.get("name", "Unknown"), amount]

			# Check if player has enough resources
			var player_has_enough = GameState.check_player_has_enough_resources(resource_type.get("slug", ""), amount)
			if not player_has_enough:
				resource_label.add_theme_color_override("font_color", Color(1, 0, 0)) # Red color for insufficient resources
				has_enough_resources = false

			unit_vbox.add_child(resource_label)

		# Add start production button
		var start_button = Button.new()
		start_button.text = "Start Production"
		start_button.disabled = not is_in_range or not has_enough_resources
		start_button.pressed.connect(func(): _on_start_production_button_pressed(unit_definition.get("id", "")))
		unit_vbox.add_child(start_button)

		production_container.add_child(unit_vbox)

# Format time remaining in a human-readable format
func _format_time_remaining(time_ms: int) -> String:
	var seconds = int(float(time_ms) / 1000.0) % 60
	var minutes = int(float(time_ms) / (1000.0 * 60.0)) % 60
	var hours = int(float(time_ms) / (1000.0 * 60.0 * 60.0))

	if hours > 0:
		return "%d:%02d:%02d" % [hours, minutes, seconds]
	else:
		return "%d:%02d" % [minutes, seconds]
