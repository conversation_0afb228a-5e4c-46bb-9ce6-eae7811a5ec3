extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

# References to specific nodes within this modal's content
@onready var inventory_grid: GridContainer = %InventoryGrid # Assumes a GridContainer for items
# Add references for equipment slots if they are part of this modal
# @onready var head_slot: TextureRect = %HeadSlot
# @onready var body_slot: TextureRect = %BodySlot
# ... etc.

func _ready():
	super._ready() # Call the base class's ready function
	# Connect to GameState inventory updates if needed
	if GameState:
		GameState.inventory_updated.connect(_on_inventory_updated)
		# Populate with initial inventory state
		_on_inventory_updated(GameState.get_player_state().get("inventory", {})) # Assuming inventory is part of player state
	else:
		Logger.critical("UI", "InventoryModal: GameState autoload not found!")


# Called when the modal is opened
func open_modal():
	# Populate inventory grid based on current GameState
	_populate_inventory()
	super.open_modal()


func _populate_inventory():
	if not inventory_grid:
		Logger.error("UI", "InventoryModal: InventoryGrid node not found!")
		return

	# Clear existing items
	for child in inventory_grid.get_children():
		inventory_grid.remove_child(child)
		child.queue_free()

	# Get inventory data from GameState (adjust key as needed)
	var inventory_items = GameState.get_player_state().get("inventory_items", [])

	# Populate grid (example using simple Labels, replace with actual item slots later)
	for item_data in inventory_items:
		var item_label = Label.new()
		item_label.text = "%s (x%d)" % [item_data.get("name", "Unknown Item"), item_data.get("quantity", 1)]
		inventory_grid.add_child(item_label)

	# Populate equipment slots if applicable
	# var equipment = GameState.get_player_state().get("equipment", {})
	# _update_slot(head_slot, equipment.get("head"))
	# _update_slot(body_slot, equipment.get("body"))
	# ... etc.


# Called when GameState signals an inventory update
func _on_inventory_updated(inventory_data: Dictionary):
	# Check if the modal is currently visible before repopulating
	if visible:
		_populate_inventory()


# Helper to update an equipment slot (example)
# func _update_slot(slot_node: TextureRect, item_data: Dictionary):
#	 if slot_node:
#		 if item_data:
#			 # Load item texture based on item_data (e.g., item_data.icon_path)
#			 # slot_node.texture = load(item_data.get("icon_path", null))
#			 pass
#		 else:
#			 slot_node.texture = null # Clear slot if empty
