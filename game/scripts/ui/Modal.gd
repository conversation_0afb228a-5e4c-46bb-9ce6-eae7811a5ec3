extends MarginContainer

signal closed

@onready var title_label: Label = %TitleLabel
@onready var close_button: Button = %CloseButton
@onready var content_container: Container = %ContentContainer

func _ready():
	# Hide by default
	visible = false
	# Connect close button if it exists
	if close_button and close_button is Button:
		close_button.pressed.connect(close_modal)
	else:
		Logger.debug("UI", "Modal: Optional CloseButton not found.")


func open_modal():
	visible = true


func open_and_fill_modal(data: Dictionary):
	Logger.warning("UI", "Modal::open_and_fill_modal is empty")
	open_modal()


func close_modal():
	visible = false
	closed.emit()
