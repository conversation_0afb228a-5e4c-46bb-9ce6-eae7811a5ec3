extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

# References to specific nodes within this modal's content
@onready var coordinates_label: Label = %CoordinatesLabel
@onready var zoom_level_label: Label = %ZoomLevelLabel
# Add more labels or controls as needed (e.g., map stats, layers toggle)

func _ready():
	super._ready() # Call the base class's ready function
	# Set the title specifically for this modal
	get_node(^"MarginContainer/VBoxContainer/TitleLabel").text = "Map Information"


# Called when the modal is opened, potentially with map context data
func open_and_fill_modal(data: Dictionary):
	# Example: Display center coordinates and zoom level
	var center_lon = data.get("center_lon", 0.0)
	var center_lat = data.get("center_lat", 0.0)
	var zoom = data.get("zoom", 0.0)

	if coordinates_label:
		# Use the DMS formatting function if available (e.g., from MapViewer or a utility script)
		# For now, just display raw coordinates
		coordinates_label.text = "Center: %.5f, %.5f" % [center_lat, center_lon]
		# Alternatively, call a formatting function:
		# coordinates_label.text = "Center: %s" % MapUtils.lonlat_to_dms(center_lon, center_lat)

	if zoom_level_label:
		zoom_level_label.text = "Zoom Level: %.2f" % zoom

	# Populate other specific fields based on map_data
	# ...

	super.open_modal()
