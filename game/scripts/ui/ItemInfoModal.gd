extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

@onready var item_icon_texture: TextureRect = %ItemIconTexture
@onready var item_description_label: Label = %ItemDescriptionLabel
@onready var item_stats_label: Label = %ItemStatsLabel # Example for stats
@onready var use_button: Button = %UseButton
@onready var equip_button: Button = %EquipButton
@onready var pickup_button: Button = %PickupButton

# Variables to track item state
var is_in_range: bool = false
var _selected_item_id: String = ""

func _ready():
	super._ready()
	
	pickup_button.pressed.connect(_on_pickup_button_pressed)
	
	# Connect to API signals
	KokumeApi.item_picked_up.connect(_on_item_picked_up)
	KokumeApi.item_pickup_failed.connect(_on_item_pickup_failed)


# Called when the modal is opened with specific item data
func open_and_fill_modal(data: Dictionary):
	var data_properties = data.get("properties", {})
	if data_properties.is_empty():
		Logger.error("ItemInfoModal", "Received null item data.")
		# Optionally show an error message or close the modal
		close_modal()
		return

	# Store item ID for potential pickup action
	_selected_item_id = data_properties.get("uuid", data_properties.get("id", ""))
	Logger.debug("ItemInfoModal", "Item ID: %s, Properties: %s" % [_selected_item_id, data_properties])

	title_label.text = data_properties.name
	
	if item_description_label:
		item_description_label.text = data_properties.get("description", "Žádný popis.")
	if item_stats_label:
		# Example: Format stats if they exist
		var stats = data_properties.get("stats", {})
		var stats_text = ""
		for key in stats:
			stats_text += "%s: %s\n" % [key.capitalize(), str(stats[key])]
		item_stats_label.text = stats_text.strip_edges() if not stats_text.is_empty() else "Žádné statistiky."

	# Handle item icon (assuming item_data contains an icon path)
	if item_icon_texture:
		var icon_path = data_properties.get("icon_path", "")
		if not icon_path.is_empty() and ResourceLoader.exists(icon_path):
			item_icon_texture.texture = load(icon_path)
		else:
			item_icon_texture.texture = null # Clear or set a default placeholder icon

	var coords = data.geometry.coordinates
	is_in_range = GameState.is_in_range(coords[1], coords[0])

	pickup_button.disabled = not is_in_range
	if GameState.player_hero.is_empty():
		pickup_button.disabled = true
		%PickupButtonComment.text = 'Nemáš hrdinu, vytvoř si ho v Bejsce'
		%PickupButtonComment.show()
	else:
		%PickupButtonComment.hide()

	open_modal()


# Handle pickup button press
func _on_pickup_button_pressed():
	if _selected_item_id.is_empty():
		Logger.warning("ItemInfoModal", "Cannot pick up item: No item ID available")
		return

	if not is_in_range:
		Logger.warning("ItemInfoModal", "Cannot pick up item: Item not in range")
		return

	Logger.info("ItemInfoModal", "Picking up item: %s" % _selected_item_id)

	# Call the API to pick up the item
	KokumeApi.pickup_item(_selected_item_id)


# Handle successful item pickup
func _on_item_picked_up(data: Dictionary):
	Logger.info("ItemInfoModal", "Item picked up successfully: %s" % data)

	# Close the modal after successful pickup
	close_modal()


# Handle failed item pickup
func _on_item_pickup_failed(response_code: int, error_data: Dictionary):
	Logger.error("ItemInfoModal", "Failed to pick up item. Code: %d, Error: %s" % [response_code, error_data])
	pickup_button.disabled = false
