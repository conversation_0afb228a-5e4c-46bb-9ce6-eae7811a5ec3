extends Panel<PERSON>ontainer

@onready var avatar_http_request: HTTPRequest = $AvatarHttpRequest
@onready var playerName: Label = $MarginContainer/HBoxContainer/VBoxContainer/PlayerName
@onready var playerID: Label = $MarginContainer/HBoxContainer/VBoxContainer/PlayerID
@onready var avatarTextureRect: TextureRect = $MarginContainer/HBoxContainer/AvatarTextureRect

func _ready():
	# Connect to GameState signal instead of KokumeApi directly
	if GameState:
		GameState.player_data_updated.connect(_on_player_data_updated)
		# Optionally, update with initial state if available
		_on_player_data_updated(GameState.get_player_state())
	else:
		printerr("PlayerInfo: GameState autoload not found!")

	# Ensure the HTTPRequest node exists and connect its signal
	if not avatar_http_request:
		printerr("PlayerInfo: AvatarHttpRequest node not found!")
	else:
		avatar_http_request.request_completed.connect(_on_avatar_download_completed)


# Called when GameState emits player_data_updated
func _on_player_data_updated(player_state: Dictionary):
	playerID.text = "#" + str(player_state.get("id", "N/A"))
	playerName.text = player_state.get("nickname", "Loading...")

	var avatar_url = player_state.get("avatar_url", "")
	if not avatar_url.is_empty():
		_download_avatar(avatar_url)
	else:
		# Clear or set default avatar if URL is empty
		avatarTextureRect.texture = null


func _download_avatar(url: String):
	if not avatar_http_request:
		printerr("Cannot download avatar, HTTPRequest node missing.")
		return

	# Simple check to avoid re-downloading the same URL if texture already exists
	# More robust caching could be added later
	if avatarTextureRect.texture and avatar_http_request.download_file == url:
		return

	print("Downloading avatar from: ", url)
	var http_error = avatar_http_request.request(url)
	if http_error != OK:
		printerr("An error occurred starting the avatar HTTP request: ", http_error)


# Called when the avatar HTTP request is completed.
func _on_avatar_download_completed(result, response_code, headers, body: PackedByteArray):
	if result != HTTPRequest.RESULT_SUCCESS or response_code >= 300:
		printerr("Avatar download failed. Code: ", response_code)
		avatarTextureRect.texture = null # Clear texture on failure
		return

	var image = Image.new()
	# Try loading as WEBP first, then PNG as fallback (adjust based on expected format)
	var image_error = image.load_webp_from_buffer(body)
	if image_error != OK:
		image_error = image.load_png_from_buffer(body) # Try PNG
		if image_error != OK:
			printerr("Failed to load avatar image from downloaded data. Error: ", image_error)
			avatarTextureRect.texture = null
			return

	# Successfully loaded image
	var texture = ImageTexture.create_from_image(image)
	avatarTextureRect.texture = texture
	print("Avatar updated.")
