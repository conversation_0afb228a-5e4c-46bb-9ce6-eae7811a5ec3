extends Control

enum LoginMode { LOGIN, REGISTER, FORGOT_PASSWORD }

@onready var error_label = %<PERSON><PERSON>r<PERSON>abel # Assumes a Label node named <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> exists in the scene
@onready var login_container = %LoginContainer
@onready var register_container = %RegisterContainer
@onready var forgot_password_container = %ForgotPasswordContainer
@onready var title_label = %TitleLabel
@onready var remember_me_checkbox = %RememberMeCheckBox

# Config file path for saved credentials
const CREDENTIALS_CONFIG_PATH = "user://credentials.cfg"
const CONFIG_SECTION = "credentials"
const EMAIL_KEY = "email"
const PASSWORD_KEY = "password"
const REMEMBER_KEY = "remember"

var current_mode: LoginMode = LoginMode.LOGIN

func _ready() -> void:
	KokumeApi.logged_in.connect(_on_logged_in)
	KokumeApi.login_failed.connect(_on_login_failed)
	KokumeApi.registration_completed.connect(_on_registration_completed)
	KokumeApi.registration_failed.connect(_on_registration_failed)
	KokumeApi.password_reset_requested.connect(_on_password_reset_requested)
	KokumeApi.password_reset_failed.connect(_on_password_reset_failed)

	if error_label:
		error_label.visible = false # Hide error label initially

	# Set initial mode
	_set_mode(LoginMode.LOGIN)

	# Load saved credentials if they exist
	_load_saved_credentials()


func _set_mode(mode: LoginMode) -> void:
	current_mode = mode

	# Hide all containers first
	if login_container:
		login_container.visible = false
	if register_container:
		register_container.visible = false
	if forgot_password_container:
		forgot_password_container.visible = false

	# Show the appropriate container based on mode
	match mode:
		LoginMode.LOGIN:
			if title_label:
				title_label.text = "LOGIN"
			if login_container:
				login_container.visible = true

		LoginMode.REGISTER:
			if title_label:
				title_label.text = "REGISTER"
			if register_container:
				register_container.visible = true

		LoginMode.FORGOT_PASSWORD:
			if title_label:
				title_label.text = "FORGOT PASSWORD"
			if forgot_password_container:
				forgot_password_container.visible = true

	# Clear any previous errors
	if error_label:
		error_label.visible = false


func _on_login_button_pressed() -> void:
	if error_label:
		error_label.visible = false # Hide previous errors

	var nickname = %InputNickname.text
	var password = %InputPassword.text
	if nickname.is_empty() or password.is_empty():
		_show_error("Nickname and password cannot be empty.")
		return

	# Save credentials if "Remember Me" is checked
	if remember_me_checkbox and remember_me_checkbox.button_pressed:
		_save_credentials(nickname, password, true)
	else:
		# If "Remember Me" is unchecked, clear any saved credentials
		_clear_saved_credentials()

	# TODO: Add loading indicator
	Logger.info("Login", "Attempting login for user: %s" % nickname)
	KokumeApi.authorize(nickname, password)


func _on_register_button_pressed() -> void:
	if error_label:
		error_label.visible = false # Hide previous errors

	var email = %InputRegEmail.text
	var nickname = %InputRegNickname.text
	var password = %InputRegPassword.text
	var game_password = %InputRegGamePassword.text

	# Validate inputs
	if email.is_empty() or nickname.is_empty() or password.is_empty() or game_password.is_empty():
		_show_error("All fields are required.")
		return

	if password.length() < 6:
		_show_error("Password must be at least 6 characters.")
		return

	if nickname.length() < 3:
		_show_error("Nickname must be at least 3 characters.")
		return

	# Validate email format
	if not _is_valid_email(email):
		_show_error("Please enter a valid email address.")
		return

	# TODO: Add loading indicator
	Logger.info("Login", "Attempting registration for user: %s" % email)
	KokumeApi.register(email, nickname, password, game_password)


func _on_reset_password_button_pressed() -> void:
	if error_label:
		error_label.visible = false # Hide previous errors

	var email = %InputResetEmail.text

	if email.is_empty():
		_show_error("Please enter your email address.")
		return

	if not _is_valid_email(email):
		_show_error("Please enter a valid email address.")
		return

	# TODO: Add loading indicator
	Logger.info("Login", "Requesting password reset for: %s" % email)
	KokumeApi.request_password_reset(email)


func _is_valid_email(email: String) -> bool:
	# Simple email validation regex
	var regex = RegEx.new()
	regex.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
	return regex.search(email) != null


func _on_switch_to_register_pressed() -> void:
	_set_mode(LoginMode.REGISTER)


func _on_switch_to_login_pressed() -> void:
	_set_mode(LoginMode.LOGIN)


func _on_forgot_password_pressed() -> void:
	_set_mode(LoginMode.FORGOT_PASSWORD)


# This method is kept for backward compatibility with existing connections
func _on_button_pressed() -> void:
	_on_login_button_pressed()


func _on_logged_in(_data: Dictionary) -> void:
	Logger.info("Login", "Login successful, fetching player info...")
	# Fetch player info immediately after login success
	KokumeApi.player_info()

	# If we're in login mode and "Remember Me" is checked, credentials were already saved
	# in _on_login_button_pressed() before the API call

	# Switch to the main game scene using the UIManager
	if UIManager:
		UIManager.show_main_game_screen()
	else:
		Logger.error("Login", "UIManager not found! Cannot switch scene.")
		# Fallback or show error to user


func _on_login_failed(response_code: int, error_data: Dictionary) -> void:
	Logger.error("Login", "Login failed! Code: %d, Data: %s" % [response_code, error_data])
	var error_message = "Login failed (%s). " % response_code
	if error_data.has("error_description"):
		error_message += error_data["error_description"]
	elif error_data.has("message"):
		error_message += error_data["message"]
	else:
		error_message += "Please check credentials or network."

	_show_error(error_message)
	# TODO: Clear loading indicator


func _on_registration_completed(_data: Dictionary) -> void:
	Logger.info("Login", "Registration successful, proceeding to login...")

	# Auto-login after successful registration
	var email = %InputRegEmail.text
	var password = %InputRegPassword.text

	# Switch to login mode and attempt login
	_set_mode(LoginMode.LOGIN)

	# Set the login fields with the registration data
	if %InputNickname and %InputPassword:
		%InputNickname.text = email
		%InputPassword.text = password

	# Set "Remember Me" checkbox to checked by default after registration
	if remember_me_checkbox:
		remember_me_checkbox.button_pressed = true

	# Save credentials for new registrations by default
	_save_credentials(email, password, true)

	# Attempt login
	KokumeApi.authorize(email, password)


func _on_registration_failed(response_code: int, error_data: Dictionary) -> void:
	Logger.error("Login", "Registration failed! Code: %d, Data: %s" % [response_code, error_data])
	var error_message = "Registration failed. "
	if error_data.has("error_description"):
		error_message += error_data["error_description"]
	elif error_data.has("message"):
		error_message += error_data["message"]
	else:
		error_message += "Please check your information and try again."

	_show_error(error_message)
	# TODO: Clear loading indicator


func _on_password_reset_requested(_data: Dictionary) -> void:
	Logger.info("Login", "Password reset request successful")

	# Show success message
	_show_error("Password reset instructions have been sent to your email. Please check your inbox.")

	# Switch back to login mode after a delay
	await get_tree().create_timer(3.0).timeout
	_set_mode(LoginMode.LOGIN)


func _on_password_reset_failed(response_code: int, error_data: Dictionary) -> void:
	Logger.error("Login", "Password reset failed! Code: %d, Data: %s" % [response_code, error_data])
	var error_message = "Password reset failed. "
	if error_data.has("error_description"):
		error_message += error_data["error_description"]
	elif error_data.has("message"):
		error_message += error_data["message"]
	else:
		error_message += "Please check your email and try again."

	_show_error(error_message)
	# TODO: Clear loading indicator


func _show_error(message: String) -> void:
	if error_label:
		error_label.text = message
		error_label.visible = true
	else:
		Logger.error("Login", "Error Label node not found in Login scene.")


func _save_credentials(email: String, password: String, remember: bool) -> void:
	var config = ConfigFile.new()

	# Set the values in the config
	config.set_value(CONFIG_SECTION, EMAIL_KEY, email)
	config.set_value(CONFIG_SECTION, PASSWORD_KEY, password)
	config.set_value(CONFIG_SECTION, REMEMBER_KEY, remember)

	# Save the config file
	var err = config.save(CREDENTIALS_CONFIG_PATH)
	if err != OK:
		Logger.error("Login", "Failed to save credentials: %s" % str(err))


func _load_saved_credentials() -> void:
	var config = ConfigFile.new()

	# Try to load the config file
	var err = config.load(CREDENTIALS_CONFIG_PATH)
	if err != OK:
		# File doesn't exist or other error, which is fine for first-time users
		return

	# Check if "Remember Me" was enabled
	var remember = config.get_value(CONFIG_SECTION, REMEMBER_KEY, false)
	if not remember:
		return

	# Get the saved credentials
	var email = config.get_value(CONFIG_SECTION, EMAIL_KEY, "")
	var password = config.get_value(CONFIG_SECTION, PASSWORD_KEY, "")

	# Set the input fields
	if %InputNickname and not email.is_empty():
		%InputNickname.text = email

	if %InputPassword and not password.is_empty():
		%InputPassword.text = password

	# Set the checkbox state
	if remember_me_checkbox:
		remember_me_checkbox.button_pressed = remember


func _clear_saved_credentials() -> void:
	var config = ConfigFile.new()

	# Set remember to false but keep the email for convenience
	if config.load(CREDENTIALS_CONFIG_PATH) == OK:
		var email = config.get_value(CONFIG_SECTION, EMAIL_KEY, "")
		config.set_value(CONFIG_SECTION, EMAIL_KEY, email)
		config.set_value(CONFIG_SECTION, PASSWORD_KEY, "")
		config.set_value(CONFIG_SECTION, REMEMBER_KEY, false)
		config.save(CREDENTIALS_CONFIG_PATH)
