extends "res://scripts/ui/Modal.gd"

# References to UI nodes
@onready var buildings_container: VBoxContainer = %BuildingsContainer
@onready var regions_container: VBoxContainer = %RegionsContainer
@onready var refresh_button: Button = %RefreshButton

# Data storage
var buildings_data: Array = []
var regions_data: Array = []
var buildings_by_region: Dictionary = {}

func _ready():
	super._ready()

	# Connect refresh button
	if refresh_button:
		refresh_button.pressed.connect(_on_refresh_button_pressed)
	else:
		Logger.error("DashboardModal", "RefreshButton node not found!")

func open_and_fill_modal(data: Dictionary):
	Logger.info("DashboardModal", "Opening dashboard modal with data: %s" % str(data))

	# Clear existing data
	_clear_containers()

	# Show loading indicators
	var loading_buildings = Label.new()
	loading_buildings.text = "Loading buildings..."
	loading_buildings.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	buildings_container.add_child(loading_buildings)

	var loading_regions = Label.new()
	loading_regions.text = "Loading regions..."
	loading_regions.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	regions_container.add_child(loading_regions)

	# Fetch data from API
	_fetch_buildings_data()
	_fetch_regions_data()

	# Show the modal
	open_modal()

func _on_refresh_button_pressed():
	Logger.info("DashboardModal", "Refreshing dashboard data")
	_clear_containers()
	_fetch_buildings_data()
	_fetch_regions_data()

func _clear_containers():
	# Clear buildings container
	for child in buildings_container.get_children():
		child.queue_free()

	# Clear regions container
	for child in regions_container.get_children():
		child.queue_free()

	# Reset data
	buildings_data = []
	regions_data = []
	buildings_by_region = {}

func _fetch_buildings_data():
	# Create HTTP request for buildings
	var http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_buildings_request_completed)

	# Make the request
	var headers = ["Authorization: Bearer " + KokumeApi.access_token]
	var error = http_request.request(KokumeApi.API_URL + KokumeApi.API_BASE_V1 + "/building", headers)

	if error != OK:
		Logger.error("DashboardModal", "An error occurred in the HTTP request for buildings: %s" % error)
		_show_error_in_container(buildings_container, "Failed to load buildings")

func _fetch_regions_data():
	# Create HTTP request for regions
	var http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_regions_request_completed)

	# Make the request
	var headers = ["Authorization: Bearer " + KokumeApi.access_token]
	var error = http_request.request(KokumeApi.API_URL + KokumeApi.API_BASE_V1 + "/region/player", headers)

	if error != OK:
		Logger.error("DashboardModal", "An error occurred in the HTTP request for regions: %s" % error)
		_show_error_in_container(regions_container, "Failed to load regions")

func _on_buildings_request_completed(result, response_code, _headers, body):
	# Clear loading indicator
	_clear_containers()

	if result != HTTPRequest.RESULT_SUCCESS or response_code != 200:
		Logger.error("DashboardModal", "Buildings request failed with code: %d" % response_code)
		_show_error_in_container(buildings_container, "Failed to load buildings")
		return

	# Parse JSON response
	var json = JSON.new()
	var error = json.parse(body.get_string_from_utf8())
	if error != OK:
		Logger.error("DashboardModal", "JSON Parse Error: %s" % error)
		_show_error_in_container(buildings_container, "Failed to parse buildings data")
		return

	# Get buildings data
	buildings_data = json.get_data()

	# Organize buildings by region
	_organize_buildings_by_region()

	# Display buildings
	_display_buildings()

func _on_regions_request_completed(result, response_code, _headers, body):
	if result != HTTPRequest.RESULT_SUCCESS or response_code != 200:
		Logger.error("DashboardModal", "Regions request failed with code: %d" % response_code)
		_show_error_in_container(regions_container, "Failed to load regions")
		return

	# Parse JSON response
	var json = JSON.new()
	var error = json.parse(body.get_string_from_utf8())
	if error != OK:
		Logger.error("DashboardModal", "JSON Parse Error: %s" % error)
		_show_error_in_container(regions_container, "Failed to parse regions data")
		return

	# Get regions data
	regions_data = json.get_data()

	# Display regions
	_display_regions()

func _organize_buildings_by_region():
	buildings_by_region.clear()

	for building in buildings_data:
		var region_id = building.get("region_id", -1)
		if region_id != -1:
			if not buildings_by_region.has(region_id):
				buildings_by_region[region_id] = []
			buildings_by_region[region_id].append(building)

func _display_buildings():
	if buildings_data.is_empty():
		var label = Label.new()
		label.text = "No buildings found"
		label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		buildings_container.add_child(label)
		return

	# Create accordion for buildings by region
	for region_id in buildings_by_region:
		var region_buildings = buildings_by_region[region_id]
		var region_name = "Unknown Region"

		# Try to find region name
		for region in regions_data:
			if region.get("id") == region_id:
				region_name = region.get("name", "Unknown Region")
				break

		# Create region panel
		var region_panel = PanelContainer.new()
		region_panel.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		buildings_container.add_child(region_panel)

		var region_vbox = VBoxContainer.new()
		region_panel.add_child(region_vbox)

		# Region header
		var region_header = Label.new()
		region_header.text = region_name + " (" + str(region_buildings.size()) + " buildings)"
		region_header.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		region_vbox.add_child(region_header)

		# Buildings list
		var buildings_list = VBoxContainer.new()
		region_vbox.add_child(buildings_list)

		for building in region_buildings:
			var building_item = HBoxContainer.new()
			buildings_list.add_child(building_item)

			var building_name = Label.new()
			building_name.text = building.get("name", "Unknown Building")
			building_name.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			building_item.add_child(building_name)

			var building_type = Label.new()
			building_type.text = building.get("type", {}).get("name", "Unknown Type")
			building_item.add_child(building_type)

			var building_state = Label.new()
			var state = building.get("state", "unknown")
			building_state.text = state
			building_item.add_child(building_state)

		# Add separator
		var separator = HSeparator.new()
		buildings_container.add_child(separator)

func _display_regions():
	if regions_data.is_empty():
		var label = Label.new()
		label.text = "No controlled regions found"
		label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		regions_container.add_child(label)
		return

	# Create list of regions
	for region in regions_data:
		var region_item = HBoxContainer.new()
		regions_container.add_child(region_item)

		var region_name = Label.new()
		region_name.text = region.get("name", "Unknown Region")
		region_name.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		region_item.add_child(region_name)

		var region_buildings_count = Label.new()
		var count = 0
		if buildings_by_region.has(region.get("id")):
			count = buildings_by_region[region.get("id")].size()
		region_buildings_count.text = str(count) + " buildings"
		region_item.add_child(region_buildings_count)

func _show_error_in_container(container: Control, error_message: String):
	for child in container.get_children():
		child.queue_free()

	var error_label = Label.new()
	error_label.text = error_message
	error_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	container.add_child(error_label)
