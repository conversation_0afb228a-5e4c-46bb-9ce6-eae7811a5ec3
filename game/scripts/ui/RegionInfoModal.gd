extends "res://scripts/ui/Modal.gd"

@onready var region_owner_label: Label = %RegionOwnerLabel
@onready var resource_list_container: VBoxContainer = %ResourceListContainer

func _ready() -> void:
	super._ready()
	KokumeApi.region_loaded.connect(_region_data_loaded)

# Called when the modal is opened with specific region data
func open_and_fill_modal(data: Dictionary):
	var data_properties = data.get("properties", {})
	if data_properties.is_empty():
		Logger.warning("UI", "RegionInfoModal: Received null region data.")
		close_modal()
		return

	if data_properties.has('id'):
		KokumeApi.get_region(int(data_properties.get('id')))

	if title_label:
		title_label.text = "Region: %s" % data_properties.get("name")

	if region_owner_label:
		if data_properties.owner != null:
			region_owner_label.text = "Vládce: %s #%s" % [data_properties.owner.nickname, data_properties.owner.id]
		else:
			region_owner_label.text = "Volné <PERSON>"

	super.open_modal()


func _region_data_loaded(data: Dictionary):
	print(data)
	if resource_list_container:
		# Clear previous resource entries
		for child in resource_list_container.get_children():
			child.queue_free()

		var resources = data.get("resource_amounts", {})
		if resources is Array and not resources.is_empty():
			
			var header = Label.new()
			header.text = "Zdroje na území:"
			resource_list_container.add_child(header)

			
			for resource:Dictionary in resources:
				var amount = resource.amount
				var resource_label = Label.new()
				# Capitalize resource name for display
				resource_label.text = "  %s: %s" % [resource.type.name, amount]
				resource_list_container.add_child(resource_label)

				# Alternative: Instantiate a custom scene for each resource line
				# var resource_line = ResourceLineScene.instantiate()
				# resource_line.set_resource_data(resource_name, amount) # Assuming ResourceLine scene has this method
				# resource_list_container.add_child(resource_line)
		else:
			# Optional: Display a message if no resources
			var no_resource_label = Label.new()
			no_resource_label.text = "Nic tu není..."
			resource_list_container.add_child(no_resource_label)
