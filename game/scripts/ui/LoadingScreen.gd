extends Control

@onready var progress_bar: ProgressBar = $VBoxContainer/ProgressBar
@onready var label: Label = $VBoxContainer/Label

# Define the scene to load after this screen
const TARGET_SCENE_PATH = "res://scenes/ui/Login.tscn"

var loading_status = ResourceLoader.THREAD_LOAD_INVALID_RESOURCE

func _ready():
	# Start loading the target scene immediately when this loading screen is ready
	progress_bar.value = 0.0
	load_scene(TARGET_SCENE_PATH) # Trigger the load automatically

func load_scene(scene_path: String):
	if not scene_path:
		Logger.error("LoadingScreen", "No target scene path provided.")
		# Optionally, transition to an error scene or show an error message
		return

	# target_scene_path = scene_path # No longer needed as it's a const
	Logger.info("LoadingScreen", "Starting threaded load for: %s" % scene_path)

	# Start background loading
	ResourceLoader.load_threaded_request(scene_path)
	loading_status = ResourceLoader.THREAD_LOAD_IN_PROGRESS
	
	# Start monitoring progress in the process loop
	set_process(true)

func _process(delta):
	if loading_status == ResourceLoader.THREAD_LOAD_IN_PROGRESS:
		var progress_array = [] # Required argument for get_progress (Godot 4)
		loading_status = ResourceLoader.load_threaded_get_status(TARGET_SCENE_PATH, progress_array)

		if loading_status == ResourceLoader.THREAD_LOAD_IN_PROGRESS:
			if progress_array.size() > 0:
				progress_bar.value = progress_array[0] * 100 # Progress is 0.0 to 1.0
				# Optional: Update label text with percentage
				# label.text = "Loading... %d%%" % int(progress_bar.value)
			# Wait for the next frame to avoid blocking the main thread
			# Use await get_tree().process_frame instead of yield in Godot 4+
			# However, await can only be used in async functions.
			# Since _process isn't async, we rely on its frame-by-frame nature.
			# If loading is very fast, this might look jumpy. A timer could smooth it.
			
		elif loading_status == ResourceLoader.THREAD_LOAD_LOADED:
			Logger.info("LoadingScreen", "Scene loaded successfully: %s" % TARGET_SCENE_PATH)
			set_process(false) # Stop processing progress updates
			var loaded_resource = ResourceLoader.load_threaded_get(TARGET_SCENE_PATH)
			if loaded_resource is PackedScene:
				get_tree().change_scene_to_packed(loaded_resource)
			else:
				Logger.error("LoadingScreen", "Loaded resource is not a PackedScene: %s" % TARGET_SCENE_PATH)
				# Handle error - maybe show an error message or go to a default scene
				
		elif loading_status == ResourceLoader.THREAD_LOAD_FAILED:
			Logger.error("LoadingScreen", "Failed to load scene: %s" % TARGET_SCENE_PATH)
			set_process(false) # Stop processing progress updates
			# Handle error - maybe show an error message or go to a default scene

		elif loading_status == ResourceLoader.THREAD_LOAD_INVALID_RESOURCE:
			Logger.error("LoadingScreen", "Invalid resource path: %s" % TARGET_SCENE_PATH)
			set_process(false) # Stop processing progress updates
			# Handle error
