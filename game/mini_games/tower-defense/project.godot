; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Kokume - tower defense"
run/main_scene="res://scenes/boot.tscn"
config/features=PackedStringArray("4.4", "GL Compatibility")
config/icon="res://icon.svg"

[autoload]

GameState="*res://scripts/autoload/GameState.gd"
KokumeApi="*res://scripts/autoload/KokumeApi.gd"

[rendering]

renderer/rendering_method="gl_forward_plus"
renderer/rendering_method.mobile="gl_forward_plus"
display/window/stretch/mode="viewport"
display/window/stretch/aspect="expand"
display/window/size/viewport_width=1920
display/window/size/viewport_height=1080
display/window/size/resizable=true
