[gd_scene load_steps=3 format=3 uid="uid://cq6phw64nbf1v"]

[ext_resource type="Script" uid="uid://bkffcgk87twxc" path="res://scripts/game/ui/components/TerrainInfo.gd" id="1_yzabc"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4defg"]
bg_color = Color(0.2, 0.2, 0.2, 1)

[node name="TerrainInfo" type="Control"]
visible = false
custom_minimum_size = Vector2(180, 250)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_yzabc")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4defg")

[node name="TitleLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 40.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 16
horizontal_alignment = 1
vertical_alignment = 1

[node name="InfoLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 50.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 14
autowrap_mode = 2
