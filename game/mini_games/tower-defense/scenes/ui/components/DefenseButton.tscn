[gd_scene load_steps=3 format=3 uid="uid://daxw7qf1w8y8k"]

[ext_resource type="Script" uid="uid://de3ft5d0uidv0" path="res://scripts/game/ui/components/DefenseButton.gd" id="1_h5wbn"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f513g"]
bg_color = Color(0.2, 0.2, 0.2, 1)

[node name="DefenseButton" type="Control"]
custom_minimum_size = Vector2(180, 60)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_h5wbn")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_f513g")

[node name="NameLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 10.0
offset_top = 10.0
offset_right = 200.0
offset_bottom = 36.0
theme_override_font_sizes/font_size = 16
text = "Defense Name"
vertical_alignment = 1

[node name="CostLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 10.0
offset_top = 36.0
offset_right = 200.0
offset_bottom = 59.0
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_font_sizes/font_size = 14
text = "Cena: 0"
vertical_alignment = 1

[connection signal="gui_input" from="Background" to="." method="_on_background_input"]
[connection signal="mouse_entered" from="Background" to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="Background" to="." method="_on_mouse_exited"]
