[gd_scene load_steps=3 format=3 uid="uid://b1x2y304a5b6c"]

[ext_resource type="Script" uid="uid://cca54yxnh0y8r" path="res://scripts/game/ui/components/DoneButton.gd" id="1_cdefg"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2hijk"]
bg_color = Color(0.301961, 0.686275, 0.313726, 1)

[node name="DoneButton" type="Control"]
custom_minimum_size = Vector2(180, 40)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_cdefg")

[node name="ButtonBg" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_2hijk")

[node name="ShadowText" type="Label" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 1.0
offset_top = 1.0
offset_right = 1.0
offset_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.180392, 0.490196, 0.196078, 0.5)
theme_override_font_sizes/font_size = 22
text = "HOTOVO"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonText" type="Label" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 22
text = "HOTOVO"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="gui_input" from="ButtonBg" to="." method="_on_button_input"]
[connection signal="mouse_entered" from="ButtonBg" to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="ButtonBg" to="." method="_on_mouse_exited"]
