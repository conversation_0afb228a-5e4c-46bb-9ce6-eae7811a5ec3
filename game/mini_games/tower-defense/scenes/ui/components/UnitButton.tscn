[gd_scene load_steps=4 format=3 uid="uid://c6g0w7q8y1h2f"]

[ext_resource type="Script" path="res://scripts/game/ui/components/UnitButton.gd" id="1_5t7k4"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t3w0g"]
bg_color = Color(0.266667, 0.266667, 0.266667, 1)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_q5k1v"]

[node name="UnitButton" type="Control"]
layout_mode = 3
anchors_preset = 0
offset_right = 180.0
offset_bottom = 100.0
script = ExtResource("1_5t7k4")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_t3w0g")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.0555556
anchor_top = 0.1
anchor_right = 0.722222
anchor_bottom = 0.9
offset_right = 4.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 4
mouse_filter = 2

[node name="NameLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Unit Name"
theme_override_font_sizes/font_size = 16
mouse_filter = 2

[node name="StatsLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Stat 1: Value
Stat 2: Value
Stat 3: Value"
theme_override_colors/font_color = Color(0.666667, 0.666667, 0.666667, 1)
theme_override_font_sizes/font_size = 12
theme_override_styles/normal = SubResource("StyleBoxEmpty_q5k1v")
mouse_filter = 2

[node name="CountLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.777778
anchor_top = 0.1
anchor_right = 0.944444
anchor_bottom = 0.3
offset_left = -8.33331
offset_right = -1.52588e-05
grow_horizontal = 0
text = "x99"
theme_override_font_sizes/font_size = 16
horizontal_alignment = 2
vertical_alignment = 1
mouse_filter = 2