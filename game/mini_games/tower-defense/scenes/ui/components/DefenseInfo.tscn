[gd_scene load_steps=4 format=3 uid="uid://c6w0h4x5q8y8j"]

[ext_resource type="Script" path="res://scripts/game/ui/components/DefenseInfo.gd" id="1_abcde"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1fghij"]
bg_color = Color(0.2, 0.2, 0.2, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.533333, 0.533333, 0.533333, 1)

[node name="DefenseInfo" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
visible = false
script = ExtResource("1_abcde")

[node name="ModalBackground" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 1 # Stop mouse events
color = Color(0, 0, 0, 0.5)

[node name="WindowBackground" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1fghij")

[connection signal="gui_input" from="ModalBackground" to="." method="_on_modal_background_input"]