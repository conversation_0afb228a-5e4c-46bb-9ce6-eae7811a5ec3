extends Node

# Game configuration constants
# Converted from JavaScript/TypeScript to GDScript

class_name GameConfig

# Grid configuration
const GRID = {
	"WIDTH": 7,
	"HEIGHT": 7,
	"TILE_SIZE": 10, # meters
	"CELL_SIZE": 64  # pixels
}

# UI configuration
const UI = {
	"PADDING": 10,
	"LEFT_PANEL_WIDTH": 200,
	"RIGHT_PANEL_WIDTH": 200,
	"TOP_PANEL_HEIGHT": 70
}

# Tower configuration
const TOWER = {
	"NAME": "Hlavní věž",
	"POSITION": {
		"X": 3,
		"Y": 3
	}
}

# Terrain types configuration
const TERRAIN_TYPES = {
	"FOREST": {
		"name": "Les",
		"defenseBonus": 0.20,
		"attackPenalty": 0.15,
		"movementCost": 1.2,
		"texture": "terrain_forest",
		"tint": Color("8fa894")  # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, méně syt<PERSON>ele<PERSON>
	},
	"WATER": {
		"name": "Voda",
		"defenseBonus": 0.0,
		"attackPenalty": 0.30,
		"movementCost": 1.5,
		"texture": "terrain_water",
		"tint": Color("b3ccd7")  # Velmi světlá, jemná modrá
	},
	"MEADOW": {
		"name": "Louka",
		"defenseBonus": 0.10,
		"attackPenalty": 0.05,
		"movementCost": 1.0,
		"texture": "terrain_meadow",
		"tint": Color("c5d1bc")  # Velmi světlá, našedlá zelená
	},
	"MOUNTAIN": {
		"name": "Hory",
		"defenseBonus": 0.30,
		"attackPenalty": 0.20,
		"movementCost": 1.8,
		"texture": "terrain_mountain",
		"tint": Color("c2c2c2")  # Světlejší šedá
	},
	"SWAMP": {
		"name": "Bažina",
		"defenseBonus": 0.05,
		"attackPenalty": 0.25,
		"movementCost": 1.6,
		"texture": "terrain_swamp",
		"tint": Color("a5aa9c")  # Světlejší olivově šedá
	},
	"DESERT": {
		"name": "Poušť",
		"defenseBonus": 0.05,
		"attackPenalty": 0.10,
		"movementCost": 1.3,
		"texture": "terrain_desert",
		"tint": Color("e8e0d1")  # Velmi světlá písková
	},
	"SNOW": {
		"name": "Sníh",
		"defenseBonus": 0.15,
		"attackPenalty": 0.20,
		"movementCost": 1.4,
		"texture": "terrain_snow",
		"tint": Color("f0f0f0")  # Téměř bílá s jemným nádechem šedé
	}
}

# Path configuration
const PATH = {
	"name": "Cesta",
	"description": "Cesta určuje trasu, kudy budou přicházet nepřátelské jednotky. Musí vést od okraje mapy k věži.",
	"cost": 100,
	"textures": {
		"horizontal": "path_horizontal",
		"vertical": "path_vertical",
		"corner_tr": "path_corner_tr",
		"corner_tl": "path_corner_tl",
		"corner_br": "path_corner_br",
		"corner_bl": "path_corner_bl",
		"t_top": "path_t_top",
		"t_right": "path_t_right",
		"t_bottom": "path_t_bottom",
		"t_left": "path_t_left",
		"cross": "path_cross",
		"single": "path_single"
	},
	"tint": Color("bdb69c"),
	"completeTint": Color("00ff00")
}

# Game types enum
enum GameType {
	DEFENDER,
	ATTACKER
}
