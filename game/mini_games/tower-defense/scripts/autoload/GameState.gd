extends Node

# GameState singleton for managing game state
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")
const GameMapClass = preload("res://scripts/game/GameMap.gd")

# Game state variables
var game_type: int  # GameConfig.GameType enum
var map  # GameMap instance
var available_defenses: Dictionary = {}  # Dictionary instead of Map
var available_units: Dictionary = {}  # Dictionary instead of Map
var resources: int = 0
var tower_health: int = 0
var path_config: Dictionary

# Signal for state changes
signal resources_changed(new_amount)
signal tower_health_changed(new_health)
signal game_state_loaded

func _ready():
	# Initialize with default values
	pass

# Initialize the game state with data
func initialize(data: Dictionary) -> void:
	# Create new game map
	map = GameMapClass.new(data.map, self)
	resources = data.resources
	tower_health = data.tower.health
	
	# Initialize available defenses
	available_defenses.clear()
	if data.has("availableDefenses"):
		for key in data.availableDefenses:
			available_defenses[key] = data.availableDefenses[key]
	
	# Initialize path config
	path_config = data.get("path", {
		"uuid": "PATH",
		"name": "Cesta",
		"cost": 100,
		"damage": 0,
		"description": "Cesta pro jednotky",
		"texture": "path_single",
		"canPlaceOnPath": false,
		"canPlaceOnTerrain": true,
		"health": 100,
		"tint": Color("bdb69c")
	})
	
	# Initialize available units
	available_units.clear()
	if data.has("units"):
		for unit in data.units:
			available_units[unit.uuid] = {
				"uuid": unit.uuid,
				"count": unit.count,
				"name": unit.name,
				"description": unit.description,
				"texture": unit.texture,
				"health": unit.health,
				"damage": unit.damage,
				"speed": unit.speed
			}
	
	game_type = data.gameType
	emit_signal("game_state_loaded")

# Get the current game type
func get_game_type() -> int:
	return game_type

# Get the game map
func get_map():
	return map

# Get current resources
func get_resources() -> int:
	return resources

# Add resources (positive or negative amount)
func add_resources(amount: int) -> void:
	resources += amount
	emit_signal("resources_changed", resources)

# Get tower health
func get_tower_health() -> int:
	return tower_health

# Get defense configuration by UUID
func get_defense_config(uuid: String) -> Dictionary:
	if not available_defenses.has(uuid):
		push_error("Defense with uuid " + uuid + " not found")
		return {}
	
	return available_defenses[uuid]

# Get path configuration
func get_path_config() -> Dictionary:
	return path_config

# Get all available defenses
func get_available_defenses() -> Array:
	var result = []
	for key in available_defenses:
		result.append([key, available_defenses[key]])
	return result

# Check if a defense can be added at a position
func can_add_defense(x: int, y: int, uuid: String) -> bool:
	var defense_config = get_defense_config(uuid)
	if defense_config.empty():
		return false
	return map.can_add_defense(x, y, defense_config)

# Add units of a specific type
func add_unit(uuid: String, count: int) -> void:
	if available_units.has(uuid):
		available_units[uuid].count += count
	else:
		# Could add new unit type here if needed
		pass

# Use a unit (decrement count)
func use_unit(type: String) -> bool:
	if not available_units.has(type) or available_units[type].count <= 0:
		return false
	
	available_units[type].count -= 1
	if available_units[type].count <= 0:
		available_units.erase(type)
	
	return true

# Get all available units
func get_available_units() -> Array:
	var result = []
	for key in available_units:
		result.append(available_units[key])
	return result

# Update tower health (apply damage)
func update_tower_health(damage: int) -> void:
	tower_health = max(0, tower_health - damage)
	emit_signal("tower_health_changed", tower_health)

# Serialize game state to dictionary
func serialize() -> Dictionary:
	var defense_entries = []
	for key in available_defenses:
		defense_entries.append([key, available_defenses[key]])
	
	var unit_entries = []
	for key in available_units:
		unit_entries.append([key, available_units[key]])
	
	return {
		"gameType": game_type,
		"resources": resources,
		"towerHealth": tower_health,
		"availableDefenses": defense_entries,
		"units": unit_entries,
		"map": map.serialize(),
		"path": path_config
	}

# Deserialize game state from dictionary
func deserialize(data: Dictionary) -> void:
	game_type = data.gameType
	resources = data.resources
	tower_health = data.towerHealth
	
	# Convert defense entries to dictionary
	available_defenses.clear()
	for entry in data.availableDefenses:
		available_defenses[entry[0]] = entry[1]
	
	# Convert unit entries to dictionary
	available_units.clear()
	for entry in data.units:
		available_units[entry[0]] = entry[1]
	
	path_config = data.get("path", path_config)
	map.deserialize(data.map)
	
	emit_signal("game_state_loaded")