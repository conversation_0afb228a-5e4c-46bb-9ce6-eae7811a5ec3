extends Node

# KokumeApi singleton for API communication
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# API configuration
var api_url: String = "https://kokume.eu/api/v1"  # TODO: Replace with the actual API URL
var tower_uuid: String = "test-tower"  # TODO: Replace with dynamic
var use_mock_data: bool = true  # Using mock data for development

# HTTP request node for API calls
var http_request: HTTPRequest

# Signal for data loading
signal data_loaded(data)
signal data_error(error_message)

func _ready():
	# Create HTTP request node
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.connect("request_completed", Callable(self, "_on_request_completed"))

# Get initial game data
func get_initial_data() -> void:
	if use_mock_data:
		# Use mock data
		var mock_data = get_mock_initial_data()
		call_deferred("emit_signal", "data_loaded", mock_data)
		return
	
	# Real API implementation
	# Note: In a real implementation, we would need to handle these promises properly
	# For now, we'll just use the mock data
	get_tower(tower_uuid)  # Fixed: removed assignment to variable
	
	# This is a placeholder for the real API implementation
	push_error("Real API implementation not yet complete, using mock data")
	var mock_data = get_mock_initial_data()
	call_deferred("emit_signal", "data_loaded", mock_data)

# Get tower data
func get_tower(uuid: String) -> void:
	if use_mock_data:
		var mock_data = get_mock_tower_data(uuid)
		call_deferred("emit_signal", "data_loaded", mock_data)
		return
	
	# Make API request
	var url = api_url + "/tower/" + uuid
	http_request.request(url)
	# Response will be handled in _on_request_completed

# Get defenses data
func get_defences() -> void:
	if use_mock_data:
		var mock_data = get_mock_defences_data()
		call_deferred("emit_signal", "data_loaded", mock_data)
		return
	
	# Make API request
	var url = api_url + "/defenses"
	http_request.request(url)
	# Response will be handled in _on_request_completed

# Get units data
func get_units() -> void:
	if use_mock_data:
		var mock_data = get_mock_units_data()
		call_deferred("emit_signal", "data_loaded", mock_data)
		return
	
	# Make API request
	var url = api_url + "/units"
	http_request.request(url)
	# Response will be handled in _on_request_completed

# Decrement unit count
func decrement_unit_count(unit_type: String) -> bool:
	# TODO: Implement actual API call
	push_warning("decrement_unit_count is not yet implemented")
	return true

# Handle HTTP request completion
func _on_request_completed(result, response_code, headers, body):
	if result != HTTPRequest.RESULT_SUCCESS:
		push_error("HTTP Request failed with result: " + str(result))
		emit_signal("data_error", "HTTP Request failed")
		return
	
	if response_code != 200:
		push_error("HTTP Request returned non-200 status code: " + str(response_code))
		emit_signal("data_error", "HTTP Request returned error status: " + str(response_code))
		return
	
	# Parse JSON response
	var json = JSON.new()
	var error = json.parse(body.get_string_from_utf8())
	if error != OK:
		push_error("JSON Parse Error: " + json.get_error_message())
		emit_signal("data_error", "Failed to parse response data")
		return
	
	var response_data = json.get_data()
	emit_signal("data_loaded", response_data)

# Fetch with retry (helper method for real API calls)
func fetch_with_retry(endpoint: String, retries: int = 3) -> void:
	var url = api_url + endpoint
	var retry_count = 0
	
	# In GDScript, we would need to implement this differently
	# For now, this is just a placeholder
	http_request.request(url)
	# The actual retry logic would need to be implemented in _on_request_completed

# MOCK DATA METHODS

# Get mock initial data
func get_mock_initial_data() -> Dictionary:
	return {
		"tower": {
			"health": 1000,
			"range": 1,
			"damage": 50
		},
		"defenses": [
			{ "uuid": "wall", "x": 5, "y": 5, "health": 500 },
			{ "uuid": "tower", "x": 7, "y": 2, "health": 300, "range": 3, "damage": 50 }
		],
		"units": _map_mock_units(get_mock_units_data()),
		"map": {
			"grid": [
				["WATER", "DESERT", "DESERT", "MEADOW", "FOREST", "MEADOW", "WATER"],
				["FOREST", "SWAMP", "SWAMP", "MEADOW", "MOUNTAIN", "MEADOW", "MEADOW"],
				["DESERT", "DESERT", "FOREST", "MEADOW", "FOREST", "DESERT", "FOREST"],
				["FOREST", "MOUNTAIN", "SNOW", "MEADOW", "FOREST", "MOUNTAIN", "DESERT"],
				["MEADOW", "MEADOW", "SNOW", "SNOW", "FOREST", "MOUNTAIN", "DESERT"],
				["SNOW", "FOREST", "FOREST", "MOUNTAIN", "SNOW", "SWAMP", "MOUNTAIN"],
				["SWAMP", "MEADOW", "SNOW", "WATER", "MOUNTAIN", "MOUNTAIN", "MEADOW"]
			],
			"tower": {
				"x": 3,
				"y": 3
			},
			"paths": [
				"0,3", "1,3", "1,2", "1,1", "2,1", "3,1", "3,2", "4,3", "5,3", "6,3"
			],
			"defenses": [
				["2,2", {"uuid": "tower", "health": 300}],
				["4,2", {"uuid": "tower", "health": 300}],
				["3,2", {"uuid": "spikes", "health": 100}],
				["3,1", {"uuid": "spikes", "health": 100}],
				["4,3", {"uuid": "spikes", "health": 100}]
			]
		},
		"gameType": 0,  # 0 = Defender, 1 = Attacker
		"path": {
			"uuid": "PATH",
			"name": "Cesta",
			"cost": 100,
			"damage": 0,
			"description": "Cesta pro jednotky",
			"texture": "path_single",
			"canPlaceOnPath": false,
			"canPlaceOnTerrain": true,
			"health": 100,
			"tint": Color("ffffff")
		},
		"availableDefenses": {
			"tower": {
				"uuid": "tower",
				"name": "Věž",
				"cost": 300,
				"damage": 20,
				"range": 1.5,
				"attackSpeed": 1000,
				"description": "Útočná věž s dlouhým dosahem",
				"texture": "defense_tower",
				"canPlaceOnPath": false,
				"canPlaceOnTerrain": true,
				"health": 300,
				"tint": Color("ffffff")
			},
			"spikes": {
				"uuid": "spikes",
				"name": "Bodce",
				"cost": 100,
				"damage": 50,
				"description": "Bodce instantně poškozí příchozí jednotky",
				"texture": "defense_spikes",
				"canPlaceOnPath": true,
				"canPlaceOnTerrain": false,
				"health": 100,
				"tint": Color("ffffff")
			}
		},
		"resources": 0
	}

# Helper function to map mock units
func _map_mock_units(units: Array) -> Array:
	var mapped_units = []
	for unit in units:
		mapped_units.append({
			"uuid": unit.type,  # Use 'type' as 'uuid'
			"name": unit.name,
			"count": unit.count,
			"description": unit.description,
			"texture": unit.texture,
			"health": unit.health,
			"damage": unit.damage,
			"speed": unit.speed
		})
	return mapped_units

# Generate mock grid
func generate_mock_grid() -> Array:
	var grid = []
	var terrain_types = GameConfig.TERRAIN_TYPES.keys()
	
	for y in range(GameConfig.GRID.HEIGHT):
		grid.append([])
		for x in range(GameConfig.GRID.WIDTH):
			# Skip tower position
			if x == GameConfig.TOWER.POSITION.X and y == GameConfig.TOWER.POSITION.Y:
				grid[y].append("MEADOW")  # Default terrain for tower
				continue
			
			# Random terrain selection
			var random_index = randi() % terrain_types.size()
			grid[y].append(terrain_types[random_index])
	
	return grid

# Get mock tower data
func get_mock_tower_data(uuid: String) -> Dictionary:
	return {
		"uuid": uuid,
		"name": "Test Tower",
		"owner": "Test User",
		"health": 100,
		"map": {
			"grid": generate_mock_grid(),
			"tower": { "x": GameConfig.TOWER.POSITION.X, "y": GameConfig.TOWER.POSITION.Y },
			"paths": ["0,3", "1,3", "2,3", "3,4", "3,5", "3,6"],
			"lockedPaths": [],
			"defenses": []
		}
	}

# Get mock defences data
func get_mock_defences_data() -> Array:
	return [
		{ "uuid": "wall", "x": 5, "y": 5, "health": 500 },
		{ "uuid": "tower", "x": 7, "y": 2, "health": 300, "range": 3, "damage": 50 }
	]

# Get mock units data
func get_mock_units_data() -> Array:
	return [
		{
			"type": "LIGHT_INFANTRY",
			"name": "Lehká pěchota",
			"health": 100,
			"damage": 20,
			"speed": 4,
			"description": "Rychlá, ale slabá jednotka",
			"texture": "light_infantry",
			"count": 5
		},
		{
			"type": "HEAVY_INFANTRY",
			"name": "Těžká pěchota",
			"health": 200,
			"damage": 30,
			"speed": 3,
			"description": "Odolná jednotka s průměrným poškozením",
			"texture": "heavy_infantry",
			"count": 3
		},
		{
			"type": "DEMOLISHER",
			"name": "Demoliční četa",
			"health": 150,
			"damage": 50,
			"speed": 2,
			"description": "Specializovaná na ničení budov",
			"texture": "demolisher",
			"count": 2
		},
		{
			"type": "TEST",
			"name": "Testovací",
			"health": 150,
			"damage": 50,
			"speed": 40,
			"description": "Super rychlá jednotka na testování",
			"texture": "test",
			"count": 10
		}
	]