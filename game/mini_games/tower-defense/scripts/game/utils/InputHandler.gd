extends Node

# InputHandler class for handling grid-based input
# This utility class converts mouse input to grid coordinates

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Signals
signal tile_clicked(x, y)
signal tile_hovered(x, y)
signal tile_unhovered

# Parent scene reference
var scene

# Last hovered tile
var last_hovered_x: int = -1
var last_hovered_y: int = -1

# Called when the node enters the scene tree for the first time
func _ready():
	scene = get_parent()
	set_process_input(true)

# Handle input events
func _input(event: InputEvent) -> void:
	if event is InputEventMouseMotion:
		handle_mouse_motion(event)
	elif event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		handle_mouse_click(event)

# Handle mouse motion
func handle_mouse_motion(event: InputEventMouseMotion) -> void:
	# Get game container from parent scene
	var game_container = scene.game_container
	
	# Convert mouse position to grid coordinates
	var world_point = game_container.get_global_transform_with_canvas().affine_inverse() * event.position
	var tile_x = int(world_point.x / GameConfig.GRID.CELL_SIZE)
	var tile_y = int(world_point.y / GameConfig.GRID.CELL_SIZE)
	
	# Check if pointer is within valid game area
	if tile_x >= 0 and tile_x < GameConfig.GRID.WIDTH and tile_y >= 0 and tile_y < GameConfig.GRID.HEIGHT:
		# Check if we moved to a new tile
		if tile_x != last_hovered_x or tile_y != last_hovered_y:
			last_hovered_x = tile_x
			last_hovered_y = tile_y
			emit_signal("tile_hovered", tile_x, tile_y)
	else:
		# If we moved out of the grid
		if last_hovered_x != -1 or last_hovered_y != -1:
			last_hovered_x = -1
			last_hovered_y = -1
			emit_signal("tile_unhovered")

# Handle mouse click
func handle_mouse_click(event: InputEventMouseButton) -> void:
	# Get game container from parent scene
	var game_container = scene.game_container
	
	# Convert mouse position to grid coordinates
	var world_point = game_container.get_global_transform_with_canvas().affine_inverse() * event.position
	var tile_x = int(world_point.x / GameConfig.GRID.CELL_SIZE)
	var tile_y = int(world_point.y / GameConfig.GRID.CELL_SIZE)
	
	# Check if pointer is within valid game area
	if tile_x >= 0 and tile_x < GameConfig.GRID.WIDTH and tile_y >= 0 and tile_y < GameConfig.GRID.HEIGHT:
		emit_signal("tile_clicked", tile_x, tile_y)