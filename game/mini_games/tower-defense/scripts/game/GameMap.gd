extends Resource

# GameMap class for managing the game map
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Map properties
var grid: Array = []  # 2D array of terrain types
var tower: Dictionary = {"x": 0, "y": 0}  # Tower position
var defenses: Dictionary = {}  # Dictionary instead of Map
var paths: Array = []  # Array of path coordinates
var game_state  # Reference to GameState

# Constructor
func _init(data = null, state = null):
	game_state = state
	
	if data:
		fill(data)
	else:
		grid = []
		tower = {"x": GameConfig.TOWER.POSITION.X, "y": GameConfig.TOWER.POSITION.Y}
		paths = []
		defenses = {}

# Generate a random grid
static func generate_grid() -> Array:
	var grid = []
	var terrain_types = GameConfig.TERRAIN_TYPES.keys()
	
	for y in range(GameConfig.GRID.HEIGHT):
		grid.append([])
		for x in range(GameConfig.GRID.WIDTH):
			# Skip tower position
			if x == GameConfig.TOWER.POSITION.X and y == GameConfig.TOWER.POSITION.Y:
				grid[y].append("MEADOW")  # Default terrain for tower
				continue
			
			# Random terrain selection
			var random_index = randi() % terrain_types.size()
			grid[y].append(terrain_types[random_index])
	
	return grid

# Fill map with data
func fill(data: Dictionary) -> void:
	grid = data.grid
	tower = data.tower
	
	# Convert paths from array to set-like array
	paths = []
	if data.has("paths"):
		for path in data.paths:
			if not path in paths:
				paths.append(path)
	
	# Convert defenses from array to dictionary
	defenses = {}
	if data.has("defenses"):
		for defense_entry in data.defenses:
			defenses[defense_entry[0]] = defense_entry[1]

# Get terrain at position
func get_terrain_at(x: int, y: int) -> String:
	if not is_valid_position(x, y):
		return ""
	return grid[y][x]

# Get all terrains
func get_terrains() -> Array:
	return grid

# Get terrain effect at position
func get_terrain_effect(x: int, y: int) -> Dictionary:
	var terrain = get_terrain_at(x, y)
	if terrain.empty():
		return {}
	return GameConfig.TERRAIN_TYPES.get(terrain, {})

# Check if position is valid
func is_valid_position(x: int, y: int) -> bool:
	return x >= 0 and x < GameConfig.GRID.WIDTH and y >= 0 and y < GameConfig.GRID.HEIGHT

# Add path at position
func add_path(x: int, y: int) -> void:
	var key = "%d,%d" % [x, y]
	if not key in paths:
		paths.append(key)

# Check if position has path
func has_path(x: int, y: int) -> bool:
	var key = "%d,%d" % [x, y]
	return key in paths

# Get all paths
func get_paths() -> Array:
	return paths

func is_tower(x: int, y: int) -> bool:
	return x == GameConfig.TOWER.POSITION.X and y == GameConfig.TOWER.POSITION.Y

# Get movement cost at position
func get_movement_cost(x: int, y: int) -> float:
	if has_path(x, y):
		return 0.8  # Paths are faster than normal terrain
	
	var effect = get_terrain_effect(x, y)
	return effect.get("movementCost", 1.0)

# Get defense bonus at position
func get_defense_bonus(x: int, y: int) -> float:
	var effect = get_terrain_effect(x, y)
	return effect.get("defenseBonus", 0.0)

# Get attack penalty at position
func get_attack_penalty(x: int, y: int) -> float:
	var effect = get_terrain_effect(x, y)
	return effect.get("attackPenalty", 0.0)

# Check if position is at edge of map
func is_edge_position(x: int, y: int) -> bool:
	return x == 0 or x == GameConfig.GRID.WIDTH - 1 or y == 0 or y == GameConfig.GRID.HEIGHT - 1

# Get path connections at position
func get_path_connections(x: int, y: int) -> Dictionary:
	return {
		"top": has_path(x, y - 1),
		"right": has_path(x + 1, y),
		"bottom": has_path(x, y + 1),
		"left": has_path(x - 1, y)
	}

# Remove path at key
func remove_path(key: String) -> void:
	if key in paths:
		paths.erase(key)

# Find path to tower from start position
func find_path_to_tower(start_x: int, start_y: int) -> Array:
	var visited = []
	var queue = []
	
	# Create initial path
	var initial_path = [{"x": start_x, "y": start_y}]
	queue.append({"x": start_x, "y": start_y, "path": initial_path})
	visited.append("%d,%d" % [start_x, start_y])
	
	while queue.size() > 0:
		var current = queue.pop_front()
		var x = current.x
		var y = current.y
		var path = current.path
		
		# Check if adjacent to tower
		if is_adjacent_to_tower(x, y):
			return path
		
		# Explore neighbors
		for neighbor in get_neighbors(x, y):
			var key = "%d,%d" % [neighbor.x, neighbor.y]
			if not key in visited and has_path(neighbor.x, neighbor.y):
				visited.append(key)
				
				# Create new path by copying and adding neighbor
				var new_path = path.duplicate()
				new_path.append({"x": neighbor.x, "y": neighbor.y})
				
				queue.append({"x": neighbor.x, "y": neighbor.y, "path": new_path})
	
	return []  # No path found

# Find path to edge from start position
func find_path_to_edge(start_x: int, start_y: int) -> Array:
	var visited = []
	var queue = []
	
	# Create initial path
	var initial_path = [{"x": start_x, "y": start_y}]
	queue.append({"x": start_x, "y": start_y, "path": initial_path})
	visited.append("%d,%d" % [start_x, start_y])
	
	while queue.size() > 0:
		var current = queue.pop_front()
		var x = current.x
		var y = current.y
		var path = current.path
		
		# Check if at edge
		if is_edge_position(x, y):
			return path
		
		# Explore neighbors
		for neighbor in get_neighbors(x, y):
			var key = "%d,%d" % [neighbor.x, neighbor.y]
			if not key in visited and has_path(neighbor.x, neighbor.y):
				visited.append(key)
				
				# Create new path by copying and adding neighbor
				var new_path = path.duplicate()
				new_path.append({"x": neighbor.x, "y": neighbor.y})
				
				queue.append({"x": neighbor.x, "y": neighbor.y, "path": new_path})
	
	return []  # No path found

# Check if position is adjacent to tower
func is_adjacent_to_tower(x: int, y: int) -> bool:
	# Check if directly adjacent to tower (not diagonally)
	return (
		# Same row, column adjacent
		(y == GameConfig.TOWER.POSITION.Y and abs(x - GameConfig.TOWER.POSITION.X) == 1) or
		# Same column, row adjacent
		(x == GameConfig.TOWER.POSITION.X and abs(y - GameConfig.TOWER.POSITION.Y) == 1)
	)

# Get neighboring positions
func get_neighbors(x: int, y: int) -> Array:
	var neighbors = [
		{"x": x - 1, "y": y},
		{"x": x + 1, "y": y},
		{"x": x, "y": y - 1},
		{"x": x, "y": y + 1}
	]
	
	# Filter valid positions
	var valid_neighbors = []
	for n in neighbors:
		if n.x >= 0 and n.x < GameConfig.GRID.WIDTH and n.y >= 0 and n.y < GameConfig.GRID.HEIGHT:
			valid_neighbors.append(n)
	
	return valid_neighbors

# Check if path can be built at position
func can_build_path_at(x: int, y: int) -> bool:
	if not is_valid_position(x, y):
		return false
	
	if has_path(x, y):
		return false
	
	if x == GameConfig.TOWER.POSITION.X and y == GameConfig.TOWER.POSITION.Y:
		return false
	
	return true

# Check if defense can be added at position
func can_add_defense(x: int, y: int, defense_config: Dictionary) -> bool:
	# Check if position is occupied by main tower
	if x == GameConfig.TOWER.POSITION.X and y == GameConfig.TOWER.POSITION.Y:
		return false
	
	if has_defense(x, y):
		return false
	
	var is_path = has_path(x, y)
	
	if is_path and defense_config.get("canPlaceOnPath", false):
		return true
	
	if not is_path and defense_config.get("canPlaceOnTerrain", false):
		return true
	
	return false

# Add defense at position
func add_defense(x: int, y: int, defense_config: Dictionary) -> bool:
	if not can_add_defense(x, y, defense_config):
		return false
	
	var key = "%d,%d" % [x, y]
	defenses[key] = {
		"uuid": defense_config.uuid,
		"health": defense_config.health
	}
	
	if game_state:
		game_state.add_resources(-defense_config.cost)
	
	return true

# Check if position has defense
func has_defense(x: int, y: int) -> bool:
	var key = "%d,%d" % [x, y]
	return defenses.has(key)

# Get defense at position
func get_defense_at(x: int, y: int) -> Dictionary:
	var key = "%d,%d" % [x, y]
	if defenses.has(key):
		return defenses[key]
	return {}

# Remove defense at position
func remove_defense(x: int, y: int) -> bool:
	var key = "%d,%d" % [x, y]
	var defense = defenses.get(key, null)
	if not defense:
		return false
	
	if game_state and defense:
		var defense_config = game_state.get_defense_config(defense.uuid)
		game_state.add_resources(floor(defense_config.cost))
	
	defenses.erase(key)
	return true

# Get all defenses
func get_all_defenses() -> Dictionary:
	return defenses

# Serialize map to dictionary
func serialize() -> Dictionary:
	var defense_entries = []
	for key in defenses:
		defense_entries.append([key, defenses[key]])
	
	return {
		"grid": grid,
		"tower": tower,
		"defenses": defense_entries,
		"paths": paths
	}

# Deserialize map from dictionary
func deserialize(data: Dictionary) -> void:
	grid = data.grid
	tower = data.tower
	
	# Convert defenses from array to dictionary
	defenses = {}
	for entry in data.defenses:
		defenses[entry[0]] = entry[1]
	
	# Convert paths from array to set-like array
	paths = []
	for path in data.paths:
		if not path in paths:
			paths.append(path)
