extends Node2D

class_name TerrainTile

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Helper function to convert hex color string to Color object
static func hex_to_color(hex_string: String) -> Color:
	if hex_string.begins_with("#"):
		hex_string = hex_string.substr(1)
	# Ensure the string has the correct length
	if hex_string.length() != 6:
		printerr("Invalid hex color string: ", hex_string)
		return Color.MAGENTA # Return an obvious error color
	var r = hex_string.substr(0, 2).hex_to_int() / 255.0
	var g = hex_string.substr(2, 2).hex_to_int() / 255.0
	var b = hex_string.substr(4, 2).hex_to_int() / 255.0
	return Color(r, g, b)

# Drawing functions (ported from TypeScript)
static func draw_tree(image: Image, x: int, y: int, size: int, color: Color) -> void:
	var half_size = size / 2
	var third_size = size / 3
	var eighth_size = size / 8
	var quarter_size = size / 4

	# Triangle top
	var points = PackedVector2Array([
		Vector2(x - half_size, y + third_size),
		Vector2(x + half_size, y + third_size),
		Vector2(x, y - half_size)
	])
	# Approximate triangle fill by setting pixels (Godot Image lacks direct polygon fill)
	# This is a simplified approach; a proper polygon fill algorithm would be more complex.
	var min_x = x - half_size
	var max_x = x + half_size
	var min_y = y - half_size
	var max_y = y + third_size
	for py in range(min_y, max_y + 1):
		for px in range(min_x, max_x + 1):
			# Basic bounding box check first
			if px >= 0 and px < image.get_width() and py >= 0 and py < image.get_height():
				if Geometry2D.is_point_in_polygon(Vector2(px, py), points):
					image.set_pixel(px, py, color)

	# Trunk (rectangle)
	var trunk_x = x - eighth_size
	var trunk_y = y + third_size
	var trunk_w = quarter_size
	var trunk_h = third_size
	for py in range(trunk_y, trunk_y + trunk_h):
		for px in range(trunk_x, trunk_x + trunk_w):
			if px >= 0 and px < image.get_width() and py >= 0 and py < image.get_height():
				image.set_pixel(px, py, color)


static func draw_waves(image: Image, size: int, color: Color) -> void:
	var amplitude = size / 16 # Reduced amplitude compared to TS for less overlap
	var frequency = PI * 4 # Increased frequency
	for row in range(1, 3): # Draw 2 wave lines
		var base_y = row * size / 3 # Adjusted spacing
		for x in range(size):
			var wave_y = base_y + sin(float(x) / size * frequency) * amplitude
			var y_int = int(round(wave_y))
			# Draw thicker lines (2px)
			for dy in range(-1, 2):
				var current_y = y_int + dy
				if x >= 0 and x < image.get_width() and current_y >= 0 and current_y < image.get_height():
					image.set_pixel(x, current_y, color)


static func draw_grass(image: Image, size: int, color: Color) -> void:
	var base_y = int(size * 0.8)
	var heights = [int(size * 0.3), int(size * 0.4), int(size * 0.3)]
	var positions = [int(size * 0.3), int(size * 0.5), int(size * 0.7)]

	for i in range(3):
		var start_x = positions[i]
		var end_y = base_y - heights[i]
		# Draw vertical line (2px thick)
		for x_offset in range(-1, 1): # Make line slightly thicker
			var current_x = start_x + x_offset
			if current_x >= 0 and current_x < image.get_width():
				for y in range(end_y, base_y + 1):
					if y >= 0 and y < image.get_height():
						image.set_pixel(current_x, y, color)


static func draw_peaks(image: Image, size: int, color: Color) -> void:
	var points = PackedVector2Array([
		Vector2(0, size -1), # Bottom-left
		Vector2(size / 2, size / 3), # Top-middle
		Vector2(size - 1, size - 1) # Bottom-right
	])
	# Approximate triangle fill
	var min_x = 0
	var max_x = size - 1
	var min_y = size / 3
	var max_y = size - 1
	for py in range(min_y, max_y + 1):
		for px in range(min_x, max_x + 1):
			# Basic bounding box check first
			if px >= 0 and px < image.get_width() and py >= 0 and py < image.get_height():
				if Geometry2D.is_point_in_polygon(Vector2(px, py), points):
					image.set_pixel(px, py, color)


static func draw_bubbles(image: Image, size: int, color: Color) -> void:
	var bubbles = [
		{"x": int(size * 0.3), "y": int(size * 0.3), "r": int(size * 0.15)},
		{"x": int(size * 0.7), "y": int(size * 0.5), "r": int(size * 0.2)},
		{"x": int(size * 0.4), "y": int(size * 0.7), "r": int(size * 0.1)}
	]

	for bubble in bubbles:
		var cx = bubble["x"]
		var cy = bubble["y"]
		var r = bubble["r"]
		var r_squared = r * r
		var r_inner_sq = (r - 2) * (r - 2) # For thickness

		for y in range(cy - r, cy + r + 1):
			for x in range(cx - r, cx + r + 1):
				# Basic bounding box check first
				if x >= 0 and x < image.get_width() and y >= 0 and y < image.get_height():
					var dist_sq = (x - cx) * (x - cx) + (y - cy) * (y - cy)
					# Draw a circle outline (approx 2px thick)
					if dist_sq <= r_squared and dist_sq >= r_inner_sq:
						image.set_pixel(x, y, color)


static func draw_dunes(image: Image, size: int, color: Color) -> void:
	var amplitude = size / 12
	var frequency = PI * 4
	for i in range(3):
		var y_offset = int((size / 3.0) * i + (size / 4.0))
		for x in range(size):
			# Draw wavy line
			var dune_y = y_offset + sin(float(x) / size * frequency) * amplitude
			var y_int = int(round(dune_y))
			# Draw thicker lines (2px)
			for dy in range(-1, 2):
				var current_y = y_int + dy
				if x >= 0 and x < image.get_width() and current_y >= 0 and current_y < image.get_height():
					image.set_pixel(x, current_y, color)

	# Add some dots like in the original
	var rng = RandomNumberGenerator.new()
	rng.randomize() # Ensure different results each time
	for _i in range(5):
		var x = rng.randi_range(0, size - 1)
		var y = rng.randi_range(0, size - 1)
		if x >= 0 and x < image.get_width() and y >= 0 and y < image.get_height():
			image.set_pixel(x, y, color)
			# Make dot slightly larger
			if x + 1 < image.get_width(): image.set_pixel(x + 1, y, color)
			if y + 1 < image.get_height(): image.set_pixel(x, y + 1, color)
			if x + 1 < image.get_width() and y + 1 < image.get_height(): image.set_pixel(x + 1, y + 1, color)


static func draw_snowflakes(image: Image, size: int, color: Color) -> void:
	var center = size / 2.0
	var length = size / 3.0
	var thickness = 1 # Half-thickness

	for i in range(3): # 3 lines make a 6-pointed star
		var angle = float(i) * PI / 3.0
		var cos_angle = cos(angle)
		var sin_angle = sin(angle)

		var start_x = center - cos_angle * length
		var start_y = center - sin_angle * length
		var end_x = center + cos_angle * length
		var end_y = center + sin_angle * length

		# Bresenham's line algorithm or similar needed for non-axis-aligned lines
		# Using a simplified line drawing approach for now
		var x1 = int(round(start_x))
		var y1 = int(round(start_y))
		var x2 = int(round(end_x))
		var y2 = int(round(end_y))

		var dx = abs(x2 - x1)
		var dy = -abs(y2 - y1) # Use negative dy for standard algorithm form
		var sx = 1 if x1 < x2 else -1
		var sy = 1 if y1 < y2 else -1
		var err = dx + dy # Error value e_xy

		var x = x1
		var y = y1

		while true:
			# Draw pixel (with thickness)
			for tx in range(-thickness, thickness + 1):
				for ty in range(-thickness, thickness + 1):
					var px = x + tx
					var py = y + ty
					if px >= 0 and px < image.get_width() and py >= 0 and py < image.get_height():
						image.set_pixel(px, py, color)

			if x == x2 and y == y2:
				break

			var e2 = 2 * err
			if e2 >= dy: # e_xy+e_x > 0
				err += dy
				x += sx
			if e2 <= dx: # e_xy+e_y < 0
				err += dx
				y += sy

# Terrain properties
var grid_x: int
var grid_y: int
var terrain_type: String
var terrain_config: Dictionary
var effects_config: Dictionary # Keep this if used elsewhere, otherwise can be removed

# Visual components
var sprite: Sprite2D
var hover_overlay: ColorRect
var decorations: Node2D # Keep this if used elsewhere, otherwise can be removed

# Effect components (Keep these if used elsewhere, otherwise can be removed)
var ambient_effects: Node2D
var combat_effects: Node2D
var ambient_particles: GPUParticles2D
var combat_particles: GPUParticles2D

# Signals
signal terrain_hover_start(terrain)
signal terrain_hover_end(terrain)

# Called when the node enters the scene tree for the first time
func _ready():
	var rectangle_shape = RectangleShape2D.new()
	rectangle_shape.size = Vector2(GameConfig.GRID.CELL_SIZE, GameConfig.GRID.CELL_SIZE)

	# Use Area2D for mouse enter/exit and potentially clicks
	var area = Area2D.new()
	area.position = Vector2(GameConfig.GRID.CELL_SIZE / 2, GameConfig.GRID.CELL_SIZE / 2)
	var area_shape = CollisionShape2D.new() # Area2D needs its own shape
	area_shape.shape = rectangle_shape # Use for the Area2D
	area.add_child(area_shape)
	area.input_pickable = true # Make sure it can detect mouse events
	area.mouse_entered.connect(_on_mouse_entered)
	area.mouse_exited.connect(_on_mouse_exited)
	# Connect input_event for clicks if needed:
	# area.input_event.connect(_on_area_input_event)
	add_child(area)


# Initialize the terrain tile
func initialize(x: int, y: int, type: String) -> void:
	grid_x = x
	grid_y = y
	terrain_type = type # Store the type key (e.g., "FOREST")
	terrain_config = GameConfig.TERRAIN_TYPES.get(type, {})
	if terrain_config.is_empty():
		printerr("Invalid terrain type provided to initialize: ", type)
		terrain_type = "MEADOW" # Default to meadow if type is invalid
		terrain_config = GameConfig.TERRAIN_TYPES.get(terrain_type, {})

	# Set position
	position = Vector2(x * GameConfig.GRID.CELL_SIZE, y * GameConfig.GRID.CELL_SIZE)

	# Create visual components
	create_visuals()

# Create visual components (Procedural Version)
func create_visuals() -> void:
	var cell_size = GameConfig.GRID.CELL_SIZE
	var base_color = terrain_config.get("tint", Color(1, 1, 1))

	# Define symbol colors based on TypeScript code (adjust as needed)
	# Note: These hex codes are derived from the TS example
	var symbol_colors = {
		"FOREST": hex_to_color("7a8a7f"),
		"MEADOW": hex_to_color("b5c1ac"),
		"WATER": hex_to_color("9ebbc7"),
		"MOUNTAIN": hex_to_color("b2b2b2"),
		"SWAMP": hex_to_color("959a8c"),
		"DESERT": hex_to_color("d8d0c1"),
		"SNOW": hex_to_color("e0e0e0")
	}
	# Use the actual terrain type string from the instance variable
	var symbol_color = symbol_colors.get(terrain_type, Color(0,0,0,0)) # Default transparent

	# Create Image and fill with base color
	var image = Image.create(cell_size, cell_size, false, Image.FORMAT_RGBA8)
	if image == null:
		printerr("Failed to create image for terrain tile.")
		return
	image.fill(base_color)

	# Draw symbols based on terrain type
	# Use the actual terrain type string from the instance variable
	match terrain_type:
		"FOREST": # Match the keys used in GameConfig.gd
			draw_tree(image, cell_size / 4, cell_size / 2, cell_size / 4, symbol_color)
			draw_tree(image, cell_size * 3 / 4, cell_size / 3, cell_size / 4, symbol_color)
		"WATER":
			draw_waves(image, cell_size, symbol_color)
		"MEADOW":
			draw_grass(image, cell_size, symbol_color)
		"MOUNTAIN":
			draw_peaks(image, cell_size, symbol_color)
		"SWAMP":
			draw_bubbles(image, cell_size, symbol_color)
		"DESERT":
			draw_dunes(image, cell_size, symbol_color)
		"SNOW":
			draw_snowflakes(image, cell_size, symbol_color)
		_:
			printerr("Unknown terrain type for procedural generation: ", terrain_type)


	# Create ImageTexture from the generated Image
	var texture = ImageTexture.create_from_image(image)
	if texture == null:
		printerr("Failed to create texture from image for terrain tile.")
		return

	# Create base sprite
	sprite = Sprite2D.new()
	sprite.texture = texture
	sprite.position = Vector2(cell_size / 2, cell_size / 2) # Center sprite in the node
	# sprite.modulate = base_color # Tint is now the base color, no need to modulate again
	add_child(sprite)

	# Create hover overlay
	hover_overlay = ColorRect.new()
	hover_overlay.size = Vector2(cell_size, cell_size)
	hover_overlay.color = Color(1, 1, 1, 0)  # Transparent white
	add_child(hover_overlay)


# Handle mouse enter
func _on_mouse_entered() -> void:
	hover_overlay.color = Color(1, 1, 1, 0.2)
	emit_signal("terrain_hover_start", self)

# Handle mouse exit
func _on_mouse_exited() -> void:
	hover_overlay.color = Color(1, 1, 1, 0)
	emit_signal("terrain_hover_end", self)

# Get terrain effects
func get_effects() -> Dictionary:
	return {
		"defenseBonus": terrain_config.get("defenseBonus", 0.0),
		"attackPenalty": terrain_config.get("attackPenalty", 0.0),
		"movementCost": terrain_config.get("movementCost", 1.0)
	}

# Input event handling (for clicks, requires Area2D input_event signal)
# Connect this to the Area2D's input_event signal if click detection is needed
func _on_area_input_event(viewport, event, shape_idx):
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		print("Terrain clicked: ", grid_x, ", ", grid_y) # Example action
		get_tree().call_group("terrain_listeners", "on_terrain_clicked", self)
