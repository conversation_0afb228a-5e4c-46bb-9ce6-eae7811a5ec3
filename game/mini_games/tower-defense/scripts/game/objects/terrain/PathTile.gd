extends Sprite2D

@export var x: int
@export var y: int

@onready var image
@onready var image_texture

func _ready():
	update_texture()

func update_texture():
	var connections = get_path_connections()
	
	var cell_size = GameConfig.GRID.CELL_SIZE
	var path_width = cell_size * 0.4
	image = Image.create(cell_size, cell_size, false, Image.FORMAT_RGBA8)
	image.fill(Color(0, 0, 0, 0)) # Transparent background
	
	var path_color = Color(GameConfig.PATH.tint)
	
	# Draw connections
	if connections.top:
		image.fill_rect(Rect2(cell_size / 2 - path_width / 2, 0, path_width, cell_size / 2), path_color)
	if connections.right:
		image.fill_rect(Rect2(cell_size / 2, cell_size / 2 - path_width / 2, cell_size / 2, path_width), path_color)
	if connections.bottom:
		image.fill_rect(Rect2(cell_size / 2 - path_width / 2, cell_size / 2, path_width, cell_size / 2), path_color)
	if connections.left:
		image.fill_rect(Rect2(0, cell_size / 2 - path_width / 2, cell_size / 2, path_width), path_color)
	
	# Draw center
	image.fill_rect(Rect2(cell_size / 2 - path_width / 2, cell_size / 2 - path_width / 2, path_width, path_width), path_color)
	
	image_texture = ImageTexture.create_from_image(image)
	texture = image_texture

func get_path_connections() -> Dictionary:
	var map = GameState.get_map()
	return {
		"top": map.has_path(x, y - 1) or map.is_tower(x, y - 1) or y == 0,
		"right": map.has_path(x + 1, y) or map.is_tower(x + 1, y) or x == GameConfig.GRID.WIDTH - 1,
		"bottom": map.has_path(x, y + 1) or map.is_tower(x, y + 1) or y == GameConfig.GRID.HEIGHT - 1,
		"left": map.has_path(x - 1, y) or map.is_tower(x - 1, y) or x == 0
	}
