extends CharacterBody2D

# Unit class for game units
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Unit properties
var id: String
var health: float
var max_health: float
var damage: float
var speed: float
var current_speed: float
var original_color: Color

# Path properties
var path: Array = []
var path_progress: float = 0.0
var path_curve: Curve2D
var path_node: Path2D # Add Path2D node
var path_follow: PathFollow2D

# Visual components
var shape: ColorRect
var health_bar: Node2D

# Signal for unit events
signal unit_destroyed(unit)
signal unit_damaged(unit, amount)

# Constructor
func _init():
	# Generate a unique ID
	id = str(randi())

# Called when the node enters the scene tree for the first time
func _ready():
	# Set up the unit
	# path_curve = Curve2D.new() # Moved initialization to initialize()
	# path_follow = PathFollow2D.new() # Moved initialization to initialize()
	# add_child(path_follow) # Moved to initialize()
	pass # Add pass to make the empty function valid

# Initialize the unit with configuration
func initialize(x: int, y: int, config: Dictionary) -> void:
	# Set basic properties
	health = config.get("health", 100)
	max_health = health
	damage = config.get("damage", 10)
	speed = config.get("speed", 1)  # kilometers per hour
	current_speed = speed
	original_color = config.get("color", Color(1, 1, 1))  # Default white
	
	# Position will be set in AttackScene after adding to tree
	# position = Vector2(...)
	
	# Create visual representation
	create_shape()
	
	# Create health bar
	create_health_bar()
	
	# Initialize path nodes before creating the path
	path_curve = Curve2D.new()
	path_node = Path2D.new() # Create Path2D
	add_child(path_node) # Add Path2D to the Unit
	path_follow = PathFollow2D.new()
	path_node.add_child(path_follow) # Add PathFollow2D as child of Path2D
	
	# Create path
	make_path(x, y)

# Create the visual shape of the unit
func create_shape() -> void:
	shape = ColorRect.new()
	shape.size = Vector2(32, 32)
	shape.color = original_color
	shape.position = Vector2(-16, -16)  # Center the shape
	add_child(shape)

# Create the health bar
func create_health_bar() -> void:
	health_bar = Node2D.new()
	add_child(health_bar)
	update_health_bar()

# Update the health bar display
func update_health_bar() -> void:
	# Clear existing health bar
	for child in health_bar.get_children():
		child.queue_free()
	
	# Background (red)
	var background = ColorRect.new()
	background.size = Vector2(30, 4)
	background.position = Vector2(-15, -20)
	background.color = Color(1, 0, 0)  # Red
	health_bar.add_child(background)
	
	# Health (green)
	var health_rect = ColorRect.new()
	var health_width = max(0, (health / max_health) * 30)
	health_rect.size = Vector2(health_width, 4)
	health_rect.position = Vector2(-15, -20)
	health_rect.color = Color(0, 1, 0)  # Green
	health_bar.add_child(health_rect)

# Create a path for the unit to follow
func make_path(x: int, y: int) -> void:
	# Get the game state and map using the global autoload name
	# var game_state = get_node("/root/GameState") # Accessing via get_node might be too early
	if not is_instance_valid(GameState):
		push_error("GameState autoload is not valid!")
		return
	var path_array = GameState.get_map().find_path_to_tower(x, y)
	
	if path_array.size() == 0:
		push_error("No path found")
		return
	
	# Path starts relative to the Unit's position (which is already set)
	# The Path2D curve points are relative to the Path2D node itself.
	
	# Create the path
	path_curve.clear_points()
	path_curve.add_point(Vector2(0, 0)) # Start at the Unit's origin
	
	# Add points from path (absolute world coords, centered in cell)
	for point in path_array:
		path_curve.add_point(Vector2(
			point.x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
			point.y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
		))
	
	# Add tower position as final point (absolute world coords, centered in cell)
	path_curve.add_point(Vector2(
		GameConfig.TOWER.POSITION.X * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
		GameConfig.TOWER.POSITION.Y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	))
	
	
	# Assign the generated curve to the Path2D node
	path_node.curve = path_curve
	
	# Set up path follow properties (it will use the parent Path2D's curve)
	# path_follow.curve = path_curve # Incorrect: PathFollow2D doesn't have this property
	path_follow.loop = false
	path_progress = 0.0
# Move along the path
func move_along_path(delta: float) -> void:
	if path_curve == null or path_curve.get_point_count() == 0:
		return
	
	# Calculate movement speed
	var meters_per_second = current_speed * 1000.0 / 3600.0
	var pixels_per_second = meters_per_second * (GameConfig.GRID.CELL_SIZE / GameConfig.GRID.TILE_SIZE)
	
	# Update progress
	path_progress += (pixels_per_second * delta) / path_curve.get_baked_length()
	
	if path_progress >= 1.0:
		# Unit reached the end of the path
		emit_signal("unit_destroyed", self)
		queue_free()
		return
	
	# Update position
	path_follow.progress_ratio = path_progress
	position = path_follow.position

# Take damage
func take_damage(amount: float) -> void:
	health -= amount
	update_health_bar()
	emit_signal("unit_damaged", self, amount)
	
	if health <= 0:
		# Convert to grid coordinates
		var grid_x = int(position.x / GameConfig.GRID.CELL_SIZE)
		var grid_y = int(position.y / GameConfig.GRID.CELL_SIZE)
		
		# Get the attack scene and create destruction effect
		var attack_scene = get_parent()
		if attack_scene.has_method("create_destruction_effect"):
			attack_scene.create_destruction_effect({"x": grid_x, "y": grid_y})
		
		# Remove from scene's units collection
		if attack_scene.has_method("remove_unit"):
			attack_scene.remove_unit(id)
		
		# Destroy the unit
		queue_free()

# Apply slow effect
func slow(percentage: float) -> void:
	current_speed = speed * (1.0 - percentage)
	
	# Slow effect - change color to blue
	shape.color = Color(0, 0, 1)  # Blue
	
	# Reset speed after delay
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 2.0
	timer.one_shot = true
	timer.connect("timeout", Callable(self, "_on_slow_timeout"))
	timer.start()

# Called when slow effect times out
func _on_slow_timeout() -> void:
	current_speed = speed
	shape.color = original_color

# Process frame
func _process(delta: float) -> void:
	move_along_path(delta)
