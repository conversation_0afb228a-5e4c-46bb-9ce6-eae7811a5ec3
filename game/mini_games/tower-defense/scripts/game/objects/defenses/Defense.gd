extends Node2D

# Defense class for defensive structures
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Defense properties
var uuid: String
var health: int
var max_health: int
var grid_x: int
var grid_y: int
var defense_config: Dictionary
var sprite: Sprite2D
var health_bar: ProgressBar
var game_state

# Signal for defense events
signal defense_damaged(defense, damage)
signal defense_destroyed(defense)

# Initialize the defense
func _init(x: int, y: int, config: Dictionary, state):
	grid_x = x
	grid_y = y
	defense_config = config
	game_state = state
	
	uuid = config.get("uuid", "")
	health = config.get("health", 100)
	max_health = health
	
	# Set position based on grid coordinates
	position = Vector2(
		x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
		y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	)
	
	# Create visual representation
	create_visuals()

# Create visual elements
func create_visuals() -> void:
	# Create sprite
	sprite = Sprite2D.new()
	
	# Try to load texture
	var texture_path = "res://assets/textures/defenses/%s.png" % defense_config.get("texture", "default_defense")
	var texture = load(texture_path)
	
	if texture:
		sprite.texture = texture
	else:
		# Create placeholder if texture not found
		var placeholder = create_placeholder_texture()
		sprite.texture = placeholder
	
	# Apply tint if specified
	if defense_config.has("tint"):
		sprite.modulate = defense_config.tint
	
	add_child(sprite)
	
	# Create health bar
	create_health_bar()

# Create a placeholder texture if the defense texture is not found
func create_placeholder_texture() -> ImageTexture:
	var image = Image.create(GameConfig.GRID.CELL_SIZE, GameConfig.GRID.CELL_SIZE, false, Image.FORMAT_RGBA8)
	
	# Fill with base color
	image.fill(Color("666666"))
	
	# Draw border
	for x in range(GameConfig.GRID.CELL_SIZE):
		for y in range(GameConfig.GRID.CELL_SIZE):
			if x == 0 or y == 0 or x == GameConfig.GRID.CELL_SIZE - 1 or y == GameConfig.GRID.CELL_SIZE - 1:
				image.set_pixel(x, y, Color("888888"))
	
	# Create texture from image
	var texture = ImageTexture.create_from_image(image)
	return texture

# Create health bar
func create_health_bar() -> void:
	health_bar = ProgressBar.new()
	health_bar.min_value = 0
	health_bar.max_value = max_health
	health_bar.value = health
	health_bar.size = Vector2(GameConfig.GRID.CELL_SIZE - 10, 5)
	health_bar.position = Vector2(-health_bar.size.x / 2, GameConfig.GRID.CELL_SIZE / 2 + 5)
	
	# Set custom stylebox for the progress bar
	var bg_style = StyleBoxFlat.new()
	bg_style.bg_color = Color("333333")
	health_bar.add_theme_stylebox_override("background", bg_style)
	
	var fg_style = StyleBoxFlat.new()
	fg_style.bg_color = Color("00ff00")
	health_bar.add_theme_stylebox_override("fill", fg_style)
	
	add_child(health_bar)

# Apply damage to the defense
func apply_damage(damage: int) -> void:
	var terrain_bonus = game_state.get_map().get_defense_bonus(grid_x, grid_y)
	var actual_damage = int(damage * (1.0 - terrain_bonus))
	
	health -= actual_damage
	health = max(0, health)
	
	# Update health bar
	health_bar.value = health
	
	# Update health bar color based on health percentage
	var health_percent = float(health) / max_health
	var fg_style = StyleBoxFlat.new()
	
	if health_percent > 0.6:
		fg_style.bg_color = Color("00ff00")  # Green
	elif health_percent > 0.3:
		fg_style.bg_color = Color("ffff00")  # Yellow
	else:
		fg_style.bg_color = Color("ff0000")  # Red
	
	health_bar.add_theme_stylebox_override("fill", fg_style)
	
	# Emit signal
	emit_signal("defense_damaged", self, actual_damage)
	
	# Check if destroyed
	if health <= 0:
		emit_signal("defense_destroyed", self)
		queue_free()

# Get defense type
func get_defense_type() -> String:
	return uuid

# Get defense configuration
func get_defense_config() -> Dictionary:
	return defense_config

# Get current health
func get_health() -> int:
	return health

# Get maximum health
func get_max_health() -> int:
	return max_health

# Get grid position
func get_grid_position() -> Vector2:
	return Vector2(grid_x, grid_y)

# Get defense range
func get_range() -> int:
	return defense_config.get("range", 0)

# Get defense damage
func get_damage() -> int:
	return defense_config.get("damage", 0)

# Check if unit is in range
func is_unit_in_range(unit_x: int, unit_y: int) -> bool:
	var range_value = get_range()
	if range_value <= 0:
		return false
	
	var dx = abs(grid_x - unit_x)
	var dy = abs(grid_y - unit_y)
	
	# Manhattan distance for grid-based range
	return dx + dy <= range_value

# Serialize defense to dictionary
func serialize() -> Dictionary:
	return {
		"uuid": uuid,
		"health": health,
		"x": grid_x,
		"y": grid_y
	}

# Deserialize defense from dictionary
func deserialize(data: Dictionary) -> void:
	uuid = data.uuid
	health = data.health
	grid_x = data.x
	grid_y = data.y
	
	# Update health bar
	if health_bar:
		health_bar.value = health