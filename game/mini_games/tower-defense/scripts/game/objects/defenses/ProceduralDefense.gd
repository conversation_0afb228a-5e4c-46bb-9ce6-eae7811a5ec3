extends Node2D

const GameConfig = preload("res://config/GameConfig.gd")

var defense_config: Dictionary
var defense_type: String = "" # e.g., "tower", "spikes"

func initialize(config: Dictionary):
	defense_config = config
	# Determine defense_type based on the texture filename
	if defense_config.has("texture"):
		# Extract the base name without extension or path (e.g., "defense_tower")
		var texture_basename = defense_config.texture.get_file().get_basename().to_lower()
		# Map texture names to drawing types, prioritizing "main_tower"
		if "main_tower" in texture_basename:
			defense_type = "main_tower"
		elif "tower" in texture_basename: # Catches "defense_tower", "archer_tower", etc. (but not main_tower now)
			defense_type = "tower" # Use "tower" as the key for draw_archer_tower
		elif "spikes" in texture_basename:
			defense_type = "spikes"
		# Add more mappings if other defense textures exist
		else:
			defense_type = "unknown"
			printerr("Unknown defense texture pattern for procedural drawing: ", defense_config.texture)
	# REMOVED: Fallback check for name == "main_tower"
	else: # This triggers ONLY if 'texture' field is missing
		defense_type = "unknown"
		printerr("Defense config is missing 'texture' field to determine drawing type: ", defense_config)

	# Set modulate/tint if specified in config
	if defense_config.has("tint"):
		modulate = Color(defense_config.tint)
	else:
		modulate = Color.WHITE # Reset modulate if no tint specified

	# Trigger a redraw
	queue_redraw()

func _draw():
	var size = GameConfig.GRID.CELL_SIZE
	
	# Center the drawing coordinate system within the node's local space
	# The node's position will handle global placement
	var draw_center = Vector2(0, 0) # Draw relative to the node's origin
	
	match defense_type:
		"tower", "archer_tower": # Combine similar types (e.g., "tower" and "archer_tower")
			draw_archer_tower(size, draw_center)
		"spikes":
			draw_spikes(size, draw_center)
		"main_tower":
			draw_main_tower(size, draw_center)
		_:
			# Draw a default placeholder if type is unknown
			draw_rect(Rect2(-size/2.0, -size/2.0, size, size), Color.MAGENTA, true)
			printerr("Unknown defense type for procedural drawing: ", defense_type, " Config: ", defense_config)

func draw_archer_tower(size: float, center: Vector2):
	# Translated from createArcherTowerTexture
	# Base colors
	var baseColor = Color("3a506b")
	var stoneColor = Color("4a6583")
	var windowColor = Color("222222")
	var doorColor = Color("332929")
	
	var baseWidth = size * 0.4 # Width of the tower base
	var crenelWidth = size * 0.08
	var crenelHeight = size * 0.1
	var baseTopY = center.y - crenelHeight # Top edge of the tower base rectangle
	var battlementTopY = baseTopY - crenelHeight # Top Y coordinate for drawing battlements

	# Tower base - centered horizontally, offset vertically
	# Rect2(x, y, width, height) relative to 'center'
	var base_rect = Rect2(center.x - baseWidth / 2.0, center.y - crenelHeight, baseWidth, size * 0.6)
	draw_rect(base_rect, baseColor)

	# Calculate spacing for 3 battlements to center them
	var numBattlements = 3
	var gapWidth = crenelWidth * 0.75 # Gap is 0.75 times crenel width based on original step 1.75
	var totalBattlementWidth = numBattlements * crenelWidth + (numBattlements - 1) * gapWidth
	var startX = center.x - totalBattlementWidth / 2.0 # Start position for the center of the first battlement group

	for i in range(numBattlements):
		var currentX = startX + (gapWidth / 2) + i * (crenelWidth + gapWidth)
		# Draw rectangle centered horizontally at currentX, with top edge at battlementTopY
		var battlement_rect = Rect2(currentX, battlementTopY, crenelWidth, crenelHeight)
		# Adjust rect position to center the crenel around currentX
		battlement_rect.position.x -= crenelWidth / 2.0
		draw_rect(battlement_rect, stoneColor)

	# Single window - centered
	var window_rect = Rect2(center.x - size * 0.05, center.y, size * 0.1, size * 0.1)
	draw_rect(window_rect, windowColor)

	# Door - centered horizontally, offset vertically
	var door_rect = Rect2(center.x - size * 0.1, center.y + size * 0.25, size * 0.2, size * 0.25)
	draw_rect(door_rect, doorColor)


func draw_spikes(size: float, center: Vector2):
	# Translated from createSpikesTexture
	var spacing = size * 0.12  # Space between dots
	var dotRadius = size * 0.04  # Size of each dot (radius)
	var spikeColor = Color.WHITE # Base color for spikes
	
	# Create 3x3 grid of dots centered around the provided center
	for row in range(-1, 2): # -1, 0, 1
		for col in range(-1, 2): # -1, 0, 1
			var pos = Vector2(col * spacing, row * spacing)
			draw_circle(center + pos, dotRadius, spikeColor)

func draw_main_tower(size: float, center: Vector2):
	# Translated from createMainTowerTexture
	# Base colors
	var baseColor = Color("3a506b")      # Dark blue for main part
	var stoneColor = Color("4a6583")     # Lighter for details
	var windowColor = Color("222222")     # Dark for windows
	var doorColor = Color("332929")       # Dark brown for doors
	
	var baseWidth = size * 0.6 # Width of the tower base
	var crenelWidth = size * 0.1
	var crenelHeight = size * 0.13
	var baseTopY = center.y # Top edge of the main tower base rectangle
	var battlementTopY = baseTopY - crenelHeight - size * 0.2 # Top Y coordinate for drawing battlements

	# Tower base - centered horizontally, offset vertically
	var base_rect = Rect2(center.x - baseWidth / 2.0, center.y - size * 0.2, baseWidth, size * 0.7)
	draw_rect(base_rect, baseColor)
	
	# Calculate spacing for 4 battlements to center them (adjust numBattlements if needed)
	var numBattlements = 4
	var gapWidth = crenelWidth * 0.5 # Gap is 0.5 times crenel width based on original step 1.5
	var totalBattlementWidth = numBattlements * crenelWidth + (numBattlements - 1) * gapWidth
	var startX = center.x - totalBattlementWidth / 2.0 # Start position for the center of the first battlement group

	for i in range(numBattlements):
		var currentX = startX + (gapWidth) + i * (crenelWidth + gapWidth)
		# Draw rectangle centered horizontally at currentX, with top edge at battlementTopY
		var battlement_rect = Rect2(currentX, battlementTopY, crenelWidth, crenelHeight)
		# Adjust rect position to center the crenel around currentX
		battlement_rect.position.x -= crenelWidth / 2.0
		draw_rect(battlement_rect, stoneColor)

	# Windows - centered horizontally, offset vertically
	var window_width = size * 0.1
	var window_height = size * 0.1
	var window_gap_x = size * 0.15
	var window_gap_y = size * 0.2
	var window_start_y = center.y - size * 0.1
	draw_rect(Rect2(center.x - window_gap_x - window_width / 2.0, window_start_y, window_width, window_height), windowColor)
	draw_rect(Rect2(center.x + window_gap_x - window_width / 2.0, window_start_y, window_width, window_height), windowColor)
	draw_rect(Rect2(center.x - window_gap_x - window_width / 2.0, window_start_y + window_gap_y, window_width, window_height), windowColor)
	draw_rect(Rect2(center.x + window_gap_x - window_width / 2.0, window_start_y + window_gap_y, window_width, window_height), windowColor)

	# Door - centered horizontally, offset vertically
	var door_rect = Rect2(center.x - size * 0.1, center.y + size * 0.25, size * 0.2, size * 0.25)
	draw_rect(door_rect, doorColor)

	# Flag - Note: Flag colors/design might need adjustment
	var flag_pole_rect = Rect2(center.x - size * 0.13, center.y - size * 0.45, size * 0.03, size * 0.17)
	draw_rect(flag_pole_rect, Color.BLACK)
	var flag_rect_1 = Rect2(center.x - size * 0.3, center.y - size * 0.47, size * 0.1, size * 0.07)
	draw_rect(flag_rect_1, Color.RED)
	var flag_rect_2 = Rect2(center.x - size * 0.2, center.y - size * 0.47, size * 0.1, size * 0.07)
	draw_rect(flag_rect_2, Color.YELLOW)
	# var flag_rect_3 = Rect2(center.x - size * 0.1, center.y - size * 0.27, size * 0.1, size * 0.07) # Original green, omitted for now
	# draw_rect(flag_rect_3, Color.GREEN)
