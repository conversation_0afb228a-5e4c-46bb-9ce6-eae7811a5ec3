extends Control

# ResourceComponent class for displaying resources
# Uses an associated .tscn file for its node structure.

# UI elements from the scene
@onready var resource_label: Label = $ResourceLabel

# Call this after instantiating the scene
func setup(initial_resources: int) -> void:
	update_resources(initial_resources)

# Update resources display
func update_resources(new_resources: int) -> void:
	if resource_label: # Check if node is ready
		resource_label.text = "Zdroje: %d" % new_resources
	else:
		# If called before _ready, defer it
		call_deferred("update_resources", new_resources)
