extends Control

# Signals emitted by the button
signal unit_selected(unit_uuid)
signal unit_hover_started(unit_data, button_node)
signal unit_hover_ended()

# Unit data associated with this button
var unit_data: Dictionary = {}
var is_selected: bool = false # Added: Track selection state

# UI Node references (will be assigned in _ready or setup)
@onready var background: Panel = $Background
@onready var name_text: Label = $VBoxContainer/NameLabel
@onready var stats_text: Label = $VBoxContainer/StatsLabel
@onready var count_text: Label = $CountLabel

# Colors
const HOVER_COLOR = Color("666666")
const NORMAL_COLOR = Color("444444")
const DISABLED_COLOR = Color("222222")
const SELECTED_COLOR = Color("888888") # Added: Color for selected state

func _ready():
	# Connect signals for interaction
	background.gui_input.connect(_on_background_gui_input)
	background.mouse_entered.connect(_on_background_mouse_entered)
	background.mouse_exited.connect(_on_background_mouse_exited)

# Setup the button with specific unit data
func setup(data: Dictionary):
	unit_data = data

	name_text.text = unit_data.get("name", "N/A")
	stats_text.text = "Zdraví: %d\nPoškození: %d\nRychlost: %.1f" % [
		unit_data.get("health", 0),
		unit_data.get("damage", 0),
		unit_data.get("speed", 0.0)
	]
	update_count(unit_data.get("count", 0))
	set_selected(false) # Ensure initial state is not selected

# Update the displayed unit count and button appearance
func update_count(new_count: int):
	unit_data["count"] = new_count
	count_text.text = "×%d" % new_count

	# Update appearance based on count (overrides selection if count is 0)
	if new_count > 0:
		mouse_filter = MOUSE_FILTER_STOP # Make interactable
		name_text.modulate = Color.WHITE
		stats_text.modulate = Color("aaaaaa")
		count_text.modulate = Color.WHITE
		# Restore selected or normal color
		_update_background_color()
	else:
		mouse_filter = MOUSE_FILTER_IGNORE # Make non-interactable
		name_text.modulate = Color.GRAY
		stats_text.modulate = Color.DARK_GRAY
		count_text.modulate = Color.GRAY
		# Set disabled color regardless of selection
		_set_background_color(DISABLED_COLOR)


# Added: Set the selection state and update appearance
func set_selected(selected: bool):
	is_selected = selected
	_update_background_color()

# Added: Helper to update background color based on state
func _update_background_color():
	if unit_data.get("count", 0) <= 0:
		_set_background_color(DISABLED_COLOR)
	elif is_selected:
		_set_background_color(SELECTED_COLOR)
	else:
		_set_background_color(NORMAL_COLOR)

# Added: Helper to apply background color
func _set_background_color(color: Color):
	var style = background.get_theme_stylebox("panel").duplicate() as StyleBoxFlat
	style.bg_color = color
	background.add_theme_stylebox_override("panel", style)


# --- Signal Handlers ---

func _on_background_gui_input(event: InputEvent):
	if unit_data.get("count", 0) > 0 and event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		# Don't emit if already selected, let AttackScene handle toggling
		# if not is_selected:
		unit_selected.emit(unit_data.uuid)
		get_viewport().set_input_as_handled() # Consume the event

func _on_background_mouse_entered():
	if unit_data.get("count", 0) > 0 and not is_selected: # Don't hover if selected
		_set_background_color(HOVER_COLOR)
		unit_hover_started.emit(unit_data, self)

func _on_background_mouse_exited():
	if unit_data.get("count", 0) > 0:
		# Restore correct color (selected or normal)
		_update_background_color()
	unit_hover_ended.emit()
