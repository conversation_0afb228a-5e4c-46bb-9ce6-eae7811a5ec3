extends Control

# PathButton class for path selection
# Uses an associated .tscn file for its node structure.

# Preload required resources
# const GameConfig = preload("res://config/GameConfig.gd") # Not strictly needed if size set in scene

# UI elements from the scene
@onready var background: Panel = $Background
@onready var name_label: Label = $NameLabel
@onready var cost_label: Label = $CostLabel

# State variables
var path_config: Dictionary
var selected: bool = false

var tooltip: Control # Keep tooltip management

# Signal for path selection
signal path_button_selected(is_selected)

# Call this after instantiating the scene
func setup(path_config: Dictionary):
	self.path_config = path_config
	# Configuration is now stored, UI updates will happen in _ready()

func _ready():
	# Ensure path_config was set before trying to use it
	if path_config:
		# Set initial text now that labels are ready
		name_label.text = path_config.get("name", "Path")
		cost_label.text = "Cena: %d" % path_config.get("cost", 0)
		# Set initial visual state (not selected)
		set_selected(false) # Ensure visual consistency on setup

# Handle button input (connected in scene)
func _on_background_input(event: InputEvent) -> void:
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		handle_selection()
		get_viewport().set_input_as_handled() # Consume event

# Handle mouse hover (connected in scene)
func _on_mouse_entered() -> void:
	if not selected:
		# Update background for hover (only if not selected)
		var hover_style = StyleBoxFlat.new()
		hover_style.bg_color = Color("444444") # Hover color when not selected
		background.add_theme_stylebox_override("panel", hover_style)

	show_tooltip()

# Handle mouse exit (connected in scene)
func _on_mouse_exited() -> void:
	# Restore normal background if not selected
	if not selected:
		_update_background_style() # Restore to normal (non-hover) color

	hide_tooltip()

# Handle selection logic
func handle_selection() -> void:
	selected = !selected
	_update_background_style() # Update background based on new selection state

	emit_signal("path_button_selected", selected)

# Updates the background style based on current state (selected or normal non-hover)
func _update_background_style() -> void:
	var style = StyleBoxFlat.new()
	if selected:
		style.bg_color = Color("008800") # Selected color
	else:
		# Not selected (non-hover state)
		style.bg_color = Color("666666") # Normal color

	background.add_theme_stylebox_override("panel", style)


# Show tooltip with path description
func show_tooltip() -> void:
	if not path_config: return

	hide_tooltip()

	tooltip = Control.new()
	tooltip.position = Vector2(size.x, 0)
	add_child(tooltip)

	var padding = 10

	var tooltip_text = Label.new()
	tooltip_text.position = Vector2(padding, padding)
	tooltip_text.text = path_config.get("description", "No description available")
	tooltip_text.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	tooltip_text.size.x = 200
	tooltip_text.add_theme_font_size_override("font_size", 14)
	tooltip.add_child(tooltip_text)

	tooltip_text.size.y = tooltip_text.get_line_count() * tooltip_text.get_theme_font_size("font_size") * 1.2 # Approximate

	var tooltip_bg = Panel.new()
	tooltip_bg.size = Vector2(tooltip_text.size.x + (padding * 2), tooltip_text.size.y + (padding * 2))

	var bg_style = StyleBoxFlat.new()
	bg_style.bg_color = Color(0, 0, 0, 0.9)
	tooltip_bg.add_theme_stylebox_override("panel", bg_style)

	tooltip.add_child(tooltip_bg)
	tooltip_bg.move_to_front()
	tooltip_text.move_to_front()


# Hide any visible tooltip
func hide_tooltip() -> void:
	if is_instance_valid(tooltip):
		tooltip.queue_free()
		tooltip = null

# Set the selection state externally
func set_selected(is_selected: bool) -> void:
	selected = is_selected
	_update_background_style()

# --- Getters ---

func get_path_config() -> Dictionary:
	return path_config

func is_selected() -> bool:
	return selected
