extends Control

# TerrainInfo class for displaying terrain information
# Uses an associated .tscn file for its node structure.

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# UI elements from the scene
@onready var panel: Panel = $Panel # Assuming the Panel node is named "Panel" in the scene
@onready var title_label: Label = $Panel/TitleLabel # Access via path from root
@onready var info_label: Label = $Panel/InfoLabel   # Access via path from root

# Show terrain information
func show_info(terrain_type: String, x: int, y: int) -> void:
	if terrain_type == "":
		hide_info() # Use the hide function
		return

	var terrain_config = GameConfig.TERRAIN_TYPES.get(terrain_type, {})
	if not terrain_config:
		printerr("TerrainInfo: Unknown terrain type - ", terrain_type)
		hide_info()
		return

	# Set title
	title_label.text = terrain_config.get("name", "Unknown Terrain")

	# Set info text
	info_label.text = get_terrain_info_text(terrain_config, x, y)

	# Show the info panel (the root Control node)
	self.visible = true

# Get terrain information text
func get_terrain_info_text(terrain_config: Dictionary, x: int, y: int) -> String:
	var text = ""

	# Add defense bonus
	var defense_bonus = terrain_config.get("defenseBonus", 0.0) * 100
	text += "Defense Bonus: +%d%%\n" % defense_bonus

	# Add attack penalty
	var attack_penalty = terrain_config.get("attackPenalty", 0.0) * 100
	text += "Attack Penalty: -%d%%\n" % attack_penalty

	# Add movement cost
	var movement_cost = terrain_config.get("movementCost", 1.0)
	text += "Movement Cost: %.1fx\n" % movement_cost

	# Add position
	text += "\nPosition: (%d, %d)" % [x, y]

	return text

# Hide the info panel
func hide_info() -> void:
	self.visible = false
	# Optionally clear labels if needed when hidden
	# title_label.text = ""
	# info_label.text = ""
