extends Control

# DefenseButton class for defense selection
# Uses an associated .tscn file for its node structure.

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# UI elements from the scene
@onready var background: Panel = $Background
@onready var name_label: Label = $NameLabel
@onready var cost_label: Label = $CostLabel

# State variables
var defense_type: String
var defense_config: Dictionary
var is_button_active: bool = false
var is_selected: bool = false

var tooltip: Control # Keep tooltip management for now

# Signals
signal defense_button_selected(defense_type, button)
signal defense_button_deselected(defense_type, button)

# Called when the node is ready
func _ready():
	if defense_config:
		name_label.text = defense_config.get("name", "Unknown Defense")
		cost_label.text = "Cena: %d" % defense_config.get("cost", 0)

	# Set initial state (usually inactive until conditions met)
	set_button_inactive()

# Initialize the component's data and initial state. Call this after instantiating the scene.
func setup(defense_config: Dictionary, defense_type: String):
	self.defense_type = defense_type
	self.defense_config = defense_config


# Set the button to active state
func set_button_active() -> void:
	if is_button_active: return # Avoid redundant setup

	is_button_active = true

	# Reset visual appearance
	name_label.add_theme_color_override("font_color", Color.WHITE)
	cost_label.add_theme_color_override("font_color", Color.YELLOW) # Use Color constants

	name_label.modulate.a = 1.0
	cost_label.modulate.a = 1.0

	# Update background color based on selection state
	_update_background_style()

	# Ensure interactivity is enabled (mouse filter is set in scene)
	background.mouse_filter = Control.MOUSE_FILTER_STOP

	# Reconnect hover signals if they were disconnected for inactive state
	if not background.is_connected("mouse_entered", Callable(self, "_on_mouse_entered")):
		background.connect("mouse_entered", Callable(self, "_on_mouse_entered"))
	if background.is_connected("mouse_entered", Callable(self, "_on_inactive_mouse_entered")):
		background.disconnect("mouse_entered", Callable(self, "_on_inactive_mouse_entered"))


# Set the button to inactive state
func set_button_inactive() -> void:
	# Check if already inactive to prevent redundant operations
	#if not is_button_active and not is_selected: return

	is_button_active = false
	is_selected = false # Cannot be selected if inactive

	# Update visual appearance for inactive state
	name_label.add_theme_color_override("font_color", Color.GRAY)
	cost_label.add_theme_color_override("font_color", Color.GRAY)

	name_label.modulate.a = 0.5
	cost_label.modulate.a = 0.5

	# Update background color
	_update_background_style()

	# Disable direct interaction (input events won't be processed in _on_background_input)
	# Keep mouse filter STOP to allow hover for inactive tooltip
	background.mouse_filter = Control.MOUSE_FILTER_STOP

	# Disconnect active hover/exit, connect inactive hover
	if background.is_connected("mouse_entered", Callable(self, "_on_mouse_entered")):
		background.disconnect("mouse_entered", Callable(self, "_on_mouse_entered"))
	if not background.is_connected("mouse_entered", Callable(self, "_on_inactive_mouse_entered")):
		background.connect("mouse_entered", Callable(self, "_on_inactive_mouse_entered"))


# Handle button input (connected in scene)
func _on_background_input(event: InputEvent) -> void:
	if not is_button_active:
		return # Ignore clicks if inactive

	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		handle_selection()
		get_viewport().set_input_as_handled() # Consume event


# Handle mouse hover (connected in scene)
func _on_mouse_entered() -> void:
	if not is_button_active: return # Should not happen if signals are correct, but good check

	if not is_selected:
		# Update background for hover (only if not selected)
		var style: StyleBoxFlat = background.get_theme_stylebox("panel").duplicate()
		style.bg_color = Color("444444") # Hover color when active but not selected
		background.add_theme_stylebox_override("panel", style)

	show_tooltip()


# Handle mouse exit (connected in scene)
func _on_mouse_exited() -> void:
	# Restore normal background if not selected
	if not is_selected:
		_update_background_style() # Restore to normal active (non-hover) color

	hide_tooltip()


# Handle mouse hover for inactive button
func _on_inactive_mouse_entered() -> void:
	if is_button_active: return # Should only trigger when inactive
	show_inactive_tooltip()


# Handle selection logic
func handle_selection() -> void:
	if not is_button_active:
		return

	is_selected = !is_selected
	_update_background_style() # Update background based on new selection state

	if is_selected:
		emit_signal("defense_button_selected", defense_type, self)
	else:
		emit_signal("defense_button_deselected", defense_type, self)


# Updates the background style based on current state (active, selected)
func _update_background_style() -> void:
	var style: StyleBoxFlat = background.get_theme_stylebox("panel").duplicate() # Modify existing or duplicate
	if not style: # Should not happen if theme is set, but safety check
		style = StyleBoxFlat.new()

	if not is_button_active:
		style.bg_color = Color("333333") # Inactive color
	elif is_selected:
		style.bg_color = Color("008800") # Selected color
	else:
		# Active but not selected (non-hover state)
		style.bg_color = Color("666666") # Normal active color

	background.add_theme_stylebox_override("panel", style)


# Show tooltip with defense description
func show_tooltip() -> void:
	if not defense_config: return # Ensure config is loaded

	hide_tooltip() # Clear previous tooltip

	tooltip = Control.new()
	tooltip.position = Vector2(size.x, 0) # Position relative to button
	add_child(tooltip)

	var padding = 10

	var tooltip_text = Label.new()
	tooltip_text.position = Vector2(padding, padding)
	tooltip_text.text = defense_config.get("description", "No description available")
	tooltip_text.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	tooltip_text.size.x = 200 # Width constraint
	tooltip_text.add_theme_font_size_override("font_size", 14)
	tooltip.add_child(tooltip_text)

	# Adjust label height based on content (simplified approach)
	tooltip_text.size.y = tooltip_text.get_line_count() * tooltip_text.get_theme_font_size("font_size") * 1.2 # Approximate

	# Create background
	var tooltip_bg = Panel.new()
	tooltip_bg.size = Vector2(tooltip_text.size.x + (padding * 2), tooltip_text.size.y + (padding * 2))

	var bg_style = StyleBoxFlat.new()
	bg_style.bg_color = Color(0, 0, 0, 0.9) # Black with 90% opacity
	tooltip_bg.add_theme_stylebox_override("panel", bg_style)

	tooltip.add_child(tooltip_bg)
	tooltip_bg.move_to_front() # Ensure background is behind text visually
	tooltip_text.move_to_front()


# Show tooltip for inactive defense button
func show_inactive_tooltip() -> void:
	hide_tooltip() # Clear previous tooltip

	tooltip = Control.new()
	tooltip.position = Vector2(size.x, 0) # Position relative to button
	add_child(tooltip)

	var padding = 10

	var tooltip_text = Label.new()
	tooltip_text.position = Vector2(padding, padding)
	tooltip_text.text = "Nejdřív musíš postavit cestu k věži!\nPoté bude možné stavět obrany."
	tooltip_text.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	tooltip_text.size.x = 200 # Width constraint
	tooltip_text.add_theme_font_size_override("font_size", 14)
	tooltip_text.add_theme_color_override("font_color", Color("ff6666"))
	tooltip.add_child(tooltip_text)

	# Adjust label height based on content (simplified approach)
	tooltip_text.size.y = tooltip_text.get_line_count() * tooltip_text.get_theme_font_size("font_size") * 1.2 # Approximate

	# Create background
	var tooltip_bg = Panel.new()
	tooltip_bg.size = Vector2(tooltip_text.size.x + (padding * 2), tooltip_text.size.y + (padding * 2))

	var bg_style = StyleBoxFlat.new()
	bg_style.bg_color = Color(0, 0, 0, 0.9) # Black with 90% opacity
	tooltip_bg.add_theme_stylebox_override("panel", bg_style)

	tooltip.add_child(tooltip_bg)
	tooltip_bg.move_to_front()
	tooltip_text.move_to_front()


# Hide any visible tooltip
func hide_tooltip() -> void:
	if is_instance_valid(tooltip):
		tooltip.queue_free()
		tooltip = null


# Set the selection state externally without triggering events
func set_selected(selected: bool) -> void:
	if not is_button_active: return # Cannot select if inactive

	is_selected = selected
	_update_background_style()


# --- Getters ---

func get_defense_type() -> String:
	return defense_type

func get_defense_config() -> Dictionary:
	return defense_config

func is_button_selected() -> bool:
	return is_selected

func is_active() -> bool:
	return is_button_active
