extends Control

# DefenseInfo class for displaying defense information
# Uses an associated .tscn file for its node structure.

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# UI elements from the scene
@onready var modal_background: ColorRect = $ModalBackground
@onready var window_background: Panel = $WindowBackground

# Signal for defense removal
signal remove_defense(x, y)

# Keep track of the current defense being shown to pass coords to remove signal
var current_defense_coords: Vector2i

# Handle modal background input (connected in scene)
func _on_modal_background_input(event: InputEvent) -> void:
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		hide_info()
		get_viewport().set_input_as_handled() # Consume event

# Shows defense information window
func show_info(defense: Dictionary, x: int, y: int) -> void:
	# Clear previous content from the window background first
	for child in window_background.get_children():
		child.queue_free()

	current_defense_coords = Vector2i(x, y)

	var window_width = window_background.size.x
	var window_height = window_background.size.y
	var padding = 20

	# Get defense configuration using Autoload
	# Ensure GameState autoload is correctly set up in project settings
	var defense_config = GameState.get_defense_config(defense.uuid)
	if not defense_config:
		printerr("DefenseInfo: Could not find config for defense UUID: ", defense.uuid)
		defense_config = {} # Use empty dict to avoid errors

	# --- Create Dynamic Content ---

	# Title
	var title = Label.new()
	# Position relative to window_background
	title.position = Vector2(padding, padding)
	title.size = Vector2(window_width - padding * 2, 30)
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.text = defense_config.get("name", "Unknown Defense")
	title.add_theme_font_size_override("font_size", 24)
	title.add_theme_color_override("font_color", Color.WHITE)
	window_background.add_child(title)

	# Stats
	var stats = Label.new()
	stats.position = Vector2(padding, title.position.y + title.size.y + padding)
	stats.size = Vector2(window_width - padding * 2, window_height / 2) # Adjust size as needed
	stats.text = "Zdraví: %d/%d\nPozice: %d,%d\nDosah: %d\n\n%s" % [
		defense.get("health", 0), # Use .get() for safety
		defense_config.get("health", 0),
		x, y,
		defense_config.get("range", 0),
		defense_config.get("description", "")
	]
	stats.add_theme_font_size_override("font_size", 16)
	stats.add_theme_color_override("font_color", Color.WHITE)
	stats.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	stats.vertical_alignment = VERTICAL_ALIGNMENT_TOP
	window_background.add_child(stats)

	# Remove button
	var remove_button = Button.new()
	var button_width = 160.0
	var button_height = 40.0
	remove_button.position = Vector2(window_width / 2 - button_width / 2, window_height - padding - button_height)
	remove_button.size = Vector2(button_width, button_height)
	remove_button.text = "Zbourat"
	remove_button.add_theme_font_size_override("font_size", 16)

	# Set custom stylebox for the button
	var button_style = StyleBoxFlat.new()
	button_style.bg_color = Color("ff4444")
	remove_button.add_theme_stylebox_override("normal", button_style)

	var button_hover_style = StyleBoxFlat.new()
	button_hover_style.bg_color = Color("ff6666")
	remove_button.add_theme_stylebox_override("hover", button_hover_style)

	# Connect pressed signal using the stored coordinates
	remove_button.pressed.connect(_on_remove_button_pressed)
	window_background.add_child(remove_button)

	# Close button in top-right corner
	var close_button = Button.new()
	var close_size = 25.0 # Slightly larger click target
	close_button.position = Vector2(window_width - padding - close_size, padding)
	close_button.size = Vector2(close_size, close_size)
	close_button.text = "X"
	close_button.add_theme_font_size_override("font_size", 16)
	close_button.add_theme_color_override("font_color", Color.WHITE)

	# Set custom stylebox for the close button
	var close_style = StyleBoxFlat.new()
	close_style.bg_color = Color("333333") # Same as window background
	close_button.add_theme_stylebox_override("normal", close_style)

	var close_hover_style = StyleBoxFlat.new()
	close_hover_style.bg_color = Color("ff0000")
	close_button.add_theme_stylebox_override("hover", close_hover_style)

	close_button.pressed.connect(hide_info) # Connect directly to hide_info
	window_background.add_child(close_button)

	# --- Show the modal ---
	self.visible = true


# Hides the defense information window
func hide_info() -> void:
	# Remove dynamically added children from window_background
	for child in window_background.get_children():
		child.queue_free()

	self.visible = false


# Handle remove button press
func _on_remove_button_pressed() -> void:
	emit_signal("remove_defense", current_defense_coords.x, current_defense_coords.y)
	hide_info()
