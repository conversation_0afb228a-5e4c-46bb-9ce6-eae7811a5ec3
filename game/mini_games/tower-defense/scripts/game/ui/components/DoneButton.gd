extends Control

# DoneButton class for completing phases
# Uses an associated .tscn file for its node structure.

# UI elements from the scene
@onready var button_bg: Panel = $ButtonBg
@onready var button_text: Label = $ButtonText
@onready var shadow_text: Label = $ShadowText # Added reference

# State variable
var disabled: bool = true

# Signal for button click
signal done_button_clicked

# Constants for colors
const NORMAL_COLOR = Color("4CAF50")
const HOVER_COLOR = Color("66BB6A")
const DISABLED_COLOR = Color("aaaaaa") # Adjust disabled appearance if needed

# Call this after instantiating the scene to set initial state
func setup(is_disabled: bool = true):
	set_disabled(is_disabled)

# Handle button input (connected in scene)
func _on_button_input(event: InputEvent) -> void:
	if disabled:
		return

	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		print("Done button clicked")
		emit_signal("done_button_clicked")

# Handle mouse hover (connected in scene)
func _on_mouse_entered() -> void:
	if disabled:
		return

	# Modify existing stylebox for hover effect
	var style: StyleBoxFlat = button_bg.get_theme_stylebox("panel").duplicate() # Duplicate to avoid modifying shared resource if theme is used elsewhere
	style.bg_color = HOVER_COLOR
	button_bg.add_theme_stylebox_override("panel", style)

# Handle mouse exit (connected in scene)
func _on_mouse_exited() -> void:
	if disabled:
		return

	# Restore normal stylebox color
	var style: StyleBoxFlat = button_bg.get_theme_stylebox("panel").duplicate()
	style.bg_color = NORMAL_COLOR
	button_bg.add_theme_stylebox_override("panel", style)


# Set button disabled state
func set_disabled(is_disabled: bool) -> void:
	if disabled == is_disabled: return # No change needed

	disabled = is_disabled

	# Kill any existing tweens on this node to prevent conflicts
	kill_tweens()

	var target_alpha = 1.0
	var target_scale = Vector2(1.0, 1.0)
	var target_mouse_filter = Control.MOUSE_FILTER_STOP
	var target_color = NORMAL_COLOR

	if disabled:
		target_alpha = 0.6
		target_mouse_filter = Control.MOUSE_FILTER_IGNORE
		target_color = DISABLED_COLOR # Use a distinct disabled color
		# Ensure scale is reset if disabled while hovered/scaled
		if not scale.is_equal_approx(Vector2(1.0, 1.0)):
			target_scale = Vector2(1.0, 1.0)
		else:
			# If already at 1.0 scale, don't tween scale
			target_scale = scale
	else:
		# Enabling: Bounce effect
		target_mouse_filter = Control.MOUSE_FILTER_STOP
		target_color = NORMAL_COLOR
		var tween_enable = create_tween().set_parallel(true)
		tween_enable.tween_property(self, "modulate:a", 1.0, 0.2).set_ease(Tween.EASE_OUT)
		var scale_tween = create_tween().set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
		scale_tween.tween_property(self, "scale", Vector2(1.1, 1.1), 0.15)
		scale_tween.tween_property(self, "scale", Vector2(1.0, 1.0), 0.15)
		# Don't tween color/mouse filter immediately for enable, let hover handle it

	# Apply immediate changes
	button_bg.mouse_filter = target_mouse_filter

	# Apply visual changes (tween alpha, set color)
	var tween_disable = create_tween().set_parallel(true)
	if modulate.a != target_alpha:
		tween_disable.tween_property(self, "modulate:a", target_alpha, 0.2).set_ease(Tween.EASE_OUT)
	if scale != target_scale and disabled: # Only tween scale down if disabling
		tween_disable.tween_property(self, "scale", target_scale, 0.2).set_ease(Tween.EASE_OUT)

	# Update background color directly or via stylebox modification
	var style: StyleBoxFlat = button_bg.get_theme_stylebox("panel").duplicate()
	style.bg_color = target_color
	button_bg.add_theme_stylebox_override("panel", style)

	# Adjust text color based on disabled state
	var text_color = Color.WHITE if not disabled else Color.DARK_GRAY
	button_text.add_theme_color_override("font_color", text_color)
	# Optionally adjust shadow color too
	shadow_text.modulate.a = 0.5 if not disabled else 0.2


# Check if button is disabled
func is_disabled() -> bool:
	return disabled

# Helper to kill existing tweens on this node
func kill_tweens():
	var tweens = get_tree().get_processed_tweens()
	for t in tweens:
		if t.is_valid() and t.get_parent() == self:
			t.kill()
