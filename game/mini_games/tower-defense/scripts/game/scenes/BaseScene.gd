extends Node2D

# BaseScene class as a foundation for game scenes
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")
const PathTileScene = preload("res://scenes/PathTile.tscn")
const ProceduralDefense = preload("res://scripts/game/objects/defenses/ProceduralDefense.gd")

# Scene components
var game_container: Node2D
var ui_container: Control
var paths: Dictionary = {}
var defenses: Dictionary = {}
var game_state
var terrain_info
var audio_system

# Called when the node enters the scene tree for the first time
func _ready():
	# Get game state
	game_state = get_node("/root/GameState")
	
	# Set up camera
	setup_camera()
	
	# Create containers
	game_container = Node2D.new()
	add_child(game_container)
	
	ui_container = Control.new()
	ui_container.size = get_viewport_rect().size
	ui_container.mouse_filter = Control.MOUSE_FILTER_PASS # Allow mouse events to pass through
	add_child(ui_container)
	
	# Create UI panels
	create_ui_panels()
	
	# Calculate game scale
	calculate_game_scale()
	
	# Create terrain info
	create_terrain_info()
	
	# Set up input handling
	setup_input()
	
	# Initialize audio system
	audio_system = load("res://scripts/game/systems/ProceduralAudioSystem.gd").new(self)
	add_child(audio_system)
	
	# Load the map
	load_map()

# Set up camera
func setup_camera() -> void:
	var camera = Camera2D.new()
	add_child(camera) # Add to tree first
	camera.make_current() # Then make current
	
	# We don't need to set camera zoom here as we're scaling the game container instead
	# This ensures the camera shows the entire scene at 1:1 scale
	camera.zoom = Vector2(1, 1)
	
	# Center the camera on the viewport
	camera.position = get_viewport_rect().size / 2

# Create UI panels
func create_ui_panels() -> void:
	var viewport_size = get_viewport_rect().size
	
	# Top panel - full width
	var top_panel = ColorRect.new()
	top_panel.color = Color(0.13, 0.13, 0.13, 0.8)
	top_panel.size = Vector2(viewport_size.x, GameConfig.UI.TOP_PANEL_HEIGHT)
	top_panel.position = Vector2(0, 0)
	top_panel.mouse_filter = Control.MOUSE_FILTER_IGNORE # Prevent blocking clicks
	ui_container.add_child(top_panel)
	
	# Side panel height (from top panel down)
	var side_panel_height = viewport_size.y - GameConfig.UI.TOP_PANEL_HEIGHT
	
	# Left panel
	var left_panel = ColorRect.new()
	left_panel.color = Color(0.13, 0.13, 0.13, 0.8)
	left_panel.size = Vector2(GameConfig.UI.LEFT_PANEL_WIDTH, side_panel_height)
	left_panel.position = Vector2(0, GameConfig.UI.TOP_PANEL_HEIGHT)
	left_panel.mouse_filter = Control.MOUSE_FILTER_IGNORE # Prevent blocking clicks
	ui_container.add_child(left_panel)
	
	# Right panel
	var right_panel = ColorRect.new()
	right_panel.color = Color(0.13, 0.13, 0.13, 0.8)
	right_panel.size = Vector2(GameConfig.UI.RIGHT_PANEL_WIDTH, side_panel_height)
	right_panel.position = Vector2(viewport_size.x - GameConfig.UI.RIGHT_PANEL_WIDTH, GameConfig.UI.TOP_PANEL_HEIGHT)
	right_panel.mouse_filter = Control.MOUSE_FILTER_IGNORE # Prevent blocking clicks
	ui_container.add_child(right_panel)
	
	# Add UI elements
	create_ui_elements()

# Create UI elements (to be overridden by child classes)
func create_ui_elements() -> void:
	pass

# Calculate game scale
func calculate_game_scale() -> void:
	var viewport_size = get_viewport_rect().size
	var game_width = GameConfig.GRID.WIDTH * GameConfig.GRID.CELL_SIZE
	var game_height = GameConfig.GRID.HEIGHT * GameConfig.GRID.CELL_SIZE
	
	# Calculate available space (accounting for UI panels)
	var available_width = viewport_size.x - GameConfig.UI.LEFT_PANEL_WIDTH - GameConfig.UI.RIGHT_PANEL_WIDTH - (GameConfig.UI.PADDING * 2)
	var available_height = viewport_size.y - GameConfig.UI.TOP_PANEL_HEIGHT - (GameConfig.UI.PADDING * 2)
	
	# Calculate scale
	var scale = min(
		available_width / game_width,
		available_height / game_height
	)
	
	# Set scale for game container only (not UI container)
	game_container.scale = Vector2(scale, scale)
	
	# Calculate centered position for game container
	var x_pos = GameConfig.UI.LEFT_PANEL_WIDTH + GameConfig.UI.PADDING + (available_width - game_width * scale) / 2
	var y_pos = GameConfig.UI.TOP_PANEL_HEIGHT + GameConfig.UI.PADDING + (available_height - game_height * scale) / 2
	
	game_container.position = Vector2(x_pos, y_pos)

# Create terrain info display from scene
func create_terrain_info() -> void:
	var terrain_info_scene = load("res://scenes/ui/components/TerrainInfo.tscn")
	terrain_info = terrain_info_scene.instantiate()
	# Set position manually
	terrain_info.position = Vector2(
		get_viewport_rect().size.x - GameConfig.UI.RIGHT_PANEL_WIDTH + GameConfig.UI.PADDING,
		GameConfig.UI.TOP_PANEL_HEIGHT + GameConfig.UI.PADDING
	)
	# Note: TerrainInfo.gd does not have a setup() function currently.
	ui_container.add_child(terrain_info)

	# Position is set above during instantiation. This block is redundant.
	# terrain_info.position.x = get_viewport_rect().size.x - GameConfig.UI.RIGHT_PANEL_WIDTH + GameConfig.UI.PADDING
	# terrain_info.position.y = GameConfig.UI.TOP_PANEL_HEIGHT + GameConfig.UI.PADDING

# Set up input handling
func setup_input() -> void:
	# Connect to window resize event
	get_viewport().connect("size_changed", Callable(self, "calculate_game_scale"))
	
	# Set up mouse tracking
	set_process_input(true)

# Handle input events
func _input(event: InputEvent) -> void:
	if event is InputEventMouseMotion:
		# Convert mouse position to grid coordinates
		var world_point = game_container.get_global_transform_with_canvas().affine_inverse() * event.position
		var tile_x = int(world_point.x / GameConfig.GRID.CELL_SIZE)
		var tile_y = int(world_point.y / GameConfig.GRID.CELL_SIZE)
		
		# Check if pointer is within valid game area
		if tile_x >= 0 and tile_x < GameConfig.GRID.WIDTH and tile_y >= 0 and tile_y < GameConfig.GRID.HEIGHT:
			update_info_panel(tile_x, tile_y)
		else:
			terrain_info.hide()

# Update info panel with terrain information
func update_info_panel(x: int, y: int) -> void:
	var terrain_type = game_state.get_map().get_terrain_at(x, y)
	
	if terrain_type == "":
		terrain_info.hide()
		return
	
	# Show terrain info
	terrain_info.show_info(terrain_type, x, y)

# Load terrains
func load_terrains() -> void:
	# Draw the basic game grid
	for y in range(GameConfig.GRID.HEIGHT):
		for x in range(GameConfig.GRID.WIDTH):
			var terrain_type = game_state.get_map().get_terrain_at(x, y)
			
			if terrain_type == "":
				continue
			
			var terrain_config = GameConfig.TERRAIN_TYPES[terrain_type]
			
			# Create terrain tile
			var terrain_tile = load("res://scripts/game/objects/terrain/TerrainTile.gd").new()
			terrain_tile.initialize(x, y, terrain_type)
			game_container.add_child(terrain_tile)

# Load paths
func load_paths() -> void:
	var map = game_state.get_map()
	
	for path in map.get_paths():
		var coords = path.split(",")
		var x = int(coords[0])
		var y = int(coords[1])
		render_path(x, y)

# Render path at position
func render_path(x: int, y: int) -> void:
	var path_tile = PathTileScene.instantiate()
	path_tile.x = x
	path_tile.y = y
	path_tile.position = Vector2(
		x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
		y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	)
	game_container.add_child(path_tile)
	paths["%d,%d" % [x, y]] = path_tile

# Load defenses
func load_defenses() -> void:
	var map = game_state.get_map()
	
	for key in map.get_all_defenses():
		var coords = key.split(",")
		var x = int(coords[0])
		var y = int(coords[1])
		var defense = map.get_defense_at(x, y)
		var defense_config = game_state.get_defense_config(defense.uuid)
		render_defense(x, y, defense_config)

# Render defense at position using procedural generation
func render_defense(x: int, y: int, defense_config: Dictionary) -> void:
	# Create procedural defense node
	var defense_node = ProceduralDefense.new()
	defense_node.position = Vector2(
		x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
		y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	)
	
	# Initialize with config (this also handles tint and triggers drawing)
	defense_node.initialize(defense_config)
	
	# Add to game container
	game_container.add_child(defense_node)
	
	# Store reference to the node
	defenses["%d,%d" % [x, y]] = defense_node

# Create main tower
func create_main_tower() -> void:
	var tower_x = GameConfig.TOWER.POSITION.X * GameConfig.GRID.CELL_SIZE + (GameConfig.GRID.CELL_SIZE / 2)
	var tower_y = GameConfig.TOWER.POSITION.Y * GameConfig.GRID.CELL_SIZE + (GameConfig.GRID.CELL_SIZE / 2)
	
	# Create procedural main tower node
	var tower_node = ProceduralDefense.new()
	tower_node.position = Vector2(tower_x, tower_y)
	
	# Initialize with a config containing a dummy texture path.
	# ProceduralDefense.gd will extract "main_tower" from the basename.
	# The actual file "res://dummy/main_tower.png" doesn't need to exist.
	tower_node.initialize({"texture": "res://dummy/main_tower.png"})
	
	game_container.add_child(tower_node)
	
	# Optionally, store a reference if needed elsewhere, though it's not currently stored
	# defenses["main_tower_key"] = tower_node # Example key

# Build path at position
func build_path(x: int, y: int) -> bool:
	var map = game_state.get_map()
	
	if not map.is_valid_position(x, y):
		return false
	
	if map.has_path(x, y):
		return false
	
	if not map.can_build_path_at(x, y):
		return false
	
	# Add path to map
	map.add_path(x, y)
	
	# Render path
	render_path(x, y)
	
	return true

# Build defense at position
func build_defense(x: int, y: int, uuid: String) -> bool:
	var defense_config = game_state.get_defense_config(uuid)
	
	if not game_state.get_map().add_defense(x, y, defense_config):
		return false
	
	# Render defense
	render_defense(x, y, defense_config)
	
	return true

# Clear map
func clear_map() -> void:
	# Remove all paths
	for key in paths:
		paths[key].queue_free()
	paths.clear()
	
	# Remove all defenses
	for key in defenses:
		defenses[key].queue_free()
	defenses.clear()
	
	# Remove all children from game container
	for child in game_container.get_children():
		child.queue_free()

# Load map
func load_map() -> void:
	clear_map()
	
	# Load terrains
	load_terrains()
	
	# Load paths
	load_paths()
	
	# Load defenses
	load_defenses()
	
	# Create main tower
	create_main_tower()

# Get game state
func get_game_state():
	return game_state

# Get audio system
func get_audio_system():
	return audio_system

# Handle tile click (to be overridden by child classes)
func handle_tile_click(x: int, y: int) -> void:
	pass

# Check if tile is valid
func is_valid_tile(x: int, y: int) -> bool:
	return x >= 0 and x < GameConfig.GRID.WIDTH and y >= 0 and y < GameConfig.GRID.HEIGHT
