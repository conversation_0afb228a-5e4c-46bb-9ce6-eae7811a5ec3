extends Node2D

# GameOverScene class for displaying game results
# Converted from JavaScript/TypeScript to GDScript

# UI elements
var title_label: Label
var message_label: Label
var restart_button: Button
var menu_button: Button
var victory: bool = false

# Called when the node enters the scene tree for the first time
func _ready():
	# Get victory status from parameters
	if get_tree().get_current_scene().has_meta("victory"):
		victory = get_tree().get_current_scene().get_meta("victory")
	
	# Create UI
	create_ui()
	
	# Play appropriate sound
	play_result_sound()

# Create UI elements
func create_ui() -> void:
	# Get viewport size
	var viewport_size = get_viewport_rect().size
	
	# Create background
	var background = ColorRect.new()
	background.color = Color("000000", 0.8)
	background.size = viewport_size
	add_child(background)
	
	# Create title
	title_label = Label.new()
	title_label.position = Vector2(viewport_size.x / 2, viewport_size.y / 4)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 48)
	
	if victory:
		title_label.text = "VÍTĚZSTVÍ!"
		title_label.add_theme_color_override("font_color", Color("00ff00"))
	else:
		title_label.text = "PORÁŽKA"
		title_label.add_theme_color_override("font_color", Color("ff0000"))
	
	add_child(title_label)
	
	# Create message
	message_label = Label.new()
	message_label.position = Vector2(viewport_size.x / 2, viewport_size.y / 2)
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	message_label.add_theme_font_size_override("font_size", 24)
	
	if victory:
		message_label.text = "Úspěšně jsi dobyl nepřátelskou věž!"
	else:
		message_label.text = "Tvoje věž byla zničena..."
	
	add_child(message_label)
	
	# Create restart button
	restart_button = Button.new()
	restart_button.position = Vector2(viewport_size.x / 2 - 100, viewport_size.y * 3/4)
	restart_button.size = Vector2(200, 50)
	restart_button.text = "Hrát znovu"
	restart_button.connect("pressed", Callable(self, "_on_restart_button_pressed"))
	add_child(restart_button)
	
	# Create menu button
	menu_button = Button.new()
	menu_button.position = Vector2(viewport_size.x / 2 - 100, viewport_size.y * 3/4 + 70)
	menu_button.size = Vector2(200, 50)
	menu_button.text = "Hlavní menu"
	menu_button.connect("pressed", Callable(self, "_on_menu_button_pressed"))
	add_child(menu_button)

# Play result sound
func play_result_sound() -> void:
	# Play victory or defeat sound
	var audio_player = AudioStreamPlayer.new()
	add_child(audio_player)
	
	# In a real implementation, we would load the appropriate sound file
	# For now, we'll just print a message
	if victory:
		print("Playing victory sound")
	else:
		print("Playing defeat sound")
	
	# Start playing
	# audio_player.play()

# Handle restart button press
func _on_restart_button_pressed() -> void:
	# Restart the game
	get_tree().change_scene_to_file("res://scenes/menu.tscn")

# Handle menu button press
func _on_menu_button_pressed() -> void:
	# Go to main menu
	get_tree().change_scene_to_file("res://scenes/menu.tscn")