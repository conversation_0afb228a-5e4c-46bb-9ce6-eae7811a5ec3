extends "res://scripts/game/scenes/BaseScene.gd"

# DefenseScene class for defender gameplay
# Converted from JavaScript/TypeScript to GDScript

# UI components
var selected_defense: String = ""
var preview_sprite: Sprite2D = null
var range_preview: Node2D = null
var defense_buttons: Dictionary = {}
var path_button
var done_button
var resource_component
var defense_info

# Path tracking
var done_paths: Array = []
var path_tweens: Dictionary = {}

# Called when the node enters the scene tree for the first time
func _ready():
	print("Creating DefenseScene...")
	super._ready()
	
	# Connect signals
	connect_signals()

# Connect signals
func connect_signals() -> void:
	# Input handling for tile clicks
	var input_handler = Node.new()
	input_handler.set_script(load("res://scripts/game/utils/InputHandler.gd"))
	input_handler.connect("tile_clicked", Callable(self, "handle_tile_click"))
	input_handler.connect("tile_hovered", Callable(self, "handle_tile_hover"))
	input_handler.connect("tile_unhovered", Callable(self, "handle_tile_hoverout"))
	add_child(input_handler)

# Create UI elements
func create_ui_elements() -> void:
	super.create_ui_elements()
	
	# Create defense info from scene
	var defense_info_scene = load("res://scenes/ui/components/DefenseInfo.tscn")
	defense_info = defense_info_scene.instantiate()
	defense_info.connect("remove_defense", Callable(self, "handle_remove_defense"))
	# Note: DefenseInfo is a full-screen modal, position is handled by anchors in the scene.
	ui_container.add_child(defense_info)
	
	# Create resource component from scene
	var resource_comp_scene = load("res://scenes/ui/components/ResourceComponent.tscn")
	resource_component = resource_comp_scene.instantiate()
	# Set position manually
	resource_component.position = Vector2(
		GameConfig.UI.LEFT_PANEL_WIDTH + GameConfig.UI.PADDING,
		GameConfig.UI.PADDING
	)
	resource_component.setup(game_state.get_resources())
	ui_container.add_child(resource_component)
	
	# Left panel - first path, then defense towers
	var button_y = GameConfig.UI.TOP_PANEL_HEIGHT + GameConfig.UI.PADDING
	
	# Create path button
	create_path_button(button_y)
	button_y += 70 + GameConfig.UI.PADDING
	
	# Create defense buttons
	create_defense_buttons(button_y)
	
	# Add Done button
	create_done_button()
	
	if has_done_paths():
		activate_defense_buttons()

# Create path button from scene
func create_path_button(button_y: int) -> void:
	var path_config = game_state.get_path_config()
	var path_button_scene = load("res://scenes/ui/components/PathButton.tscn")
	path_button = path_button_scene.instantiate()
	path_button.position = Vector2(GameConfig.UI.PADDING, button_y)
	path_button.setup(path_config)
	path_button.connect("path_button_selected", Callable(self, "handle_path_button_selected"))
	ui_container.add_child(path_button)

# Create defense buttons
func create_defense_buttons(start_y: int) -> void:
	var button_y = start_y
	
	for defense_entry in game_state.get_available_defenses():
		var type = defense_entry[0]
		var defense_config_data = defense_entry[1] # Renamed for clarity

		var button_scene = load("res://scenes/ui/components/DefenseButton.tscn")
		var button = button_scene.instantiate()
		button.position = Vector2(GameConfig.UI.PADDING, button_y)
		button.setup(defense_config_data, type)

		button.connect("defense_button_selected", Callable(self, "handle_defense_button_selected"))
		button.connect("defense_button_deselected", Callable(self, "handle_defense_button_deselected"))
		
		defense_buttons[type] = button
		button_y += 70 + GameConfig.UI.PADDING
		
		# Add to UI container
		ui_container.add_child(button)

# Create done button from scene
func create_done_button() -> void:
	var done_button_scene = load("res://scenes/ui/components/DoneButton.tscn")
	done_button = done_button_scene.instantiate()
	# Set position manually
	done_button.position = Vector2(
		get_viewport_rect().size.x - GameConfig.UI.RIGHT_PANEL_WIDTH + GameConfig.UI.PADDING,
		get_viewport_rect().size.y - 40 - GameConfig.UI.PADDING
	)
	done_button.setup(true) # Setup with initial disabled state
	done_button.connect("done_button_clicked", Callable(self, "handle_done_button_clicked"))
	ui_container.add_child(done_button)

# Handle defense button selection
func handle_defense_button_selected(type: String, button) -> void:
	# Deselect other defense buttons
	for def_type in defense_buttons:
		if def_type != type:
			defense_buttons[def_type].set_selected(false)
	
	# Deselect path button
	path_button.set_selected(false)
	
	# Set selected defense
	selected_defense = type
	
	# Clear any preview
	clear_preview()

# Handle defense button deselection
func handle_defense_button_deselected(type: String, button) -> void:
	if selected_defense == type:
		selected_defense = ""
	
	# Clear any preview
	clear_preview()

# Handle path button selection
func handle_path_button_selected(selected: bool) -> void:
	if selected:
		selected_defense = "PATH"
		
		# Deselect all defense buttons
		for def_type in defense_buttons:
			defense_buttons[def_type].set_selected(false)
	else:
		selected_defense = ""
	
	clear_preview()

# Handle remove defense
func handle_remove_defense(x: int, y: int) -> void:
	remove_defense(x, y)

# Activate defense buttons
func activate_defense_buttons() -> void:
	for def_type in defense_buttons:
		defense_buttons[def_type].set_button_active()
	
	done_button.set_disabled(false)

# Deactivate defense buttons
func deactivate_defense_buttons() -> void:
	for def_type in defense_buttons:
		defense_buttons[def_type].set_button_inactive()
	
	if selected_defense != "" and selected_defense != "PATH":
		selected_defense = ""
	
	done_button.set_disabled(true)

# Hide tooltip
func hide_tooltip() -> void:
	var existing_tooltip = get_node_or_null("tooltip")
	if existing_tooltip:
		existing_tooltip.queue_free()

# Handle tile click
func handle_tile_click(x: int, y: int) -> void:
	var defense = game_state.get_map().get_defense_at(x, y)
	if defense and defense != {} and selected_defense == "":
		defense_info.show_info(defense, x, y)
		return
	
	if selected_defense == "":
		return
	
	# If PATH is selected and we click on an existing path, remove it
	if selected_defense == "PATH" and game_state.get_map().has_path(x, y):
		remove_path(x, y)
		return
	
	if selected_defense == "PATH":
		handle_path_placement(x, y)
	else:
		handle_defense_placement(x, y)

# Handle path placement
func handle_path_placement(x: int, y: int) -> void:
	var path_config = game_state.get_path_config()
	
	# Check if we have enough resources
	if game_state.get_resources() < path_config.cost:
		show_resource_warning()
		return
	
	if build_path(x, y):
		# Deduct path cost
		spend_resources(path_config.cost)
		if is_path_done(x, y):
			audio_system.generate_path_complete_sound()
		else:
			audio_system.generate_path_placement_sound(true)
	else:
		audio_system.generate_path_placement_sound(false)

# Handle defense placement
func handle_defense_placement(x: int, y: int) -> void:
	if selected_defense == "":
		return
	
	var defense_config = game_state.get_defense_config(selected_defense)
	
	# Check if we have enough resources
	if game_state.get_resources() < defense_config.cost:
		show_resource_warning()
		return
	
	# Check if we can build defense at the position
	var map = game_state.get_map()
	if not map.can_add_defense(x, y, defense_config):
		show_warning("%s nelze postavit na této pozici." % defense_config.name)
		return
	
	if build_defense(x, y, selected_defense):
		# Deduct cost
		spend_resources(defense_config.cost)
		audio_system.generate_defense_sound(selected_defense)

# Calculate and apply refund
func calculate_and_apply_refund(removed_paths: Array) -> int:
	var path_cost = game_state.get_path_config().cost
	var total_refund = floor(removed_paths.size() * path_cost)
	
	refund_resources(total_refund)
	return total_refund

# Build defense
func build_defense(x: int, y: int, uuid: String) -> bool:
	if game_state.get_resources() >= game_state.get_defense_config(uuid).cost:
		return super.build_defense(x, y, uuid)
	return false

# Build path
func build_path(x: int, y: int) -> bool:
	var map = game_state.get_map()
	
	var neighbors = map.get_neighbors(x, y)
	for neighbor in neighbors:
		if is_path_done(neighbor.x, neighbor.y):
			return false
	
	if not super.build_path(x, y):
		return false
	
	# Try to find a path to the tower and then to the nearest edge
	var path_adjacent_to_tower = map.find_path_to_tower(x, y)
	
	if path_adjacent_to_tower and path_adjacent_to_tower.size() > 0:
		var last_point = path_adjacent_to_tower[path_adjacent_to_tower.size() - 1]
		var path_to_edge = map.find_path_to_edge(last_point.x, last_point.y)
		
		if path_to_edge and path_to_edge.size() > 0:
			var edge_point = path_to_edge[path_to_edge.size() - 1]
			
			# Find shortest path from edge to tower
			var path = map.find_path_to_tower(edge_point.x, edge_point.y)
			
			# If no path found, return false
			if not path or path.size() == 0:
				return false
			
			# Done the shortest path
			for point in path:
				var key = "%d,%d" % [point.x, point.y]
				if not key in done_paths:
					done_paths.append(key)
				highlight_done_path(point.x, point.y)
			
			# Find all paths connected to the shortest path
			var connected_paths = []
			for path_key in map.get_paths():
				var coords = path_key.split(",")
				var px = int(coords[0])
				var py = int(coords[1])
				
				if is_path_done(px, py):
					continue
				
				var path_neighbors = map.get_neighbors(px, py)
				
				var has_done_neighbor = false
				for n in path_neighbors:
					if is_path_done(n.x, n.y):
						has_done_neighbor = true
						break
				
				var in_shortest_path = false
				for point in path:
					if point.x == px and point.y == py:
						in_shortest_path = true
						break
				
				if has_done_neighbor and not in_shortest_path:
					connected_paths.append(path_key)
			
			# Remove unnecessary connected paths
			if connected_paths.size() > 0:
				# Refund resources for removed paths
				var refund = calculate_and_apply_refund(connected_paths)
				print("Refunded %d resources for %d unnecessary connected path segments" % [refund, connected_paths.size()])
				
				# Remove unnecessary paths
				for path_key in connected_paths:
					var coords = path_key.split(",")
					var px = int(coords[0])
					var py = int(coords[1])
					
					if not is_path_done(px, py):
						map.remove_path(path_key)
						
						# Destroy path sprite
						var sprite = paths.get(path_key)
						if sprite:
							sprite.queue_free()
							paths.erase(path_key)
						
						# Reset path appearance
						reset_path_appearance(px, py)
			
			
			# Activate defense buttons
			activate_defense_buttons()
	return true

# Check if we can build at position
func can_build_at(x: int, y: int) -> bool:
	if not game_state.get_map().is_valid_position(x, y):
		return false
	
	if selected_defense != "":
		if selected_defense == "PATH":
			return game_state.get_map().can_build_path_at(x, y)
		else:
			var defense_config = game_state.get_defense_config(selected_defense)
			return game_state.get_map().can_add_defense(x, y, defense_config)
	
	return true

# Remove defense
func remove_defense(x: int, y: int) -> bool:
	# Remove defense from map
	var key = "%d,%d" % [x, y]
	var defense = game_state.get_map().get_defense_at(x, y)
	
	if not defense or defense == {}:
		return false
	
	var defense_config = game_state.get_defense_config(defense.uuid)
	
	game_state.get_map().remove_defense(x, y)
	
	var sprite = defenses.get(key)
	if sprite:
		sprite.queue_free()
		defenses.erase(key)
	
	refund_resources(defense_config.cost)
	
	audio_system.generate_defense_sound(defense.uuid)
	
	return true

# Show defense range
func show_defense_range(x: int, y: int) -> void:
	var key = "%d,%d" % [x, y]
	var defense_sprite = defenses.get(key)
	if not defense_sprite:
		return
	
	var defense = game_state.get_map().get_defense_at(x, y)
	if not defense or defense == {}:
		return
	
	var defense_config = game_state.get_defense_config(defense.uuid)
	
	if defense_config.has("range") and defense_config.range > 0:
		var center_x = x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
		var center_y = y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
		
		if range_preview:
			range_preview.queue_free()
		
		range_preview = Node2D.new()
		
		var circle = ColorRect.new()
		circle.color = Color(1, 1, 1, 0.3)
		circle.size = Vector2(defense_config.range * GameConfig.GRID.CELL_SIZE * 2, defense_config.range * GameConfig.GRID.CELL_SIZE * 2)
		circle.position = Vector2(-circle.size.x / 2, -circle.size.y / 2)
		
		range_preview.add_child(circle)
		range_preview.position = Vector2(center_x, center_y)
		range_preview.z_index = -1  # Ensure it's rendered below the sprite
		
		game_container.add_child(range_preview)

# Remove path
func remove_path(x: int, y: int) -> void:
	var map = game_state.get_map()
	var key = "%d,%d" % [x, y]
	
	if not map.has_path(x, y):
		return
	
	# Remove path sprite
	var sprite = paths.get(key)
	if sprite:
		sprite.queue_free()
		paths.erase(key)
	
	# Get connected done paths
	var connected_done_paths = get_connected_done_paths(x, y)
	
	# Remove connected done paths
	for path_key in connected_done_paths:
		var coords = path_key.split(",")
		var px = int(coords[0])
		var py = int(coords[1])
		remove_done_path(px, py)
		reset_path_appearance(px, py)
	
	# Refund resources for removed paths
	calculate_and_apply_refund([key])
	
	# Remove path from map
	map.remove_path(key)
	
	# Deactivate defense buttons if no done paths remain
	if not has_done_paths():
		deactivate_defense_buttons()
	
	audio_system.generate_path_placement_sound(false)

# Get connected done paths
func get_connected_done_paths(x: int, y: int) -> Array:
	var start_key = "%d,%d" % [x, y]
	if not is_path_done(x, y):
		return [start_key]
	
	var connected_paths = []
	var visited = []
	var queue = [start_key]
	
	while queue.size() > 0:
		var current_key = queue.pop_front()
		if current_key in visited:
			continue
		
		var coords = current_key.split(",")
		var cx = int(coords[0])
		var cy = int(coords[1])
		
		visited.append(current_key)
		if is_path_done(cx, cy):
			connected_paths.append(current_key)
			
			# Add neighboring locked paths to queue
			var neighbors = [
				"%d,%d" % [cx + 1, cy],
				"%d,%d" % [cx - 1, cy],
				"%d,%d" % [cx, cy + 1],
				"%d,%d" % [cx, cy - 1]
			]
			
			for neighbor_key in neighbors:
				if neighbor_key in paths and not neighbor_key in visited:
					queue.append(neighbor_key)
	
	return connected_paths

# Check if path is done
func is_path_done(x: int, y: int) -> bool:
	return "%d,%d" % [x, y] in done_paths

# Check if there are done paths
func has_done_paths() -> bool:
	return done_paths.size() > 0

# Remove done path
func remove_done_path(x: int, y: int) -> void:
	var key = "%d,%d" % [x, y]
	if key in done_paths:
		done_paths.erase(key)

# Handle tile hover
func handle_tile_hover(x: int, y: int) -> void:
	preview_defense(x, y)
	show_defense_range(x, y)

# Preview defense
func preview_defense(x: int, y: int) -> void:
	# Hide preview if no defense is selected or if PATH is selected
	if selected_defense == "" or selected_defense == "PATH":
		handle_tile_hoverout()
		return
	
	# Hide preview if the pointer is not over a valid position
	if not can_build_at(x, y):
		handle_tile_hoverout()
		return
	
	var defense_config = game_state.get_defense_config(selected_defense)
	
	var center_x = x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	var center_y = y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	
	if not preview_sprite:
		# Create defense sprite preview
		preview_sprite = Sprite2D.new()
		preview_sprite.texture = load("res://assets/textures/defenses/" + defense_config.texture + ".png")
		preview_sprite.modulate.a = 0.7  # Increased alpha for better visibility
		preview_sprite.z_index = 1  # Ensure it's rendered above other elements
		game_container.add_child(preview_sprite)
	
	# Update defense sprite preview
	preview_sprite.position = Vector2(center_x, center_y)
	preview_sprite.texture = load("res://assets/textures/defenses/" + defense_config.texture + ".png")
	preview_sprite.visible = true
	
	if defense_config.has("tint"):
		preview_sprite.modulate = Color(defense_config.tint)
		preview_sprite.modulate.a = 0.7  # Keep transparency
	else:
		preview_sprite.modulate = Color(1, 1, 1, 0.7)
	
	# Show range preview if defense has a positive range
	if defense_config.has("range") and defense_config.range > 0:
		var range_radius = defense_config.range * GameConfig.GRID.CELL_SIZE
		
		if not range_preview:
			range_preview = Node2D.new()
			
			var circle = ColorRect.new()
			circle.color = Color(1, 1, 1, 0.3)
			circle.size = Vector2(range_radius * 2, range_radius * 2)
			circle.position = Vector2(-range_radius, -range_radius)
			
			range_preview.add_child(circle)
			range_preview.z_index = 0  # Ensure it's rendered below the sprite
			game_container.add_child(range_preview)
		
		range_preview.position = Vector2(center_x, center_y)
		range_preview.visible = true
	elif range_preview:
		range_preview.visible = false

# Handle tile hover out
func handle_tile_hoverout() -> void:
	if preview_sprite:
		preview_sprite.visible = false
	
	if range_preview:
		range_preview.visible = false

# Clear preview
func clear_preview() -> void:
	if preview_sprite:
		preview_sprite.queue_free()
		preview_sprite = null
	
	if range_preview:
		range_preview.queue_free()
		range_preview = null

# Show resource warning
func show_resource_warning() -> void:
	show_warning("Nemáš dostatek zdrojů!")

# Show warning
func show_warning(message: String) -> void:
	hide_tooltip()
	
	var tooltip = Control.new()
	tooltip.position = Vector2(get_viewport().get_mouse_position())
	tooltip.name = "tooltip"
	add_child(tooltip)
	
	var padding = 10
	
	var tooltip_text = Label.new()
	tooltip_text.position = Vector2(padding, padding)
	tooltip_text.text = message
	tooltip_text.add_theme_font_size_override("font_size", 14)
	tooltip_text.add_theme_color_override("font_color", Color("ff6666"))
	tooltip_text.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	tooltip_text.size = Vector2(200, 0)
	tooltip.add_child(tooltip_text)
	
	# Adjust label height based on content
	var font = tooltip_text.get_theme_font("font")
	var font_size = tooltip_text.get_theme_font_size("font_size")
	var lines = tooltip_text.text.split("\n")
	var total_height = lines.size() * (font_size + 4)  # Approximate line height
	
	tooltip_text.size.y = total_height
	
	# Create background
	var background = ColorRect.new()
	background.color = Color("000000", 0.9)  # Black with 90% opacity
	background.size = Vector2(tooltip_text.size.x + (padding * 2), tooltip_text.size.y + (padding * 2))
	background.position = Vector2(0, 0)
	tooltip.add_child(background)
	background.show_behind_parent = true  # Show behind the text
	
	# Auto-hide after 2 seconds
	var timer = Timer.new()
	timer.wait_time = 2.0
	timer.one_shot = true
	timer.connect("timeout", Callable(self, "hide_tooltip"))
	tooltip.add_child(timer)
	timer.start()

# Handle done button clicked
func handle_done_button_clicked() -> void:
	print("Submitting defense map...")
	print(game_state.get_map().serialize())
	
	# Start attack scene
	get_tree().change_scene_to_file("res://scenes/attack.tscn")

# Highlight done path
func highlight_done_path(x: int, y: int) -> void:
	var path_sprite = paths.get("%d,%d" % [x, y])
	
	if path_sprite:
		# Store the original tint
		path_sprite.set_meta("original_tint", path_sprite.modulate)
		
		# Set a gray tint
		path_sprite.modulate = Color("808080")

# Reset path appearance
func reset_path_appearance(x: int, y: int) -> void:
	var path_sprite = paths.get("%d,%d" % [x, y])
	
	if path_sprite:
		var original_tint = path_sprite.get_meta("original_tint")
		
		if original_tint:
			path_sprite.modulate = original_tint
		elif not is_path_done(x, y):
			path_sprite.modulate = Color(GameConfig.PATH.tint)
		
		path_sprite.set_meta("original_tint", null)  # Clean up meta

# Load paths
func load_paths() -> void:
	super.load_paths()
	
	for path in game_state.get_map().get_paths():
		var coords = path.split(",")
		var x = int(coords[0])
		var y = int(coords[1])
		done_paths.append(path)
		highlight_done_path(x, y)
	
	if has_done_paths():
		activate_defense_buttons()

# Refund resources
func refund_resources(amount: int) -> void:
	game_state.add_resources(amount)
	resource_component.update_resources(game_state.get_resources())

# Spend resources
func spend_resources(amount: int) -> void:
	game_state.add_resources(-amount)
	resource_component.update_resources(game_state.get_resources())
