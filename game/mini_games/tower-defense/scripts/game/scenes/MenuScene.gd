extends Node2D

# MenuScene class for main menu
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# UI elements
var title_label: Label
var defender_button: Button
var attacker_button: Button
var audio_system

# Called when the node enters the scene tree for the first time
func _ready():
	# Create UI elements
	create_ui()
	
	# Initialize audio system
	audio_system = load("res://scripts/game/systems/ProceduralAudioSystem.gd").new(self)
	add_child(audio_system)

# Create UI elements for menu
func create_ui() -> void:
	# Background
	var background = ColorRect.new()
	background.color = Color(0.1, 0.1, 0.1)
	background.size = get_viewport_rect().size
	add_child(background)
	
	# Container for centered content
	var container = VBoxContainer.new()
	container.size = Vector2(600, 400)
	container.position = (get_viewport_rect().size - container.size) / 2
	add_child(container)
	
	# Title
	title_label = Label.new()
	title_label.text = "Tower Defense"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 64)
	container.add_child(title_label)
	
	# Spacer
	var spacer1 = Control.new()
	spacer1.custom_minimum_size = Vector2(0, 60)
	container.add_child(spacer1)
	
	# Defender button
	defender_button = Button.new()
	defender_button.text = "Play as Defender"
	defender_button.custom_minimum_size = Vector2(300, 60)
	defender_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	defender_button.connect("pressed", Callable(self, "_on_defender_button_pressed"))
	container.add_child(defender_button)
	
	# Spacer
	var spacer2 = Control.new()
	spacer2.custom_minimum_size = Vector2(0, 20)
	container.add_child(spacer2)
	
	# Attacker button
	attacker_button = Button.new()
	attacker_button.text = "Play as Attacker"
	attacker_button.custom_minimum_size = Vector2(300, 60)
	attacker_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	attacker_button.connect("pressed", Callable(self, "_on_attacker_button_pressed"))
	container.add_child(attacker_button)
	
	# Spacer
	var spacer3 = Control.new()
	spacer3.custom_minimum_size = Vector2(0, 40)
	container.add_child(spacer3)
	
	# Credits text
	var credits = Label.new()
	credits.text = "Tower Defense Game"
	credits.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	container.add_child(credits)

# Handle defender button press
func _on_defender_button_pressed() -> void:
	audio_system.generate_ui_click_sound()
	
	# Set game type in GameState
	var game_state = get_node("/root/GameState")
	game_state.game_type = GameConfig.GameType.DEFENDER
	
	# Transition to defender welcome scene
	get_tree().change_scene_to_file("res://scenes/defender_welcome.tscn")

# Handle attacker button press
func _on_attacker_button_pressed() -> void:
	audio_system.generate_ui_click_sound()
	
	# Set game type in GameState
	var game_state = get_node("/root/GameState")
	game_state.game_type = GameConfig.GameType.ATTACKER
	
	# Transition to attacker welcome scene
	get_tree().change_scene_to_file("res://scenes/attacker_welcome.tscn")

# Process frame
func _process(delta: float) -> void:
	# Add any animations or effects for menu screen here
	pass