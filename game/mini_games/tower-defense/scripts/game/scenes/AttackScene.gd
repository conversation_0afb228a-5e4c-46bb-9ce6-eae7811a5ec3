extends "res://scripts/game/scenes/BaseScene.gd"

# AttackScene class for attacker gameplay
# Converted from JavaScript/TypeScript to GDScript

# UI components
var preview: Node2D
var selected_unit: String = ""
# Removed: unit_buttons, unit_count_texts
var tower: Dictionary = {}
var tower_health_bar: Node2D
var units: Dictionary = {}

# New variables for UnitButton integration
const unit_button_scene: PackedScene = preload("res://scenes/ui/components/UnitButton.tscn")
var unit_button_nodes: Dictionary = {} # Stores UnitButton instances by UUID
var tooltip_node: Control = null # Holds the current tooltip
var defense_attack_times: Dictionary = {}

# Called when the node enters the scene tree for the first time
func _ready():
	print("Creating AttackScene...")
	super._ready()
	
	# Use the map from the passed GameState
	if game_state and game_state.get_map():
		# Get tower data from the GameState
		tower = {
			"name": "Tower",
			"owner": "Player",
			"health": game_state.get_tower_health(),
			"max_health": game_state.get_tower_health()
		}
		
		# Initialize the rest of the scene
		create_animations()
		create_preview()
		create_tower_health_bar()
		setup_unit_updates()
		setup_tower_attacks()
		
		# Create unit buttons using the units from GameState
		_create_unit_buttons() # Corrected function name
	else:
		print("Failed to initialize AttackScene")
		# Display error message or redirect to menu
		get_tree().change_scene_to_file("res://scenes/menu.tscn")

# Create animations
func create_animations() -> void:
	# Create animations if needed
	pass

# Create preview
func create_preview() -> void:
	preview = Node2D.new()
	add_child(preview)

# Create unit buttons using the UnitButton scene
func _create_unit_buttons() -> void:
	var available_units = game_state.get_available_units()
	print("Available units:", available_units)
	
	var button_y = GameConfig.UI.TOP_PANEL_HEIGHT + GameConfig.UI.PADDING
	
	for unit_data in available_units:
		var unit_button = unit_button_scene.instantiate() as Control
		
		# Set position and add to UI container
		unit_button.position = Vector2(GameConfig.UI.PADDING, button_y)
		unit_button.size.x = GameConfig.UI.LEFT_PANEL_WIDTH - (GameConfig.UI.PADDING * 2)
		ui_container.add_child(unit_button)
		
		# Setup the button with unit data
		unit_button.setup(unit_data)
		
		# Connect signals from the UnitButton instance
		unit_button.unit_selected.connect(_on_unit_button_selected)
		unit_button.unit_hover_started.connect(_show_unit_tooltip)
		unit_button.unit_hover_ended.connect(_hide_unit_tooltip)
		
		# Store the node reference
		unit_button_nodes[unit_data.uuid] = unit_button
		
		button_y += unit_button.size.y + GameConfig.UI.PADDING

# --- New Signal Handlers for UnitButton ---

# Handle unit selection from a UnitButton
func _on_unit_button_selected(unit_uuid: String):
	select_unit(unit_uuid)

# Show unit tooltip (connected to UnitButton's unit_hover_started signal)
func _show_unit_tooltip(unit_data: Dictionary, button_node: Control) -> void:
	_hide_unit_tooltip() # Ensure any previous tooltip is removed
	
	tooltip_node = Control.new()
	tooltip_node.position = Vector2(GameConfig.UI.LEFT_PANEL_WIDTH, button_node.global_position.y) # Position relative to button
	tooltip_node.name = "unit_tooltip" # Keep name for potential direct access if needed
	ui_container.add_child(tooltip_node)
	
	var padding = 10
	
	var tooltip_text = Label.new()
	tooltip_text.position = Vector2(padding, padding)
	tooltip_text.text = unit_data.get("description", "Základní jednotka")
	tooltip_text.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	tooltip_text.size = Vector2(200, 0) # Width fixed, height adjusts
	tooltip_text.add_theme_font_size_override("font_size", 14)
	tooltip_node.add_child(tooltip_text)
	
	# Adjust label height based on content (more robust calculation)
	var font = tooltip_text.get_theme_font("font")
	var font_size = tooltip_text.get_theme_font_size("font_size")
	var string_size = font.get_string_size(tooltip_text.text, HORIZONTAL_ALIGNMENT_LEFT, tooltip_text.size.x, font_size)
	tooltip_text.size.y = string_size.y
	
	# Create background
	var background = ColorRect.new()
	background.color = Color("000000", 0.9) # Black with 90% opacity
	background.size = tooltip_text.size + Vector2(padding * 2, padding * 2)
	background.position = Vector2(0, 0)
	tooltip_node.add_child(background)
	background.show_behind_parent = true # Show behind the text

# Hide unit tooltip (connected to UnitButton's unit_hover_ended signal)
func _hide_unit_tooltip() -> void:
	if tooltip_node and is_instance_valid(tooltip_node):
		tooltip_node.queue_free()
		tooltip_node = null

# Select unit
func select_unit(unit_type: String) -> void:
	var unit = null
	for u in game_state.get_available_units():
		if u.uuid == unit_type:
			unit = u
			break

	# Check if the unit exists and has count > 0, unless we are deselecting
	if (not unit or unit.count <= 0) and selected_unit != unit_type:
		return

	var new_selection = ""
	var show_preview = false

	# Toggle selection
	if selected_unit == unit_type:
		# Deselecting the current unit
		selected_unit = ""
		clear_previews()
		new_selection = "" # Ensure deselection state is set below
	else:
		# Selecting a new unit (only if count > 0)
		if unit and unit.count > 0:
			var map = game_state.get_map()
			if not map:
				print("Map data not loaded")
				return

			selected_unit = unit_type
			clear_previews()
			show_spawn_points()
			new_selection = unit_type
			show_preview = true
		else:
			# Tried to select a unit with 0 count, do nothing visually
			return

	# Update button visual states
	for button_uuid in unit_button_nodes:
		var button_node = unit_button_nodes[button_uuid] as Control # Cast to Control or specific type if known
		if button_node and button_node.has_method("set_selected"):
			button_node.set_selected(button_uuid == new_selection)

	# If we just selected a new unit, ensure previews are shown
	# (This logic was moved from the 'else' block above)
	# if show_preview:
	#	 clear_previews() # Already cleared above
	#	 show_spawn_points() # Already called above

# Show spawn points
func show_spawn_points() -> void:
	var map = game_state.get_map()
	if not map:
		return
	
	# Clear previous preview
	clear_previews()
	
	# Create new preview node
	preview = Node2D.new()
	game_container.add_child(preview)
	
	# Get grid dimensions
	var max_x = GameConfig.GRID.WIDTH
	var max_y = GameConfig.GRID.HEIGHT
	
	for x in range(max_x):
		for y in range(max_y):
			if is_valid_spawn_point({"x": x, "y": y}):
				# Get terrain for color differentiation
				var terrain = map.get_terrain_at(x, y)
				var terrain_config = GameConfig.TERRAIN_TYPES.get(terrain, {})
				
				# Color based on terrain advantage for attack
				var color = get_terrain_preview_color(terrain_config)
				var alpha = 0.5
				
				# Calculate pixel coordinates
				var pixel_x = x * GameConfig.GRID.CELL_SIZE
				var pixel_y = y * GameConfig.GRID.CELL_SIZE
				
				# Create spawn zone
				var spawn_zone = ColorRect.new()
				spawn_zone.position = Vector2(pixel_x, pixel_y)
				spawn_zone.size = Vector2(GameConfig.GRID.CELL_SIZE, GameConfig.GRID.CELL_SIZE)
				spawn_zone.color = Color(color, alpha)
				spawn_zone.mouse_filter = Control.MOUSE_FILTER_STOP
				
				# Connect signals
				var connect_error = spawn_zone.connect("gui_input", Callable(self, "_on_spawn_zone_input").bind({"x": x, "y": y}, spawn_zone))
				if connect_error != OK:
					print("ERROR connecting gui_input for spawn zone at ", x, ",", y, ": ", connect_error) # DEBUG
				else:
					print("Connected gui_input for spawn zone at ", x, ",", y) # DEBUG
					
				spawn_zone.connect("mouse_entered", Callable(self, "_on_spawn_zone_entered").bind(spawn_zone))
				spawn_zone.connect("mouse_exited", Callable(self, "_on_spawn_zone_exited").bind(spawn_zone, alpha))
				
				preview.add_child(spawn_zone)

# Handle spawn zone input
func _on_spawn_zone_input(event: InputEvent, position: Dictionary, spawn_zone) -> void:
	print("Spawn zone input received: ", event, " at position: ", position, " selected_unit: ", selected_unit) # DEBUG
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		print("Spawn zone LEFT CLICK detected.") # DEBUG
		if selected_unit != "":
			print("Selected unit found (", selected_unit, "), calling place_unit...") # DEBUG
			place_unit(position)
		else:
			print("No unit selected.") # DEBUG

# Handle spawn zone mouse enter
func _on_spawn_zone_entered(spawn_zone) -> void:
	spawn_zone.color.a = 0.7

# Handle spawn zone mouse exit
func _on_spawn_zone_exited(spawn_zone, alpha: float) -> void:
	spawn_zone.color.a = alpha

# Create destruction effect
func create_destruction_effect(position: Dictionary) -> void:
	var x = position.x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	var y = position.y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	
	# Create flash effect
	var flash = ColorRect.new()
	flash.position = Vector2(x - 20, y - 20)
	flash.size = Vector2(40, 40)
	flash.color = Color("ffff00", 0.8)
	game_container.add_child(flash)
	
	# Create tween for fade out
	var tween = create_tween()
	tween.tween_property(flash, "modulate:a", 0.0, 0.3)
	tween.tween_property(flash, "scale", Vector2(2, 2), 0.3)
	tween.tween_callback(Callable(flash, "queue_free"))

# Create combat effect
func create_combat_effect(position: Dictionary) -> void:
	var x = position.x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	var y = position.y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	
	# Create particles
	for i in range(20):
		var particle = ColorRect.new()
		particle.position = Vector2(
			x + (randf() * 40 - 20),
			y + (randf() * 40 - 20)
		)
		particle.size = Vector2(randf() * 5 + 2, randf() * 5 + 2)
		particle.color = Color("ff0000")
		game_container.add_child(particle)
		
		# Create tween for fade out
		var tween = create_tween()
		tween.tween_property(particle, "modulate:a", 0.0, 1.0)
		tween.parallel().tween_property(particle, "scale", Vector2(0, 0), 1.0)
		tween.parallel().tween_property(particle, "position", 
			Vector2(particle.position.x + (randf() * 60 - 30), particle.position.y + (randf() * 60 - 30)), 
			1.0)
		tween.tween_callback(Callable(particle, "queue_free"))

# Check if position is a valid spawn point
func is_valid_spawn_point(position: Dictionary) -> bool:
	var map = game_state.get_map()
	if not map:
		return false
	
	# Basic position validation
	if not is_valid_tile(position.x, position.y):
		return false
	
	# Check if position is on the edge of the map
	var is_on_edge = position.x == 0 or position.x == GameConfig.GRID.WIDTH - 1 or \
					position.y == 0 or position.y == GameConfig.GRID.HEIGHT - 1
	
	# Check if there is a path at this position
	if not is_on_edge or not map.has_path(position.x, position.y):
		return false
	
	return true

# Clear previews
func clear_previews() -> void:
	if preview:
		preview.queue_free()
		preview = null

# Get grid position from pixel coordinates
func get_grid_position(x: float, y: float) -> Dictionary:
	return {
		"x": int(x / GameConfig.GRID.CELL_SIZE),
		"y": int(y / GameConfig.GRID.CELL_SIZE)
	}

# Get terrain preview color
func get_terrain_preview_color(terrain_config: Dictionary) -> Color:
	if terrain_config.size() == 0:
		return Color("00ff00")
	
	# Color based on terrain advantage for attack
	var penalty = terrain_config.get("attackPenalty", 0.0)
	if penalty > 0.25:
		return Color("ff0000")  # Very disadvantageous
	elif penalty > 0.15:
		return Color("ff7f00")  # Moderately disadvantageous
	elif penalty > 0.05:
		return Color("ffff00")  # Slightly disadvantageous
	else:
		return Color("00ff00")  # Advantageous

# Create tower health bar
func create_tower_health_bar() -> void:
	tower_health_bar = Node2D.new()
	# Add to game_container instead of self
	game_container.add_child(tower_health_bar)
	update_tower_health_bar()

# Update tower health bar
func update_tower_health_bar() -> void:
	var x = GameConfig.TOWER.POSITION.X * GameConfig.GRID.CELL_SIZE
	var y = GameConfig.TOWER.POSITION.Y * GameConfig.GRID.CELL_SIZE + (GameConfig.GRID.CELL_SIZE - 4)
	var width = GameConfig.GRID.CELL_SIZE
	var height = 4
	
	# Remove previous health bar elements
	for child in tower_health_bar.get_children():
		child.queue_free()
	
	# Background
	var background = ColorRect.new()
	background.position = Vector2(x, y)
	background.size = Vector2(width, height)
	background.color = Color("ff0000")
	tower_health_bar.add_child(background)
	
	# Health
	var health_width = max(0, (tower.health / tower.max_health) * width)
	var health_bar = ColorRect.new()
	health_bar.position = Vector2(x, y)
	health_bar.size = Vector2(health_width, height)
	health_bar.color = Color("00ff00")
	tower_health_bar.add_child(health_bar)

# Setup unit updates
func setup_unit_updates() -> void:
	# Create timer for unit updates
	var timer = Timer.new()
	timer.wait_time = 0.016  # 60fps update
	timer.autostart = true
	timer.connect("timeout", Callable(self, "update_units"))
	add_child(timer)

# Update units
func update_units() -> void:
	# Get delta time for physics-independent movement
	var delta = get_process_delta_time()
	
	for unit_id in units:
		var unit = units[unit_id]
		if not unit: continue
		unit.move_along_path(delta) # Pass delta time
		check_defense_collisions(unit)
		if check_tower_collision(unit):
			attack_tower(unit)

# Check defense collisions
func check_defense_collisions(unit) -> void:
	var map = game_state.get_map()
	var defenses = map.get_all_defenses()
	
	for key in defenses:
		var defense = defenses[key]
		var coords = key.split(",")
		var def_x = int(coords[0])
		var def_y = int(coords[1])
		
		var defense_pos = {
			"x": def_x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
			"y": def_y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
		}
		
		var distance = sqrt(pow(unit.position.x - defense_pos.x, 2) + pow(unit.position.y - defense_pos.y, 2))
		var defense_config = game_state.get_defense_config(defense.uuid)
		if defense_config.is_empty():
			continue
		
		# Handle ranged defenses
		if defense_config.has("range") and defense_config.has("damage"):
			var last_attack_time = defense_attack_times.get(key, 0)
			var now = Time.get_ticks_msec()
			var attack_speed = defense_config.get("attackSpeed", 1000)  # Default 1 second if not specified
			
			if distance <= defense_config.range * GameConfig.GRID.CELL_SIZE and \
				now - last_attack_time >= attack_speed:
				defense_attack(defense_pos, unit, defense_config)
				defense_attack_times[key] = now

# Defense attack
func defense_attack(defense_pos: Dictionary, unit, defense_config: Dictionary) -> void:
	# Create projectile effect
	var projectile = ColorRect.new()
	projectile.position = Vector2(defense_pos.x - 3, defense_pos.y - 3)
	projectile.size = Vector2(6, 6)
	projectile.color = Color("00ffff")
	game_container.add_child(projectile)
	
	# Create tween for projectile movement
	var tween = create_tween()
	tween.tween_property(projectile, "position", Vector2(unit.position.x - 3, unit.position.y - 3), 0.3)
	tween.tween_callback(Callable(self, "_on_projectile_hit").bind(projectile, unit, defense_config.damage))

# Handle projectile hit
func _on_projectile_hit(projectile, unit, damage: int) -> void:
	projectile.queue_free()
	
	# Deal damage to the unit
	unit.take_damage(damage)
	
	# Create combat effect
	create_combat_effect({
		"x": int(unit.position.x / GameConfig.GRID.CELL_SIZE),
		"y": int(unit.position.y / GameConfig.GRID.CELL_SIZE)
	})

# Check tower collision
func check_tower_collision(unit) -> bool:
	var tower_bounds = Rect2(
		GameConfig.TOWER.POSITION.X * GameConfig.GRID.CELL_SIZE,
		GameConfig.TOWER.POSITION.Y * GameConfig.GRID.CELL_SIZE,
		GameConfig.GRID.CELL_SIZE,
		GameConfig.GRID.CELL_SIZE
	)
	
	return tower_bounds.has_point(unit.position)

# Attack tower
func attack_tower(unit) -> void:
	game_state.update_tower_health(unit.damage)
	update_tower_health_bar()
	create_combat_effect({"x": GameConfig.TOWER.POSITION.X, "y": GameConfig.TOWER.POSITION.Y})
	
	# Convert pixel coordinates back to grid coordinates
	var grid_x = int(unit.position.x / GameConfig.GRID.CELL_SIZE)
	var grid_y = int(unit.position.y / GameConfig.GRID.CELL_SIZE)
	
	units.erase(unit.id)
	unit.queue_free()
	create_destruction_effect({"x": grid_x, "y": grid_y})
	
	if game_state.get_tower_health() <= 0:
		handle_tower_destroyed()

# Handle tower destroyed
func handle_tower_destroyed() -> void:
	create_destruction_effect({"x": GameConfig.TOWER.POSITION.X, "y": GameConfig.TOWER.POSITION.Y})
	
	# Create timer for game over
	var timer = Timer.new()
	timer.wait_time = 1.0
	timer.one_shot = true
	timer.connect("timeout", Callable(self, "_on_game_over_timer_timeout"))
	add_child(timer)
	timer.start()

# Handle game over timer timeout
func _on_game_over_timer_timeout() -> void:
	get_tree().change_scene_to_file("res://scenes/game_over.tscn")

# Place unit
func place_unit(position: Dictionary) -> void:
	print("place_unit called with position: ", position, " selected_unit: ", selected_unit) # DEBUG
	if selected_unit == "":
		print("place_unit: No unit selected, returning.") # DEBUG
		return
	
	var unit_data = null
	for u in game_state.get_available_units():
		if u.uuid == selected_unit:
			unit_data = u
			break
	
	print("place_unit: Found unit_data: ", unit_data) # DEBUG
	if not unit_data or unit_data.count <= 0:
		print("place_unit: Unit data not found or count is zero, returning.") # DEBUG
		return
	
	print("place_unit: Placing unit...") # DEBUG
	var unit_config = unit_data
	var unit = load("res://scripts/game/objects/units/Unit.gd").new()
	# Removed 'self' argument as initialize expects only 3 arguments (x, y, config)
	unit.initialize(position.x, position.y, unit_config)
	game_container.add_child(unit)
	# Set position AFTER adding to the tree to avoid appearing at (0,0) briefly
	unit.position = Vector2(
		position.x * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2,
		position.y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	)
	units[unit.id] = unit
	print("place_unit: Unit instance created and added: ", unit.id) # DEBUG
	
	game_state.use_unit(selected_unit)
	update_unit_count_text(selected_unit)
	clear_previews()
	select_unit(selected_unit)  # Reselect to update preview
	print("place_unit: Unit placed successfully.") # DEBUG

# Update unit count text on the corresponding UnitButton node
func update_unit_count_text(unit_type: String) -> void:
	var unit_button = unit_button_nodes.get(unit_type) as Control
	if unit_button:
		# Find the unit data again to get the latest count
		var unit_data = null
		for u in game_state.get_available_units():
			if u.uuid == unit_type:
				unit_data = u
				break
		
		if unit_data:
			unit_button.update_count(unit_data.count)
		else:
			# If unit data is somehow missing, update button to show 0
			unit_button.update_count(0)

# Setup tower attacks
func setup_tower_attacks() -> void:
	# Create timer for tower attacks
	var timer = Timer.new()
	timer.wait_time = 1.0  # Attack every second
	timer.autostart = true
	timer.connect("timeout", Callable(self, "tower_attack"))
	add_child(timer)

# Tower attack
func tower_attack() -> void:
	# Find the closest unit within range
	var tower_x = GameConfig.TOWER.POSITION.X * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	var tower_y = GameConfig.TOWER.POSITION.Y * GameConfig.GRID.CELL_SIZE + GameConfig.GRID.CELL_SIZE / 2
	var tower_range = GameConfig.GRID.CELL_SIZE * 3  # 3 cells range
	
	var closest_unit = null
	var closest_distance = INF
	
	for unit_id in units:
		var unit = units[unit_id]
		var distance = sqrt(pow(unit.position.x - tower_x, 2) + pow(unit.position.y - tower_y, 2))
		
		if distance <= tower_range and distance < closest_distance:
			closest_unit = unit
			closest_distance = distance
	
	if closest_unit:
		# Create projectile effect
		var projectile = ColorRect.new()
		projectile.position = Vector2(tower_x - 4, tower_y - 4)
		projectile.size = Vector2(8, 8)
		projectile.color = Color("ff0000")
		game_container.add_child(projectile)
		
		# Create tween for projectile movement
		var tween = create_tween()
		tween.tween_property(projectile, "position", Vector2(closest_unit.position.x - 4, closest_unit.position.y - 4), 0.2)
		tween.tween_callback(Callable(self, "_on_tower_projectile_hit").bind(projectile, closest_unit))

# Handle tower projectile hit
func _on_tower_projectile_hit(projectile, unit) -> void:
	projectile.queue_free()
	
	# Check if the unit still exists before applying damage/effects
	if is_instance_valid(unit):
		# Deal damage to the unit
		unit.take_damage(20)  # 20 damage per hit
		
		# Create combat effect only if unit is still valid
		create_combat_effect({
				"x": int(unit.position.x / GameConfig.GRID.CELL_SIZE),
				"y": int(unit.position.y / GameConfig.GRID.CELL_SIZE)
		})

# Override _input to handle clicks directly
func _input(event: InputEvent) -> void:
	# Check if it's a left mouse button press
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		print("_input: Left Click Detected at Viewport Pos: ", event.position) # DEBUG

		# Ensure game_container is valid
		if not is_instance_valid(game_container):
			print("_input: game_container is not valid!") # DEBUG
			return

		# Convert viewport coordinates to game_container's local coordinates
		var local_pos = game_container.get_global_transform_with_canvas().affine_inverse() * event.position
		print("_input: Converted to Local Pos (in game_container): ", local_pos) # DEBUG

		# Calculate grid tile coordinates
		var tile_x = int(local_pos.x / GameConfig.GRID.CELL_SIZE)
		var tile_y = int(local_pos.y / GameConfig.GRID.CELL_SIZE)
		print("_input: Calculated Tile Coords: x=", tile_x, ", y=", tile_y) # DEBUG

		# Check if a unit is selected and the tile is a valid spawn point
		if selected_unit != "":
			print("_input: Unit '", selected_unit, "' is selected.") # DEBUG
			var click_pos_dict = {"x": tile_x, "y": tile_y}
			if is_valid_spawn_point(click_pos_dict):
				print("_input: Clicked tile IS a valid spawn point. Calling place_unit...") # DEBUG
				place_unit(click_pos_dict)
				get_viewport().set_input_as_handled() # Consume the event so it doesn't propagate further
				return # Exit after handling
			else:
				print("_input: Clicked tile is NOT a valid spawn point.") # DEBUG
		else:
			print("_input: No unit selected.") # DEBUG

	# If the event wasn't handled here, pass it to the base class (important for mouse motion etc.)
	# Check if the base class actually implements _input before calling
	if get_script().get_base_script() and get_script().get_base_script().has_method("_input"):
			super._input(event)
