extends Node2D

# AttackerWelcomeScene class for attacker mode introduction
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# UI elements
var title_label: Label
var description_label: Label
var start_button: Button
var back_button: Button
var audio_system

# Called when the node enters the scene tree for the first time
func _ready():
	# Create UI elements
	create_ui()
	
	# Initialize audio system
	audio_system = load("res://scripts/game/systems/ProceduralAudioSystem.gd").new(self)
	add_child(audio_system)

# Create UI elements for welcome screen
func create_ui() -> void:
	# Background
	var background = ColorRect.new()
	background.color = Color(0.1, 0.1, 0.1)
	background.size = get_viewport_rect().size
	add_child(background)
	
	# Container for centered content
	var container = VBoxContainer.new()
	container.size = Vector2(800, 500)
	container.position = (get_viewport_rect().size - container.size) / 2
	add_child(container)
	
	# Title
	title_label = Label.new()
	title_label.text = "Attacker Mode"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 48)
	container.add_child(title_label)
	
	# Spacer
	var spacer1 = Control.new()
	spacer1.custom_minimum_size = Vector2(0, 30)
	container.add_child(spacer1)
	
	# Description
	description_label = Label.new()
	description_label.text = """As an attacker, your goal is to destroy the enemy tower.

1. Deploy units from the edge of the map
2. Units will follow the path to the enemy tower
3. Different units have different strengths and weaknesses
4. Use your resources wisely to overwhelm the defenses

Pay attention to terrain effects on your units!
Some terrains slow down your units or reduce their attack power."""
	description_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	description_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	container.add_child(description_label)
	
	# Spacer
	var spacer2 = Control.new()
	spacer2.custom_minimum_size = Vector2(0, 40)
	container.add_child(spacer2)
	
	# Button container for horizontal layout
	var button_container = HBoxContainer.new()
	button_container.size_flags_horizontal = Control.SIZE_FILL
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.add_child(button_container)
	
	# Back button
	back_button = Button.new()
	back_button.text = "Back to Menu"
	back_button.custom_minimum_size = Vector2(200, 50)
	back_button.connect("pressed", Callable(self, "_on_back_button_pressed"))
	button_container.add_child(back_button)
	
	# Spacer between buttons
	var button_spacer = Control.new()
	button_spacer.custom_minimum_size = Vector2(40, 0)
	button_container.add_child(button_spacer)
	
	# Start button
	start_button = Button.new()
	start_button.text = "Start Game"
	start_button.custom_minimum_size = Vector2(200, 50)
	start_button.connect("pressed", Callable(self, "_on_start_button_pressed"))
	button_container.add_child(start_button)

# Handle back button press
func _on_back_button_pressed() -> void:
	audio_system.generate_ui_click_sound()
	get_tree().change_scene_to_file("res://scenes/menu.tscn")

# Handle start button press
func _on_start_button_pressed() -> void:
	audio_system.generate_ui_click_sound()
	get_tree().change_scene_to_file("res://scenes/attack.tscn")

# Process frame
func _process(delta: float) -> void:
	# Add any animations or effects for welcome screen here
	pass
