extends Node2D

# BootScene class for initial game loading
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Loading properties
var loading_text: Label
var progress_bar: ProgressBar
var loading_complete: bool = false

# Called when the node enters the scene tree for the first time
func _ready():
	# Create UI elements
	create_ui()
	
	# Start loading process
	start_loading()

# Create UI elements for loading screen
func create_ui() -> void:
	# Background
	var background = ColorRect.new()
	background.color = Color(0.1, 0.1, 0.1)
	background.size = get_viewport_rect().size
	add_child(background)
	
	# Container for centered content
	var container = VBoxContainer.new()
	container.size = Vector2(600, 200)
	container.position = (get_viewport_rect().size - container.size) / 2
	add_child(container)
	
	# Title
	var title = Label.new()
	title.text = "Tower Defense"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 48)
	container.add_child(title)
	
	# Spacer
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 40)
	container.add_child(spacer)
	
	# Loading text
	loading_text = Label.new()
	loading_text.text = "Loading..."
	loading_text.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	container.add_child(loading_text)
	
	# Progress bar
	progress_bar = ProgressBar.new()
	progress_bar.max_value = 100
	progress_bar.value = 0
	progress_bar.custom_minimum_size = Vector2(600, 20)
	container.add_child(progress_bar)

# Start the loading process
func start_loading() -> void:
	# Connect to API
	var api = get_node("/root/KokumeApi")
	api.connect("data_loaded", Callable(self, "_on_data_loaded"))
	api.connect("data_error", Callable(self, "_on_data_error"))
	
	# Start loading assets
	load_assets()
	
	# Get initial data from API
	api.get_initial_data()

# Load game assets
func load_assets() -> void:
	# Simulate loading process with a timer
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 0.01
	timer.connect("timeout", Callable(self, "_on_load_progress").bind(timer))
	timer.start()

# Handle load progress
func _on_load_progress(timer: Timer) -> void:
	# Simulate loading progress
	progress_bar.value += 2
	
	if progress_bar.value >= 100:
		timer.stop()
		
		# Check if API data is also loaded
		check_loading_complete()

# Handle API data loaded
func _on_data_loaded(data) -> void:
	# Initialize game state with loaded data
	var game_state = get_node("/root/GameState")
	game_state.initialize(data)
	
	# Check if assets are also loaded
	check_loading_complete()

# Handle API data error
func _on_data_error(error_message) -> void:
	loading_text.text = "Error: " + error_message
	loading_text.add_theme_color_override("font_color", Color(1, 0, 0))

# Check if loading is complete
func check_loading_complete() -> void:
	var game_state = get_node("/root/GameState")
	
	if progress_bar.value >= 100 and game_state.get_map() != null:
		loading_complete = true
		loading_text.text = "Loading complete! Starting game..."
		
		# Wait a moment before transitioning to menu
		await get_tree().create_timer(1.0).timeout
		get_tree().change_scene_to_file("res://scenes/menu.tscn")

# Process frame
func _process(delta: float) -> void:
	# Add any animations or effects for loading screen here
	pass
