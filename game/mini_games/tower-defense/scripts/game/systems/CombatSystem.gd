extends Node

# CombatSystem class for handling combat interactions
# Converted from JavaScript/TypeScript to GDScript

# Preload required resources
const GameConfig = preload("res://config/GameConfig.gd")

# Combat properties
var scene  # Reference to the scene
var last_combat_time: Dictionary = {}  # For cooldown between attacks

# Initialize the combat system
func _init(parent_scene):
	scene = parent_scene

# Handle combat between unit and defense
func handle_combat(unit, defense) -> int:
	# Get terrain at defense position
	var terrain = scene.map.get_tile_at(
		int(defense.position.x / GameConfig.GRID.CELL_SIZE),
		int(defense.position.y / GameConfig.GRID.CELL_SIZE)
	)
	
	# Get terrain modifiers
	var terrain_modifiers = get_terrain_modifiers(terrain)
	
	# Calculate final damage
	var damage = calculate_damage(unit, defense, terrain_modifiers.attack_penalty, terrain_modifiers.defense_bonus)
	
	# Apply damage
	apply_damage(unit, defense, damage)
	
	# Play combat effects
	play_combat_effects(unit, defense, terrain, damage)
	
	return damage

# Get terrain modifiers
func get_terrain_modifiers(terrain) -> Dictionary:
	if not terrain:
		return {"attack_penalty": 0.0, "defense_bonus": 0.0}
	
	var terrain_config = terrain.get_effects()
	return {
		"attack_penalty": terrain_config.get("attackPenalty", 0.0),
		"defense_bonus": terrain_config.get("defenseBonus", 0.0)
	}

# Calculate damage based on unit, defense, and terrain modifiers
func calculate_damage(unit, defense, attack_penalty: float, defense_bonus: float) -> int:
	# Base damage from unit
	var base_damage = unit.damage
	
	# Apply terrain attack penalty
	base_damage *= (1.0 - attack_penalty)
	
	# Apply terrain defense bonus
	var effective_defense = defense.health * (1.0 + defense_bonus)
	
	# Calculate final damage
	var final_damage = max(0, base_damage - (effective_defense * 0.5))
	
	# Add random factor (±10%)
	var random_factor = 0.9 + randf() * 0.2
	
	return int(final_damage * random_factor)

# Apply damage to defense and handle counter-damage
func apply_damage(unit, defense, damage: int) -> int:
	# Check cooldown
	var now = Time.get_ticks_msec()
	var last_attack = last_combat_time.get(unit.id, 0)
	var cooldown = unit.get("attackSpeed", 1000)  # 1 second default
	
	if now - last_attack < cooldown:
		return 0
	
	# Apply damage
	defense.health -= damage
	last_combat_time[unit.id] = now
	
	# Counter-damage from defense (if exists)
	if defense.has("counterDamage") and defense.counterDamage > 0:
		unit.take_damage(defense.counterDamage)
		
		# Check if unit died
		if unit.health <= 0:
			handle_unit_death(unit)
	
	# Check if defense was destroyed
	if defense.health <= 0:
		handle_defense_destruction(defense)
	
	return damage

# Play combat visual and audio effects
func play_combat_effects(unit, defense, terrain, damage: int) -> void:
	# Create impact effect
	scene.create_impact_effect({
		"x": defense.position.x,
		"y": defense.position.y,
		"damage": damage,
		"critical": damage > unit.damage * 1.5
	})
	
	# Terrain effect
	if terrain and terrain.has_method("play_combat_effect"):
		terrain.play_combat_effect(damage / float(unit.damage))
	
	# Defense damage animation
	if defense.has_method("play_damage_animation"):
		defense.play_damage_animation(damage)
	
	# Sound effects
	play_sound(unit, defense, damage)

# Handle unit death
func handle_unit_death(unit) -> void:
	# Create death effect
	scene.create_death_effect({
		"x": unit.position.x,
		"y": unit.position.y,
		"type": "unit",
		"unitType": unit.get("type", "generic")
	})
	
	# Remove unit
	unit.queue_free()

# Handle defense destruction
func handle_defense_destruction(defense) -> void:
	# Create destruction effect
	scene.create_death_effect({
		"x": defense.position.x,
		"y": defense.position.y,
		"type": "defense",
		"defenseType": defense.get("type", "generic")
	})
	
	# Remove defense
	defense.queue_free()

# Play sound effects for combat
func play_sound(unit, defense, damage: int) -> void:
	var volume = min(1.0, damage / 100.0)  # Volume based on damage
	var detune = randf_range(-100, 100)  # Random pitch variation
	
	var audio_player = AudioStreamPlayer.new()
	scene.add_child(audio_player)
	
	if damage > unit.damage * 1.5:
		# Critical hit
		audio_player.stream = load("res://assets/audio/critical_hit.wav")
	else:
		# Normal hit
		audio_player.stream = load("res://assets/audio/hit.wav")
	
	audio_player.volume_db = linear_to_db(volume)
	audio_player.pitch_scale = 1.0 + (detune / 1000.0)
	audio_player.play()
	
	# Auto-remove player when done
	audio_player.connect("finished", Callable(audio_player, "queue_free"))

# Clear combat data for a unit
func clear_unit_data(unit_id: String) -> void:
	if last_combat_time.has(unit_id):
		last_combat_time.erase(unit_id)