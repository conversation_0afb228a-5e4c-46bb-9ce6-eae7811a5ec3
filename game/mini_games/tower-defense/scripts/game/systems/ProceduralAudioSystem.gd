extends Node

# ProceduralAudioSystem class for generating audio effects
# Converted from JavaScript/TypeScript to GDScript

# Audio properties
var scene  # Reference to the scene

# Initialize the audio system
func _init(parent_scene):
	scene = parent_scene

# Generate defense sound based on type
func generate_defense_sound(type: String) -> void:
	var player = AudioStreamPlayer.new()
	scene.add_child(player)
	
	# Configure sound based on defense type
	match type:
		"LASER":
			configure_laser_sound(player)
		"CANNON":
			configure_cannon_sound(player)
		"SHIELD":
			configure_shield_sound(player)
		_:  # Default
			configure_default_sound(player)
	
	# Play sound and auto-remove when done
	player.play()
	player.connect("finished", Callable(player, "queue_free"))

# Configure laser sound
func configure_laser_sound(player: AudioStreamPlayer) -> void:
	# In Godot, we'll use a pre-generated sound or create one with AudioStreamGenerator
	# For this example, we'll use a pre-generated sound
	var stream = create_sine_wave(880, 0.1, 110)
	player.stream = stream
	player.volume_db = linear_to_db(0.3)

# Configure cannon sound
func configure_cannon_sound(player: AudioStreamPlayer) -> void:
	var stream = create_square_wave(55, 0.2, 22)
	player.stream = stream
	player.volume_db = linear_to_db(0.5)

# Configure shield sound
func configure_shield_sound(player: AudioStreamPlayer) -> void:
	var stream = create_triangle_wave(440, 0.1, 880)
	player.stream = stream
	player.volume_db = linear_to_db(0.2)

# Configure default sound
func configure_default_sound(player: AudioStreamPlayer) -> void:
	var stream = create_sine_wave(440, 0.1)
	player.stream = stream
	player.volume_db = linear_to_db(0.2)

# Generate path placement sound
func generate_path_placement_sound(success: bool) -> void:
	var player = AudioStreamPlayer.new()
	scene.add_child(player)
	
	if success:
		# Positive sound for successful path placement
		var stream = create_sine_wave(440, 0.2, 660)
		player.stream = stream
		player.volume_db = linear_to_db(0.2)
	else:
		# Negative sound for invalid placement
		var stream = create_square_wave(220, 0.15, 110)
		player.stream = stream
		player.volume_db = linear_to_db(0.15)
	
	player.play()
	player.connect("finished", Callable(player, "queue_free"))

# Generate no money sound
func generate_no_money_sound() -> void:
	var player = AudioStreamPlayer.new()
	scene.add_child(player)
	
	# Two-tone warning sound
	var stream = create_square_wave_two_tone(220, 180, 0.3)
	player.stream = stream
	player.volume_db = linear_to_db(0.2)
	
	player.play()
	player.connect("finished", Callable(player, "queue_free"))

# Generate defense destroy sound
func generate_defense_destroy_sound() -> void:
	var player = AudioStreamPlayer.new()
	scene.add_child(player)
	
	# Explosive sound with filter
	var stream = create_sawtooth_wave(220, 0.3, 44)
	player.stream = stream
	player.volume_db = linear_to_db(0.3)
	
	# Add low-pass filter
	var effect = AudioEffectLowPassFilter.new()
	effect.cutoff_hz = 1000
	
	var effect_idx = AudioServer.get_bus_effect_count(AudioServer.get_bus_index("Master"))
	AudioServer.add_bus_effect(AudioServer.get_bus_index("Master"), effect, effect_idx)
	
	# Store effect index for removal
	player.set_meta("effect_idx", effect_idx)
	
	# Remove effect after sound finishes
	player.connect("finished", Callable(self, "_remove_effect"))
	
	player.play()
	player.connect("finished", Callable(player, "queue_free"))

# Generate UI click sound
func generate_ui_click_sound() -> void:
	var player = AudioStreamPlayer.new()
	scene.add_child(player)
	
	# Short, sharp sound for UI interactions
	var stream = create_sine_wave(880, 0.05)
	player.stream = stream
	player.volume_db = linear_to_db(0.1)
	
	player.play()
	player.connect("finished", Callable(player, "queue_free"))

# Generate path complete sound
func generate_path_complete_sound() -> void:
	var player1 = AudioStreamPlayer.new()
	var player2 = AudioStreamPlayer.new()
	scene.add_child(player1)
	scene.add_child(player2)
	
	# First note (C5)
	var stream1 = create_sine_wave(523.25, 0.1)
	player1.stream = stream1
	player1.volume_db = linear_to_db(0.2)
	
	# Second note (G5) - starts after the first one ends
	var stream2 = create_sine_wave(783.99, 0.1)
	player2.stream = stream2
	player2.volume_db = linear_to_db(0.2)
	
	# Play first note
	player1.play()
	player1.connect("finished", Callable(player1, "queue_free"))
	
	# Play second note after delay
	await get_tree().create_timer(0.1).timeout
	player2.play()
	player2.connect("finished", Callable(player2, "queue_free"))

# Helper function to remove audio effect
func _remove_effect(player: AudioStreamPlayer) -> void:
	# Get effect index from player metadata
	var effect_idx = player.get_meta("effect_idx")
	AudioServer.remove_bus_effect(AudioServer.get_bus_index("Master"), effect_idx)

# Helper function to create a sine wave
func create_sine_wave(start_freq: float, duration: float, end_freq: float = -1) -> AudioStreamWAV:
	var sample_rate = 44100
	var samples = int(duration * sample_rate)
	var data = PackedByteArray()
	
	for i in range(samples):
		var t = float(i) / sample_rate
		var freq
		
		if end_freq > 0:
			# Linear interpolation between start and end frequency
			freq = lerp(start_freq, end_freq, t / duration)
		else:
			freq = start_freq
		
		var amplitude = 0.5 * (1.0 - t / duration)  # Fade out
		var sample = sin(TAU * freq * t) * amplitude
		
		# Convert to 16-bit PCM
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.stereo = false
	stream.data = data
	stream.mix_rate = sample_rate
	
	return stream

# Helper function to create a square wave
func create_square_wave(start_freq: float, duration: float, end_freq: float = -1) -> AudioStreamWAV:
	var sample_rate = 44100
	var samples = int(duration * sample_rate)
	var data = PackedByteArray()
	
	for i in range(samples):
		var t = float(i) / sample_rate
		var freq
		
		if end_freq > 0:
			freq = lerp(start_freq, end_freq, t / duration)
		else:
			freq = start_freq
		
		var amplitude = 0.5 * (1.0 - t / duration)  # Fade out
		var sample = 1.0 if sin(TAU * freq * t) > 0 else -1.0
		sample *= amplitude
		
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.stereo = false
	stream.data = data
	stream.mix_rate = sample_rate
	
	return stream

# Helper function to create a triangle wave
func create_triangle_wave(start_freq: float, duration: float, end_freq: float = -1) -> AudioStreamWAV:
	var sample_rate = 44100
	var samples = int(duration * sample_rate)
	var data = PackedByteArray()
	
	for i in range(samples):
		var t = float(i) / sample_rate
		var freq
		
		if end_freq > 0:
			freq = lerp(start_freq, end_freq, t / duration)
		else:
			freq = start_freq
		
		var amplitude = 0.5 * (1.0 - t / duration)  # Fade out
		var phase = fmod(freq * t, 1.0)
		var sample = 2.0 * abs(2.0 * phase - 1.0) - 1.0
		sample *= amplitude
		
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.stereo = false
	stream.data = data
	stream.mix_rate = sample_rate
	
	return stream

# Helper function to create a sawtooth wave
func create_sawtooth_wave(start_freq: float, duration: float, end_freq: float = -1) -> AudioStreamWAV:
	var sample_rate = 44100
	var samples = int(duration * sample_rate)
	var data = PackedByteArray()
	
	for i in range(samples):
		var t = float(i) / sample_rate
		var freq
		
		if end_freq > 0:
			freq = lerp(start_freq, end_freq, t / duration)
		else:
			freq = start_freq
		
		var amplitude = 0.5 * (1.0 - t / duration)  # Fade out
		var phase = fmod(freq * t, 1.0)
		var sample = 2.0 * phase - 1.0
		sample *= amplitude
		
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.stereo = false
	stream.data = data
	stream.mix_rate = sample_rate
	
	return stream

# Helper function to create a two-tone square wave
func create_square_wave_two_tone(freq1: float, freq2: float, duration: float) -> AudioStreamWAV:
	var sample_rate = 44100
	var samples = int(duration * sample_rate)
	var data = PackedByteArray()
	
	var half_samples = samples / 2
	
	# First tone
	for i in range(half_samples):
		var t = float(i) / sample_rate
		var amplitude = 0.5
		var sample = 1.0 if sin(TAU * freq1 * t) > 0 else -1.0
		sample *= amplitude
		
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	# Second tone
	for i in range(half_samples, samples):
		var t = float(i) / sample_rate
		var amplitude = 0.5 * (1.0 - (t - duration/2) / (duration/2))  # Fade out
		var sample = 1.0 if sin(TAU * freq2 * t) > 0 else -1.0
		sample *= amplitude
		
		var sample_int = int(sample * 32767)
		data.append(sample_int & 0xFF)
		data.append((sample_int >> 8) & 0xFF)
	
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.stereo = false
	stream.data = data
	stream.mix_rate = sample_rate
	
	return stream