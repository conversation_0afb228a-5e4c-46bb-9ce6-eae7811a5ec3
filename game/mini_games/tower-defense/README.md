# Tower Defense Game - Godot Migration

This project is a migration of a JavaScript/TypeScript tower defense game to the Godot Engine.

## Project Structure

The project follows a structured organization:

```
godot/
├── assets/
│   ├── textures/
│   │   ├── terrain/
│   │   ├── units/
│   │   ├── defenses/
│   │   └── ui/
│   └── audio/
├── scenes/
│   ├── boot.tscn
│   ├── menu.tscn
│   ├── defender_welcome.tscn
│   ├── attacker_welcome.tscn
│   ├── defense.tscn
│   └── attack.tscn
├── scripts/
│   ├── autoload/
│   │   ├── GameState.gd
│   │   └── KokumeApi.gd
│   ├── game/
│   │   ├── GameMap.gd
│   │   ├── objects/
│   │   │   ├── terrain/
│   │   │   │   └── TerrainTile.gd
│   │   │   └── units/
│   │   │       └── Unit.gd
│   │   ├── scenes/
│   │   │   ├── BaseScene.gd
│   │   │   ├── BootScene.gd
│   │   │   ├── MenuScene.gd
│   │   │   ├── DefenderWelcomeScene.gd
│   │   │   ├── AttackerWelcomeScene.gd
│   │   │   ├── DefenseScene.gd
│   │   │   └── AttackScene.gd
│   │   ├── systems/
│   │   │   ├── CombatSystem.gd
│   │   │   └── ProceduralAudioSystem.gd
│   │   └── ui/
│   │       └── components/
│   │           ├── DefenseButton.gd
│   │           ├── DefenseInfo.gd
│   │           ├── DoneButton.gd
│   │           ├── PathButton.gd
│   │           ├── ResourceComponent.gd
│   │           └── TerrainInfo.gd
└── config/
    └── GameConfig.gd
```

## Game Overview

This is a tower defense game with two playable modes:

1. **Defender Mode**: Build paths and place defensive structures to protect your tower from enemy attacks.
2. **Attacker Mode**: Deploy units to attack the enemy tower, navigating through the defensive structures.

## Key Features

- Grid-based gameplay with different terrain types
- Path building system
- Various defensive structures with different abilities
- Multiple unit types with different strengths and weaknesses
- Resource management
- Procedurally generated audio effects
- API integration for game data

## Technical Implementation

The game is implemented using Godot 4.x with GDScript. Key technical aspects include:

- **Singleton Pattern**: Used for global state management (GameState, KokumeApi)
- **Component-Based Design**: UI and game elements are modular and reusable
- **Scene Inheritance**: BaseScene provides common functionality for game scenes
- **Procedural Generation**: Audio and some visual effects are generated procedurally
- **API Integration**: Game data can be loaded from an external API

## Controls

- **Mouse**: Click to interact with UI elements and game grid
- **Left Click**: Place paths/defenses in Defender mode, deploy units in Attacker mode

## Development Notes

- The game uses a mock API by default for development purposes
- Assets are generated programmatically, no external assets are required
- The game is designed to be easily extensible with new units, defenses, and terrain types

## Migration Notes

This project was migrated from a JavaScript/TypeScript implementation using Phaser.js to Godot Engine. The migration process involved:

1. Converting JavaScript/TypeScript classes to GDScript
2. Replacing Phaser.js scene management with Godot's scene system
3. Implementing Godot-specific rendering and input handling
4. Converting Web Audio API to Godot's audio system
5. Adapting the UI system to Godot's Control nodes

## Future Improvements

- Implement additional unit types and defensive structures
- Add more terrain effects and interactions
- Enhance visual effects and animations
- Implement multiplayer functionality
- Add sound effects and background music