[gd_scene load_steps=3 format=3 uid="uid://lmnopq8aa012"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://dvuip52g0piax" path="res://scripts/ui/InventoryModal.gd" id="2_inventory_modal_script"]

[node name="InventoryModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_inventory_modal_script")

[node name="TitleLabel" parent="Modal/MarginContainer/VBoxContainer" index="0"]
text = "Inventory"

[node name="ScrollContainer" type="ScrollContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="0"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="InventoryGrid" type="GridContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
columns = 4

[node name="EquipmentPanel" type="PanelContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="1"]
layout_mode = 2
