[gd_scene load_steps=3 format=3 uid="uid://stuvwx4568aa"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://cgv2gmpvqdsg7" path="res://scripts/ui/BuildModal.gd" id="2_build_modal_script"]

[node name="BuildModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_build_modal_script")

[node name="TitleLabel" parent="Modal/MarginContainer/VBoxContainer" index="0"]
text = "Build New Building"

[node name="ContentContainer" parent="Modal/MarginContainer/VBoxContainer" index="2"]
theme_override_constants/separation = 8

[node name="HBoxContainer" type="HBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LeftPanel" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer" index="0"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 5

[node name="Label" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/LeftPanel" index="0"]
layout_mode = 2
text = "Available Buildings:"

[node name="ScrollContainer" type="ScrollContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/LeftPanel" index="1"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="BuildingOptionsContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/LeftPanel/ScrollContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 5

[node name="PlaceholderLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/LeftPanel/ScrollContainer/BuildingOptionsContainer" index="0"]
layout_mode = 2
text = "Loading build options..."
horizontal_alignment = 1

[node name="RightPanel" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer" index="1"]
custom_minimum_size = Vector2(180, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 5

[node name="BuildingDetailsLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel" index="0"]
layout_mode = 2
text = "Building Details:"

[node name="NameContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel" index="1"]
layout_mode = 2

[node name="NameLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/NameContainer" index="0"]
layout_mode = 2
text = "Name:"

[node name="BuildingNameInput" type="LineEdit" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/NameContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "Enter building name"

[node name="DescriptionContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel" index="2"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2
size_flags_vertical = 0

[node name="DescriptionLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/DescriptionContainer" index="0"]
layout_mode = 2
text = "Description:"

[node name="BuildingDescriptionLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/DescriptionContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
text = "Select a building type to see its description."
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2
text_overrun_behavior = 3
max_lines_visible = 4
text_direction = 1

[node name="CostContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel" index="3"]
layout_mode = 2

[node name="CostLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/CostContainer" index="0"]
layout_mode = 2
text = "Cost:"

[node name="BuildingCostContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/RightPanel/CostContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="1"]
layout_mode = 2
theme_override_constants/separation = 10
alignment = 1

[node name="CancelButton" type="Button" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ButtonContainer" index="0"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 3
text = "Cancel"

[node name="BuildButton" type="Button" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ButtonContainer" index="1"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 3
text = "Build"
