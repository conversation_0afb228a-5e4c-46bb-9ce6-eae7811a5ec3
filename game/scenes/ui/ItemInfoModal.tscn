[gd_scene load_steps=3 format=3 uid="uid://uvwxy01358a"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://4gcm3khs83k6" path="res://scripts/ui/ItemInfoModal.gd" id="2_item_modal_script"]

[node name="ItemInfoModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_item_modal_script")

[node name="HBoxContainer" type="HBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="ItemIconTexture" type="TextureRect" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer" index="0"]
unique_name_in_owner = true
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainerInfo" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer" index="1"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ItemDescriptionLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/VBoxContainerInfo" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Item description goes here..."
autowrap_mode = 2

[node name="ItemStatsLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/HBoxContainer/VBoxContainerInfo" index="1"]
unique_name_in_owner = true
layout_mode = 2
text = "Stats: ..."
autowrap_mode = 2

[node name="ActionButtons" type="HBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="1"]
layout_mode = 2
alignment = 1

[node name="PickupButtonContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ActionButtons" index="0"]
layout_mode = 2

[node name="PickupButton" type="Button" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ActionButtons/PickupButtonContainer" index="0"]
unique_name_in_owner = true
custom_minimum_size = Vector2(160, 50)
layout_mode = 2
text = "Zvednout"

[node name="PickupButtonComment" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ActionButtons/PickupButtonContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
theme_override_colors/font_color = Color(0.999947, 0.541282, 0.47681, 1)
text = "Chyba"
