[gd_scene load_steps=3 format=3 uid="uid://dxnxqnvxnqvgj"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://5ep4754pube5" path="res://scripts/ui/DashboardModal.gd" id="2_dashboard_modal_script"]

[node name="DashboardModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_dashboard_modal_script")

[node name="TitleLabel" parent="Modal/MarginContainer/VBoxContainer" index="0"]
text = "Player Dashboard"

[node name="ScrollContainer" type="ScrollContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer" index="0"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="VBoxContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer" index="0"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 15

[node name="BuildingsSection" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="0"]
layout_mode = 2

[node name="Label" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/BuildingsSection" index="0"]
layout_mode = 2
text = "Buildings"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/BuildingsSection" index="1"]
layout_mode = 2

[node name="BuildingsContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/BuildingsSection" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="LoadingLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/BuildingsSection/BuildingsContainer" index="0"]
layout_mode = 2
text = "Loading buildings..."
horizontal_alignment = 1

[node name="RegionsSection" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="1"]
layout_mode = 2

[node name="Label" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/RegionsSection" index="0"]
layout_mode = 2
text = "Controlled Regions"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/RegionsSection" index="1"]
layout_mode = 2

[node name="RegionsContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/RegionsSection" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="LoadingLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/RegionsSection/RegionsContainer" index="0"]
layout_mode = 2
text = "Loading regions..."
horizontal_alignment = 1

[node name="RefreshButton" type="Button" parent="Modal/MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="2"]
unique_name_in_owner = true
layout_mode = 2
text = "Refresh Data"
