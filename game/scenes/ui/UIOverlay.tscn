[gd_scene load_steps=2 format=3 uid="uid://bqwerty68aa0"]

[ext_resource type="Script" uid="uid://dqoisq6hvuimc" path="res://scripts/ui/UIOverlay.gd" id="1_uioverlay"]

[node name="UIOverlay" type="CanvasLayer"]
script = ExtResource("1_uioverlay")

[node name="TopBar" type="PanelContainer" parent="."]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 80.0
grow_horizontal = 2

[node name="VBoxContainer" type="VBoxContainer" parent="TopBar"]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="TopBar/VBoxContainer"]
layout_mode = 2
theme_override_constants/margin_left = 12
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 12
theme_override_constants/margin_bottom = 8

[node name="HBoxContainer" type="HBoxContainer" parent="TopBar/VBoxContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="MenuButton" type="Button" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(60, 60)
layout_mode = 2
text = "☰"

[node name="AvatarTextureRect" type="TextureRect" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(60, 60)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="AvatarHttpRequest" type="HTTPRequest" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer/AvatarTextureRect"]
unique_name_in_owner = true

[node name="VBoxContainer" type="VBoxContainer" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer"]
layout_mode = 2

[node name="PlayerNameLabel" type="Label" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Player Name"

[node name="PlayerIDLabel" type="Label" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "ID: --------"

[node name="FollowPlayerButton" type="Button" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(70, 60)
layout_mode = 2
text = "GPS lock
ON"

[node name="BuildButton" type="Button" parent="TopBar/VBoxContainer/MarginContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(70, 60)
layout_mode = 2
text = "Build
New"

[node name="ResourceContainer" type="HBoxContainer" parent="TopBar/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="TopBar/VBoxContainer/ResourceContainer"]
layout_mode = 2
text = "Resources placeholder"

[node name="SlideMenu" type="PanelContainer" parent="."]
unique_name_in_owner = true
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -300.0
offset_top = 110.0
offset_bottom = -12.0
grow_vertical = 2
size_flags_horizontal = 0

[node name="MarginContainer" type="MarginContainer" parent="SlideMenu"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="SlideMenu/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="Label" type="Label" parent="SlideMenu/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Menu"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="SlideMenu/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="HeroButton" type="Button" parent="SlideMenu/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 60)
layout_mode = 2
text = "👤 Hero Details"

[node name="DashboardButton" type="Button" parent="SlideMenu/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 60)
layout_mode = 2
text = "📊 Player Dashboard"

[node name="HSeparator2" type="HSeparator" parent="SlideMenu/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="LogoutButton" type="Button" parent="SlideMenu/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 60)
layout_mode = 2
text = "🚪 Logout"
