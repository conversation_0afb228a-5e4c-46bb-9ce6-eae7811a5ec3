[gd_scene load_steps=2 format=3 uid="uid://cdefgh123456"]

[ext_resource type="Script" uid="uid://bmjrk2obt634s" path="res://scripts/ui/Modal.gd" id="1_modal_script"]

[node name="MarginModal" type="MarginContainer"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -195.0
offset_top = -386.0
offset_right = 195.0
offset_bottom = 386.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_top = 30
script = ExtResource("1_modal_script")

[node name="Modal" type="PanelContainer" parent="."]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="Modal"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="Modal/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TitleLabel" type="Label" parent="Modal/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Modal Title"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Modal/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="ContentContainer" type="VBoxContainer" parent="Modal/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="HSeparator2" type="HSeparator" parent="Modal/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="CloseButton" type="Button" parent="Modal/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(120, 50)
layout_mode = 2
size_flags_horizontal = 4
text = "Close"
