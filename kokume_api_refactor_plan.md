# Refined Plan for Aligning game/kokume_api.gd with Backend API

This plan outlines the necessary changes to `game/kokume_api.gd` to align it with the existing backend API structure, based on analysis of `web/src/common/api.service.ts` and relevant backend controllers.

## Phase 1: Align `game/kokume_api.gd` with Existing API

1.  **Base URL Handling:**
    *   Keep `API_url` configurable in Godot (e.g., `http://kokume.loc`).
    *   Define base path constants: `API_BASE_V1 = "/api/v1"` and `API_BASE_OAUTH = "/api"`.
    *   Modify the `_endpoint(path: String)` helper function to correctly prepend `API_url` + the appropriate base path (`API_BASE_V1` or `API_BASE_OAUTH`) depending on the endpoint.

2.  **OAuth Request:**
    *   Update the `authorize()` function to send the request data as `multipart/form-data`. **Note:** This is based on the web client's implementation (`api.service.ts`). The implementation might require adjusting how the request body is built in Godot.
    *   Use the `API_BASE_OAUTH` path for the `EP_TOKEN` endpoint.

3.  **Update Endpoint Constants (using V1 base unless noted):**
    *   `EP_TOKEN = "/oauth2/access_token"` (Uses `API_BASE_OAUTH`)
    *   `EP_PLAYER = "/player"`
    *   **Remove** `EP_PLAYER_STATUS`.
    *   `EP_OCCUPY_REGION = "/occupy-region"`
    *   **Remove** `EP_RESOURCE_GATHER`. Define path format: `EP_BUILDING_PICKUP_RESOURCES_FORMAT = "/building/%s/pick-up-resources"`.
    *   `EP_MAP_DATA = "/map/data"`
    *   **Remove** `EP_NEARBY_TERRITORIES`.
    *   **Remove** `EP_NEARBY_RESOURCES`.

4.  **Refactor API Functions:**
    *   `authorize()`: Use correct base URL (`API_BASE_OAUTH`), send as `multipart/form-data`.
    *   `player_info()`: Use `GET` to `EP_PLAYER`.
    *   `get_player_status()`: **Rename/Replace** with `update_player_status(position_data: Dictionary)` using `POST` to `EP_PLAYER`. The `position_data` dictionary should contain keys like `latLng` (as a nested dictionary with `lat`, `lng`), `accuracy`, `speed`.
    *   `capture_territory()`: **Rename** to `occupy_region()`. Use `POST` to `EP_OCCUPY_REGION`. Remove the `territory_id` parameter as the API call is implicit.
    *   `gather_resource()`: **Rename/Replace** with `pickup_building_resources(building_id: String)` using `GET` to the formatted `EP_BUILDING_PICKUP_RESOURCES_FORMAT` path. Remove the `amount` parameter.
    *   `get_nearby_territories()`: **Rename/Replace** with `get_map_data(bbox_string: String)` using `GET` to `EP_MAP_DATA` with the query parameter `?bbox={bbox_string}`. Add helper logic in Godot to convert latitude/longitude/radius to a bounding box string format (`minLon,minLat,maxLon,maxLat`).
    *   `get_nearby_resources()`: **Remove** this function entirely.

5.  **Update Callbacks & Signals:**
    *   Adjust callback functions (`_completed`) to match the renamed/removed API functions.
    *   Update or remove corresponding signals. For example:
        *   `player_status_updated` -> `player_heartbeat_sent` (or similar)
        *   `territory_captured` -> `region_occupied`
        *   `resources_gathered` -> `building_resources_picked_up`
        *   `nearby_territories_loaded` & `nearby_resources_loaded` -> `map_data_loaded`
    *   Ensure parsing logic in callbacks handles the expected response structures from the correct endpoints (e.g., parsing GeoJSON from `/map/data`).

## Phase 2: Suggest API Improvements (Optional - For Future Consideration)

*   Standardize on a single base URL (e.g., `/api/v1`) and a consistent `Content-Type` for OAuth.
*   Add optional `lat`, `lon`, `radius` query parameters to `GET /map/data` as an alternative to `bbox`.
*   If the game needs to *fetch* status distinct from `GET /player`, consider adding `GET /player/status`.
*   If direct gathering from map nodes (not buildings) is a required mechanic, add an endpoint like `POST /resource/{node_id}/gather`.
*   Clarify the `POST /occupy-region` mechanism. If specific regions can be targeted, consider `POST /region/{id}/occupy` or adding `region_id` to the body.