# IDE directories
.idea/
.vscode/

# Operating System files
.DS_Store
Thumbs.db

# Dependency directories
# Ideally, these should be in the respective sub-project .gitignore files
# backend/vendor/
# */node_modules/
node_modules/
vendor/

# Build artifacts & Cache
# Ideally, these should be in the respective sub-project .gitignore files
# game/.godot/
# tower-defense/.godot/
# web/dist/
.godot/
dist/
build/
temp/
tmp/

# Logs
*.log
log/
logs/

# Environment variables
*.env
dev.env

# Large data files
czech_republic-*.osm.pbf
*.osm

# Godot specific (can be project-specific)
*.import
*.translation
# export_presets.cfg

# certs
backend/resources/oauth/*.key

#cline
.cline*
/memory-bank

# RooCode
.roo*

# aider
.aider*

# repomix
repomix-output.txt