# Logging System Design (Godot - Kokume Project)

**Date:** 2025-03-30

**Status:** Approved

## 1. Overview

This document outlines the design for a logging system for the Godot application located in the `game/` directory. The system aims to be easy to use, configurable via Project Settings, output to the Godot console, and support filtering by source and log level. It also includes a global switch to disable all logging.

## 2. Core Component: `Logger.gd` Autoload Singleton

A new script, `Logger.gd`, will be created and registered as an Autoload singleton named `Logger`.

**File Location:** `game/scripts/autoload/Logger.gd`

**Key Features:**

*   **Log Levels:** Defines standard log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL).
*   **Configuration:** Loads settings from `ProjectSettings` on startup (`logging/enabled`, `logging/min_level`, `logging/default_source_enabled`, `logging/sources/*`).
*   **Global Switch:** A boolean flag (`_logging_enabled`) controls whether any logging occurs.
*   **Filtering:** Checks the global switch, log level against `min_level`, and source against `_source_settings` before printing.
*   **Output:** Prints formatted messages (Timestamp, Level, Source, Message) to the Godot Output console using `print` or `printerr`.
*   **API:** Provides simple public methods (`Logger.debug()`, `Logger.info()`, etc.).

**Implementation (`Logger.gd`):**

```gdscript
# Logger.gd
extends Node

enum LogLevel {
    DEBUG,    # Detailed information, typically of interest only when diagnosing problems.
    INFO,     # Confirmation that things are working as expected.
    WARNING,  # An indication that something unexpected happened, or indicative of some problem in the near future.
    ERROR,    # Due to a more serious problem, the software has not been able to perform some function.
    CRITICAL  # A serious error, indicating that the program itself may be unable to continue running.
}
const LOG_LEVEL_STRINGS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

var _logging_enabled: bool = true # Global switch
var _min_log_level: LogLevel = LogLevel.INFO # Default
var _source_settings: Dictionary = {} # Stores { "SourceName": is_enabled }
var _default_source_enabled: bool = true

func _ready():
    _load_config()

func _load_config():
    # Load global enabled flag
    _logging_enabled = ProjectSettings.get_setting("logging/enabled", true)

    # Load minimum level
    var level_str = ProjectSettings.get_setting("logging/min_level", "INFO").to_upper()
    _min_log_level = _string_to_log_level(level_str)

    # Load default source enabled state
    _default_source_enabled = ProjectSettings.get_setting("logging/default_source_enabled", true)

    # Load specific source settings
    _source_settings.clear()
    var source_keys = ProjectSettings.get_setting("logging/sources", {})
    for source_name in source_keys:
        var enabled = ProjectSettings.get_setting("logging/sources/" + source_name + "/enabled", _default_source_enabled)
        _source_settings[source_name.to_upper()] = enabled

    # Log initialization only if logging is enabled overall
    if _logging_enabled:
         print("Logger initialized. Enabled: %s, Min Level: %s, Default Source Enabled: %s, Sources: %s" % [_logging_enabled, LOG_LEVEL_STRINGS[_min_log_level], _default_source_enabled, _source_settings])
    else:
         print("Logger initialized. Logging globally DISABLED.")


func _string_to_log_level(level_str: String) -> LogLevel:
    match level_str:
        "DEBUG": return LogLevel.DEBUG
        "INFO": return LogLevel.INFO
        "WARNING": return LogLevel.WARNING
        "ERROR": return LogLevel.ERROR
        "CRITICAL": return LogLevel.CRITICAL
        _:
            printerr("Invalid log level '%s' in Project Settings. Defaulting to INFO." % level_str)
            return LogLevel.INFO

# Internal function to handle level/source checks and formatting.
func _log(level: LogLevel, source: String, message: String):
    # Check global switch first
    if not _logging_enabled:
        return

    if level < _min_log_level:
        return # Skip logging if below minimum level

    var source_upper = source.to_upper()
    var is_source_enabled = _source_settings.get(source_upper, _default_source_enabled)

    if not is_source_enabled:
        return # Skip logging if source is disabled

    var timestamp = Time.get_datetime_string_from_system(false, true) # YYYY-MM-DD HH:MM:SS
    var level_str = LOG_LEVEL_STRINGS[level]
    var output = "[%s] [%s] [%s] %s" % [timestamp, level_str, source, message]

    if level >= LogLevel.ERROR:
        printerr(output) # Use printerr for ERROR and CRITICAL
    else:
        print(output)

# Public Logging Methods
func debug(source: String, message: String):
    _log(LogLevel.DEBUG, source, message)

func info(source: String, message: String):
    _log(LogLevel.INFO, source, message)

func warning(source: String, message: String):
    _log(LogLevel.WARNING, source, message)

func error(source: String, message: String):
    _log(LogLevel.ERROR, source, message)

func critical(source: String, message: String):
    _log(LogLevel.CRITICAL, source, message)
```

## 3. Project Settings Configuration (`project.godot`)

Add the following section to `project.godot`:

```gdscript
; Add these lines to your project.godot file

[logging]

enabled=true ; Set to false to disable all logging (e.g., for release)
min_level="INFO" ; Possible values: DEBUG, INFO, WARNING, ERROR, CRITICAL
default_source_enabled=true ; Log messages from sources not explicitly listed?

[logging.sources]

; Add specific sources you want to control
; If a source is not listed here, 'default_source_enabled' applies.
API/enabled=true
GPS/enabled=true
UI/enabled=true
Map/enabled=true
GameState/enabled=true
Player/enabled=true
; Add more sources as needed...
; Example: Combat/enabled=false
```

## 4. Example Usage

```gdscript
# In any script:
Logger.info("Map", "Map viewer initialized.")
Logger.debug("API", "Sending request to endpoint X.")
Logger.error("Player", "Failed to load player data: %s" % error_details)
```

## 5. Architecture Diagram

```mermaid
graph TD
    subgraph Game Scripts
        A[kokume_api.gd]
        B[map_viewer.gd]
        C[login.gd]
        D[...]
    end

    subgraph Autoloads
        Logger(Logger.gd)
    end

    subgraph Godot Engine
        Settings[ProjectSettings (project.godot)]
        Console[Output Console]
    end

    A -- Calls --> Logger
    B -- Calls --> Logger
    C -- Calls --> Logger
    D -- Calls --> Logger

    Logger -- Reads config from --> Settings
    Logger -- Prints to --> Console

    classDef autoload fill:#f9f,stroke:#333,stroke-width:2px;
    class Logger autoload;