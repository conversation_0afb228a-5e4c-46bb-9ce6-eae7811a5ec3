# GPS Simulation Feature Plan (Revised)

**Objective:** Implement a development-only feature to simulate GPS coordinates within the Godot game (`game/`), allowing testing of location-dependent features without relying on the actual device GPS. The simulation logic should be fully contained within `GpsService.gd`.

## Plan Details

### 1. State Management & Logic (Internal to `game/scripts/autoload/GpsService.gd`)

*   **Add Internal State Variables:**
    *   `var simulate_gps: bool = false`
    *   `var simulated_latitude: float = 0.0`
    *   `var simulated_longitude: float = 0.0`
    *   `var simulated_accuracy: float = 5.0` (Default accuracy for simulated points)
*   **Add Public Control Functions:**
    *   `func set_gps_simulation(enabled: bool) -> void`:
        *   Updates the internal `simulate_gps` flag.
        *   If enabling simulation (`enabled = true`):
            *   Stops the real GPS listener (`gps_provider.StopListening()`).
            *   Sets `is_listening = true` (conceptually, indicating the service is active).
            *   Updates status (`_update_status("ACTIVE")`).
            *   Immediately emits `location_updated` with the current simulated values.
        *   If disabling simulation (`enabled = false`):
            *   Calls the original `start_listening()` logic to attempt activating the real GPS provider.
    *   `func set_simulated_location(lat: float, lon: float) -> void`:
        *   Updates internal `simulated_latitude` and `simulated_longitude`.
        *   If `simulate_gps` is true and `is_listening` is true, emits `location_updated` with the new simulated values.
    *   `func is_gps_simulated() -> bool`: Returns the internal `simulate_gps` flag.
    *   `func get_simulated_location() -> Vector2`: Returns the internal simulated location.
*   **Modify `_on_location_updated(data: Dictionary)`:**
    *   Add check at the start: `if simulate_gps: return`. Ignores real GPS updates when simulating.
*   **Modify `start_listening()`:**
    *   Focuses on starting the *real* GPS provider. Simulation activation is handled by `set_gps_simulation(true)`.
*   **Modify `stop_listening()`:**
    *   Focuses on stopping the *real* GPS provider. Simulation deactivation is handled by `set_gps_simulation(false)`.

### 2. Debug UI (New Scene/Script)

*   **Files:**
    *   `game/scenes/ui/DebugPanel.tscn`
    *   `game/scripts/ui/DebugPanel.gd`
*   **Scene Structure (`DebugPanel.tscn`):**
    *   Root: `PanelContainer` or similar.
    *   Layout: `VBoxContainer`.
    *   Controls:
        *   `CheckBox` ("Simulate GPS").
        *   `LineEdit` (Latitude input, initially disabled).
        *   `LineEdit` (Longitude input, initially disabled).
        *   `Button` ("Apply Simulated Location", initially disabled).
*   **Script Logic (`DebugPanel.gd`):**
    *   Connect `CheckBox` `toggled` signal:
        *   Calls `GpsService.set_gps_simulation(is_checked)`.
        *   Enables/disables LineEdits and Button.
        *   Populates LineEdits with `GpsService.get_simulated_location()` when enabling.
    *   Connect `Button` `pressed` signal:
        *   Reads lat/lon from LineEdits (validate input).
        *   Calls `GpsService.set_simulated_location(lat, lon)`.

### 3. Debug UI Integration

*   Modify `Boot.gd` or `MainGame.gd` to instance `DebugPanel.tscn` and add it to the scene tree *only* if `OS.is_debug_build()` is true.
*   Consider adding a toggle key (e.g., F1) to show/hide the debug panel.

## Diagrammatic Overview

```mermaid
graph TD
    subgraph Debug UI (DebugPanel.tscn/gd)
        ToggleSim[CheckBox: Simulate GPS]
        InputLat[LineEdit: Latitude]
        InputLon[LineEdit: Longitude]
        ApplyBtn[Button: Apply]
    end

    subgraph Autoloads
        GPS[GpsService.gd]
    end

    subgraph Core Logic
        MV[MapViewer.gd]
        Other[Other Game Logic]
    end

    subgraph External
        RealGPS[PraxisMapperGPSPlugin]
    end

    ToggleSim -- toggled --> GPS(set_gps_simulation)
    ApplyBtn -- pressed --> GPS(set_simulated_location)

    GPS -- Emits simulated/real --> MV(location_updated)
    GPS -- Emits simulated/real --> Other(location_updated)

    GPS -- Internally decides --> RealGPS

    classDef autoload fill:#f9f,stroke:#333,stroke-width:2px;
    classDef debugui fill:#ff9,stroke:#333,stroke-width:2px;
    class GPS autoload;
    class ToggleSim,InputLat,InputLon,ApplyBtn debugui;