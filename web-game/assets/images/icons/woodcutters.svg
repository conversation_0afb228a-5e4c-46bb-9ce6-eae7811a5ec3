<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Created using Krita: https://krita.org -->
<svg xmlns="http://www.w3.org/2000/svg" 
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:krita="http://krita.org/namespaces/svg/krita"
    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
    width="720pt"
    height="720pt"
    viewBox="0 0 720 720">
<defs/>
<ellipse id="shape0" transform="translate(43.0200262352213, 44.1000261515363)" rx="316.979973764779" ry="315.899973848464" cx="316.979973764779" cy="315.899973848464" fill="none" stroke="#000000" stroke-width="28.8" stroke-linecap="square" stroke-linejoin="bevel"/><path id="shape1" transform="matrix(0.7325895310731 0.680670683195711 -0.680670683195711 0.7325895310731 385.467887925124 170.617184579382)" fill="none" stroke="#000000" stroke-width="14.4" stroke-linecap="square" stroke-linejoin="bevel" d="M28.8 115.2L28.8 316.8C28.8 336.48 38.4 346.08 57.6 345.6C76.8 345.12 86.4 335.52 86.4 316.8L86.4 115.2C137.482 100.146 155.552 127.423 200.88 158.4C221.082 97.8196 221.322 45.0196 201.6 2.84217e-14C171.36 30.72 132.96 40.32 86.4 28.8L86.4 0L28.8 0L28.8 28.8L0 28.8L0 115.2Z" sodipodi:nodetypes="cczccccccccccc"/><path id="shape2" transform="translate(325.439997413639, 274.319997819904)" fill="none" stroke="#000000" stroke-width="14.4" stroke-linecap="square" stroke-linejoin="miter" stroke-miterlimit="2" d="M0 0L44.64 41.04" sodipodi:nodetypes="cc"/><path id="shape3" transform="translate(388.079996915822, 209.519998334887)" fill="none" stroke="#000000" stroke-width="14.4" stroke-linecap="square" stroke-linejoin="miter" stroke-miterlimit="2" d="M0 0L42.48 41.04" sodipodi:nodetypes="cc"/><ellipse id="shape4" transform="translate(371.52, 248.985000001073)" rx="7.01999846792757" ry="7.13249895340573" cx="7.01999846792757" cy="7.13249895340573" fill="none" stroke="#000000" stroke-width="14.4" stroke-linecap="square" stroke-linejoin="bevel"/>
</svg>
