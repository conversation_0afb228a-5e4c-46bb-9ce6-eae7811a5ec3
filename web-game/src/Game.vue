<script setup lang="ts">

import {onMounted, watch} from "vue";
import router from "./router";
import * as Sentry from "@sentry/vue";

import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faSignOut } from '@fortawesome/free-solid-svg-icons';

import logo from '@/assets/images/logo.webp';

import InventoryModal from "./components/inventory/InventoryModal.vue";
import HelpModal from "./components/HelpModal.vue";

import SignService from "./common/sign.service";

import { useSignStore } from "./store/sign";
import { usePlayerStore } from "./store/player";
import { useMapStore } from "./store/map";
import { useInventoryStore } from "./store/inventory";
import { useHelpStore } from "./store/help";

const signStore = useSignStore();
const playerStore = usePlayerStore();
const mapStore = useMapStore();
const inventoryStore = useInventoryStore();
const helpStore = useHelpStore();

watch(() => signStore.authenticated, (authenticated) => {
	if (authenticated === false) {
		router.push({name: 'sign'});
	}
});

watch(() => playerStore.player, (player) => {
	if (player) {
		Sentry.setUser(player);
	}
});

onMounted(() => {
	if (signStore.authenticated) {
		playerStore.fetchPlayer();
	} else {
		router.push({name: 'sign'});
	}

	if (playerStore.player) {
		Sentry.setUser(playerStore.player);
	}
});

</script>

<template>
	<div class="container flex-grow-1 p-0">
		<header>
			<nav class="navbar navbar-expand-lg bg-light">
				<div class="container">
					<div class="navbar-brand">
						<img :src="logo" alt="Kokume" width="32" height="32" class="d-inline-block">
					</div>
					<ul class="m-0 me-auto" v-if="router.currentRoute.value.name === 'map'">
						<li class="list-inline-item d-lg-none d-inline">
							<button
								@click="mapStore.state = mapStore.state !== 'build' ? 'build' : 'none'"
								class="btn btn-sm"
								:class="mapStore.state === 'build' ? 'btn-success' : 'btn-outline-secondary'"
							>
								🏗️ Stavět
							</button>
						</li>
						<li v-if="playerStore.player?.hero" class="list-inline-item d-lg-none d-inline">
							<button
								@click="inventoryStore.open = !inventoryStore.open"
								class="btn btn-sm"
								:class="inventoryStore.open ? 'btn-success' : 'btn-outline-secondary'"
							>
								💼 Inventář
							</button>
						</li>
					</ul>
					<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainMenu" aria-controls="mainMenu" aria-expanded="false" aria-label="Toggle navigation">
						<span class="navbar-toggler-icon"></span>
					</button>
					<div class="collapse navbar-collapse" id="mainMenu">
						<ul class="navbar-nav me-auto mb-2 mb-lg-0 d-flex" v-if="signStore.authenticated">
							<li class="nav-item">
								<RouterLink to="/" class="nav-link">Domů</RouterLink>
							</li>
							<li class="nav-item">
								<RouterLink to="/dashboard" class="nav-link">Přehled</RouterLink>
							</li>
							<li class="nav-item">
								<RouterLink to="/map" class="nav-link">Mapa</RouterLink>
							</li>
							<li class="nav-item" v-if="playerStore.player?.hero">
								<RouterLink to="/hero" class="nav-link">Hrdina</RouterLink>
							</li>
							<li class="nav-item">
								<button class="nav-link btn btn-link" @click="helpStore.toggleModal()">
									Nápověda
								</button>
							</li>
						</ul>
						<ul class="navbar-nav navbar-right" v-if="signStore.authenticated">
							<li class="nav-item">
								<span class="nav-link text-black"><strong>{{ playerStore.player?.nickname }}</strong></span>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link" @click.prevent="SignService.signOut()"><font-awesome-icon :icon="faSignOut"></font-awesome-icon> Odhlásit se</a>
							</li>
						</ul>
					</div>
				</div>
			</nav>
		</header>
		<main class="container flex-grow-1 p-0 p-3">
			<div class="container">
				<div class="alert alert-primary alert-dismissible fade show d-flex align-items-center" role="alert" v-if="playerStore.player && !playerStore.player.hero">
					V hlavní budově si můžeš najmout hrdinu. Díky němu můžeš sbírat předměty a bojovat s nepřáteli.
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			</div>
			<RouterView />
			<InventoryModal v-if="playerStore.player?.hero" />
			<HelpModal />
		</main>
	</div>
</template>

<style scoped>
	.navbar {
		z-index: 2000;
	}
</style>
