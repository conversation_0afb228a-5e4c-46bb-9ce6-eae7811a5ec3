import L, {LatLng} from "leaflet";

export interface ApiError {
    error: string;
    message: string;
}

export interface Player {
    id: number;
    nickname: string;
    range: number;
    hero?: Hero;
}

export enum heroSex {
    MALE = 'male',
    FEMALE = 'female',
    NONE = 'none',
}

export interface Hero {
    name: string;
    description: string;
    sex: heroSex;

    level: number;
    experiences: number;
    experiences_next_level: number;

    health: number;
    max_health: number;
    defense: number;
    strength: number;
    agility: number;

    max_items: number;
}

export interface PlayerPosition {
    latLng: LatLng,
    accuracy: number,
    speed: number,
}

export interface BuildingTypeProduction {
    type: ResourceType;
    levels: {1: number, 2: number, 3: number, 4: number, 5: number};
}

export interface UnitDefinition {
    id: number;
    name: string;
    description: string;
    size: number;
    baseHealth: number;
    goldCost: number;
    foodCost: number;
}

export interface UnitProduction {
    unit_definition: UnitDefinition;
    production_time: number,
    cost: Array<Resource>,
}

export interface BuildingType {
    slug: string;
    name: string;
    description: string;
    icon: string;
    cost: Array<Resource>;
    demolish_gain: Array<Resource>;
    build_time: number;
    demolish_time: number;
    range: number;
    max_level: number;
    unique_in_world: boolean;
    unique_in_region: boolean;
    productions?: Array<BuildingTypeProduction>;
    unit_productions?: Array<UnitProduction>;
}

export enum BuildingState {
    BUILDING = 'building',
    BUILT = 'built',
    DEMOLISHING = 'demolishing',
    DEMOLISHED = 'demolished',
    UPGRADING = 'upgrading',
}

export interface Building {
    uuid: string;
    type: BuildingType;
    name: string;
    region: Region;
    position: L.LatLng;
    owner: Player;
    state: BuildingState;
    level: number;
    upgrade_cost: Array<Resource>;
    state_valid_to: any;
    stock: Array<Resource>;
}

export interface ResourceType {
    slug: string;
    name: string;
}

export interface ResourceRegionType {
    slug: string;
    name: string;
}

export interface Resource {
    type: ResourceType;
    amount: number;
}

export interface Region {
    id: number;
    name: string;

    level: string;
    level_name: string;

    owner_id: number;
    owner_name: string;

    resource_amounts: Array<Resource>;
}

export interface ResourceRegion {
    id: number;
    name: string;
    type: ResourceRegionType;
    resource_amounts: Array<Resource>
}

export interface MapFeature {
    properties: {
        type: string,
        id?: number,
        uuid?: string,
        name: string,
		description?: string,
        resource?: string,
    };
    geometry: any;
}

export interface Item {
    uuid: string;
    type: string;
    category: string;
    name: string;
    description: string;
    durability: number;
}

export interface ItemPlaced {
    item: Item;
    position: L.LatLng;
}

export interface CombatLog {
	attacker: string;
	defender: string;
	damage: number;
	defenderHealth: number;
}

export interface CombatResult {
	logs: Array<CombatLog>;
	is_win: boolean;
	winner: string;
	loser: string;
} 

export interface StealResult {
	building: Building;
	combat_result: CombatResult;
	loot: Array<Resource>;
}