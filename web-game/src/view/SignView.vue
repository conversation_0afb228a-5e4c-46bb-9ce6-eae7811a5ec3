<script setup lang="ts">

import { onMounted, ref, watch } from "vue";
import router from "../router";

import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { faUser, faLock, faGamepad, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons'

import SignService from "../common/sign.service";

import { useSignStore } from "../store/sign";
import {ApiError} from "@/modules/game/type";


const signStore = useSignStore();

const isRegister = ref(false);
const error = ref<string|null>(null);
const showPassword = ref(false);
const showGamePassword = ref(false);

const email = ref("");
const nickname = ref("");
const password = ref("");
const gamePassword = ref("");

function doLogin() {
	if (email.value === "" || password.value === "") {
		error.value = "Vyplň všechny pole";
	} else {
		error.value = "";
		SignService.signIn(email.value, password.value);
	}
}

function doRegister() {
	if (email.value === "" || nickname.value === "" || password.value === "") {
		error.value = "Vyplň všechny pole";
		return;
	}

	if (password.value.length < 6) {
		error.value = "Heslo musí mít alespoň 6 znaků";
		return;
	}

	if (gamePassword.value.length < 6) {
		error.value = "Heslo od admina musí mít alespoň 6 znaků";
		return;
	}

	if (nickname.value.length < 3) {
		error.value = "Přezdívka musí mít alespoň 3 znaky";
		return;
	}

	// validate email
	if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
		error.value = "Email není ve správném formátu";
		return;
	}

	error.value = null;
	SignService.signUp(email.value, nickname.value, password.value, gamePassword.value);
}

watch(() => signStore.error, (apiError: ApiError) => {
	if (apiError) {
		error.value = apiError.code !== 500 ? apiError.message : "Něco se pokazilo";
	} else {
		error.value = null;
	}
});

watch(() => signStore.authenticated, (newVal) => {
	if (newVal) {
		router.push({name: "home"});
	}
});

onMounted(() => {
	// redirect if already authenticated
	if (signStore.authenticated) {
		router.push({name: "home"});
	}
});

</script>

<template>
	<div class="container">
		<div class="mt-5 row">
			<div class="col-lg-4 col-md-6 col-sm-8 mx-auto">
				<div class="card p-2" v-bind:class="{ error: error !== '' }">
					<h1 v-if="isRegister">Registrace</h1>
					<h1 v-else>Přihlášení</h1>

					<form class="form-group">
						<div class="input-group mb-2">
							<span class="input-group-text">@</span>
							<input v-model="email" type="email" class="form-control" name="email" placeholder="Email" required>
						</div>
						<div v-if="isRegister" class="input-group mb-2">
							<span class="input-group-text"><font-awesome-icon :icon="faUser"></font-awesome-icon></span>
							<input v-model="nickname" type="text" minlength="3" class="form-control" placeholder="Přezdívka" required>
						</div>
						<div class="input-group mb-2">
							<span class="input-group-text"><font-awesome-icon :icon="faLock"></font-awesome-icon></span>
							<input v-model="password" :type="showPassword ? 'text' : 'password'" minlength="6" class="form-control" name="password" placeholder="Heslo" required>
							<span class="input-group-text" @click="showPassword = !showPassword" :title="showPassword ? 'Skrýt heslo' : 'Zobrazit heslo'">
								<font-awesome-icon :icon="showPassword ? faEyeSlash : faEye"></font-awesome-icon>
							</span>
						</div>
						<div v-if="isRegister" class="input-group mb-2">
							<span class="input-group-text"><font-awesome-icon :icon="faGamepad"></font-awesome-icon></span>
							<input v-model="gamePassword" :type="showGamePassword ? 'text' : 'password'" minlength="6" class="form-control" name="gamePassword" placeholder="Heslo od admina" required>
							<span class="input-group-text" @click="showGamePassword = !showGamePassword" :title="showGamePassword ? 'Skrýt heslo' : 'Zobrazit heslo'">
								<font-awesome-icon :icon="showGamePassword ? faEyeSlash : faEye"></font-awesome-icon>
							</span>
						</div>

						<button v-if="isRegister" type="submit" class="btn btn-primary" @click.prevent="doRegister">Vytvořit účet</button>
						<button v-else type="submit" class="btn btn-primary" @click.prevent="doLogin">Přihlásit se</button>

						<p v-if="error" class="p-2 text-danger">{{ error }}</p>

						<p v-if="isRegister" class="mt-4 p-2 alert alert-warning">
							Přístup do hry je zatím <strong>jen na pozvání</strong>, chceš hrát? Napiš na <a href="mailto:<EMAIL>"><EMAIL></a> z e-mailu, se kterým se budeš registrovat.
						</p>
						<p v-if="isRegister" class="p-2">Už máš svůj učet? <a href="#" @click="isRegister = !isRegister; error = ''">Tady se přihlaš</a></p>
						<p v-else class="p-2">Ještě tu nemáš účet? <a href="#" @click="isRegister = !isRegister; error = ''">Vytvoř si ho tady</a></p>
					</form>
				</div>
			</div>
		</div>

	</div>
</template>

<style scoped>

</style>