<script setup lang="ts">

import {onMounted} from "vue";
import Loading from "../components/Loading.vue";

import {useBuildingStore} from "../store/building";
import {useRegionStore} from "../store/region";
import {BuildingState} from "../type";
import Timer from "../components/Timer.vue";

const buildingStore = useBuildingStore();
const regionStore = useRegionStore();

onMounted(() => {
	if (buildingStore.buildings === undefined) {
		buildingStore.fetchBuildings();
	}

	if (regionStore.playerRegions === undefined) {
		regionStore.fetchPlayerRegions();
	}
});

</script>

<template>
	<h1>Přehled</h1>
	<div class="row">
		<div class="col-md-6">
			<div class="card mb-3">
				<div class="card-header">
					<h3 class="card-title">Budovy</h3>
				</div>
				<div class="card-body">
					<div v-if="buildingStore.buildings" class="accordion" id="my_region_buildings">
						<div v-for="({region, buildings}, regionId) in buildingStore.regionsBuildings" class="accordion-item">
							<h2 class="accordion-header" :id="'region_heading-' + regionId">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" :data-bs-target="'#region_buildings-' + regionId" aria-expanded="false" :aria-controls="'region_buildings-' + regionId">
									<strong>{{ region.name }}</strong>&nbsp;<span class="text-black-50">( {{ buildings.length }} {{ buildings.length === 1 ? 'budova' : (buildings.length < 5 ? 'budovy' : 'budov') }} )</span>
								</button>
							</h2>
							<div :id="'region_buildings-' + regionId" class="accordion-collapse collapse" :aria-labelledby="'region_buildings-' + regionId" data-bs-parent="#my_region_buildings">
								<div class="accordion-body">
									<div class="accordion" id="my_buildings">
										<div v-for="building in buildings" class="accordion-item">
											<h2 class="accordion-header" :id="'building_heading-' + building.uuid">
												<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" :data-bs-target="'#building_detail-' + building.uuid" aria-expanded="false" :aria-controls="'building_detail-' + building.uuid">
													<strong>{{ building.name }}</strong>
													&nbsp;
													<span class="text-black-50">
														( {{ building.type.name }}
														<span v-if="building.state === BuildingState.BUILDING">&nbsp;- ve výstavbě</span>
														<span v-else-if="building.state === BuildingState.DEMOLISHING">&nbsp;- bourá se</span>
														<span v-else-if="building.state === BuildingState.DEMOLISHED">&nbsp;- zbouráno</span>)
													</span>
												</button>
											</h2>
											<div :id="'building_detail-' + building.uuid" class="accordion-collapse collapse" :aria-labelledby="'building_heading-' + building.uuid" data-bs-parent="#my_buildings">
												<div class="accordion-body">
													<div>
														<strong v-if="building.state === BuildingState.BUILDING" class="text-uppercase">ve výstavbě</strong>
														<strong v-else-if="building.state === BuildingState.DEMOLISHING">bourá se</strong>
														<strong v-else-if="building.state === BuildingState.DEMOLISHED">zbouráno</strong>
														<span v-if="building.state_valid_to"> - <timer :end-date="new Date(building.state_valid_to)" /></span>
													</div>
													<div v-if="building.state === BuildingState.BUILT">
														<strong>Sklad</strong>
														<ul v-if="building.stock?.length > 0" class="list-unstyled">
															<li v-for="stock in building.stock">
																{{ stock.type.name }}: {{ stock.amount }}
															</li>
														</ul>
														<span v-else> prázdný</span>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<loading v-else></loading>
				</div>
			</div>
		</div>
		<div class="col-md-6">
			<div class="card mb-3">
				<div class="card-header">
					<h3 class="card-title">Ovládnuté regiony</h3>
				</div>
				<div class="card-body">
					<ul v-if="regionStore.playerRegions" class="list-unstyled">
						<li v-for="region in regionStore.playerRegions">
							<strong>{{ region.name }}</strong>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>