<script setup lang="ts">
import 'leaflet/dist/leaflet.css';

import L, {LatLng, Marker, Point} from 'leaflet';
import {LCircle, LGeoJson, LMap, LPopup, LTileLayer} from '@vue-leaflet/vue-leaflet';
import {onMounted, onUnmounted, ref, watch} from "vue";
import {debounce} from 'lodash';
import BuildModal from "../components/BuildModal.vue";

import {storeToRefs} from "pinia";

import {useMapStore} from "../store/map";
import {usePlayerStore} from "../store/player";
import {useRegionStore} from "../store/region";
import {useBuildingStore} from "../store/building";
import {useInventoryStore} from "../store/inventory";

import baseIcon from '@/assets/images/icons/base.svg';
import subBaseIcon from '@/assets/images/icons/sub_base.svg';
import farmIcon from '@/assets/images/icons/farm.svg';
import fishingHutIcon from '@/assets/images/icons/fishing_hut.svg';
import mineIcon from '@/assets/images/icons/mine.svg';
import woodcuttersIcon from '@/assets/images/icons/woodcutters.svg';
import quarryIcon from '@/assets/images/icons/stones.svg';
import infoIcon from '@/assets/images/icons/info.svg';
import itemIcon from '@/assets/images/icons/treasure.svg';

import GeoLocationService from "../common/geolocation.service";
import MapInfoModal from "../components/MapInfoModal.vue";

const map = ref<typeof LMap | undefined>(undefined);
const gjLayer = ref<typeof LGeoJson | undefined>(undefined);

const url = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
const attribution = 'Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors';
const mapStore = useMapStore();

const regionStore = useRegionStore();
const buildingStore = useBuildingStore();
const playerStore = usePlayerStore();
const inventoryStore = useInventoryStore();

const { zoom, bounds, state } = storeToRefs(mapStore);

const lockPosition = ref(false);

const fakePosActive = ref(false);
const fakePos = ref<LatLng | undefined>(undefined);
const fakePosInterval = ref<NodeJS.Timeout | undefined>(undefined);

function mapClicked(event: any) {
	if (state.value === 'none') {
		showInfoModal(event.layer);
	}
	else if (state.value === 'fake_pos') {
		fakePos.value = event.latlng;
		state.value = 'none';
	}
	else if (state.value === 'build_preview') {
		state.value = 'build';
	}
	else if (state.value === 'build_position') {
		state.value = 'build';
	}
}

function showInfoModal(layer: any) {
	mapStore.selectedFeature = layer.feature;
}

function centerToPos() {
	if (playerStore.position && map.value) {
		map.value.leafletObject.setView(playerStore.position.latLng);
	}
}

function reloadPage() {
	location.reload();
}

function setGeoJSONStyle(feature: any) {
	if (feature.geometry.type === 'Polygon') {
		const styles = {
			color: '#000000',
			fillColor: '#cecece',
			weight: 1.0,
		};

		const owner = feature.properties.owner;
		const type = feature.properties.type;

		if (type === 'resource') {
			styles.color = '#c124fa';
			styles.weight = 2.0;

			const resource = feature.properties.resource;
			switch (resource) {
				case 'forest':
					styles.fillColor = '#00ff00';
					break;
				case 'meadow':
					styles.fillColor = '#00ff00';
					break;
				case 'field':
					styles.fillColor = '#ffff00';
					break;
				case 'quarry':
				case 'mine':
					styles.fillColor = '#646469';
					break;
				case 'water':
				case 'reservoir':
				case 'pond':
					styles.fillColor = '#0000ff';
					break;
			}
		} else if (owner) {
			styles.fillColor = owner.id === playerStore.player?.id ? '#20b720' : '#b72020';
		}

		return styles;
	}
}

function pointToLayer(feature: any, latlng: any) {
	const type = feature.properties.type;

	if (type === 'building') {
		const buildingType = feature.properties.building_type;

		if (buildingType === 'base') {
			return new Marker(latlng, {
				icon: L.icon({
					iconUrl: baseIcon,
					iconSize: new Point(40, 40),
					iconAnchor: new Point(10, 10),
				}),
			});
		}

		const icon = {
			'sub_base': subBaseIcon,
			'farm': farmIcon,
			'fishing_hut': fishingHutIcon,
			'iron_mine': mineIcon,
			'clay_mine': mineIcon,
			'sawmill': woodcuttersIcon,
			'quarry': quarryIcon,
		}[buildingType];

		return new Marker(latlng, {
			icon: L.icon({
				iconUrl: icon,
				iconSize: [30, 30],
				iconAnchor: [10, 10],
			}),
		});
	}

	if (type === 'item') {
		return new Marker(latlng, {
			icon: L.icon({
				iconUrl: itemIcon,
				iconSize: [25, 25],
				iconAnchor: [10, 10],
			}),
		})
	}

	return new Marker(latlng);
}

function occupyRegion() {
	regionStore.occupyCurrentRegion()
}

function toggleFakePos(pos: LatLng | undefined = undefined) {
	if (pos) {
		fakePos.value = pos;
		lockPosition.value = true;

		const playerPos = {latLng: pos, accuracy: 10, speed: 0};
		playerStore.position = playerPos;
		playerStore.update(playerPos);
	} else {
		state.value = fakePosActive.value ? 'none' : 'fake_pos';
	}

	fakePosActive.value = !fakePosActive.value;

	if (fakePosActive) {
		GeoLocationService.clearWatch();
	} else {
		GeoLocationService.watchGeolocation();
	}
}

function updateMapData() {
	updateMapDataDebounced();
}

const updateMapDataDebounced = debounce(() => {
	mapStore.fetchData();
}, 1000);

watch([zoom, bounds], () => {
	updateMapData();
});

watch(() => fakePosActive.value, (newValue) => {
	if (newValue) {
		if (fakePosInterval.value) {
			return;
		}

		fakePosInterval.value = setInterval(() => {
			if (!fakePos.value) {
				return;
			}

			playerStore.update({
				latLng: fakePos.value,
				accuracy: Math.round(Math.random() * 20),
				speed: Math.round(Math.random() * 5),
			});
		}, 5000);
	} else {
		if (fakePosInterval.value) {
			clearInterval(fakePosInterval.value);
			fakePosInterval.value = undefined;
		}
	}
});

// center to player position after map is loaded
watch(map, (newValue) => {
	if (newValue) {
		setTimeout(centerToPos, 500);
	}
});

watch(fakePos, (newValue) => {
	if (newValue?.lat && newValue?.lng) {
		document.cookie = `fake_pos=${newValue.lat},${newValue.lng}`;
	} else {
		document.cookie = 'fake_pos=; expires=Thu, 01 Jan 1970 00:00:00 UTC';
	}
});

onMounted(() => {
	if (buildingStore.buildings === undefined) {
		buildingStore.fetchBuildings();
	}

	GeoLocationService.watchGeolocation();

	setInterval(() => {
		if (lockPosition.value) {
			centerToPos();
		}
	}, 500);

	let lastPosition: LatLng | null = null;
	setInterval(() => {
		if (lockPosition.value && playerStore.position?.latLng && lastPosition !== playerStore.position.latLng) {
			mapStore.fetchData();
			lastPosition = playerStore.position.latLng;
		}
	}, 2000);

	// load fake position from cookie
	const fakePosCookie = document.cookie.split('; ').find(row => row.startsWith('fake_pos='));
	if (fakePosCookie) {
		const [lat, lng] = fakePosCookie.split('=')[1].split(',');
		if (parseFloat(lat) && parseFloat(lng)) {
			toggleFakePos(new LatLng(parseFloat(lat), parseFloat(lng)));
		}
	}
});

onUnmounted(() => {
	GeoLocationService.clearWatch();
	mapStore.geoJson = undefined;
});

</script>

<template>
	<div class="map-container">
		<div class="navbar map-navbar">
			<ul class="list-unstyled list-inline">
				<li class="list-inline-item">
					<button @click="lockPosition = !lockPosition"
							class="btn btn-sm"
							:class="'btn' + (lockPosition ? '-success' : '-secondary')"
							title="Uzamknout polohu"
							aria-label="Uzamknout polohu">
						🧭
					</button>
				</li>
				<li class="list-inline-item">
					<button @click="toggleFakePos()"
							class="btn btn-sm"
							:class="'btn' + (fakePosActive ? '-success' : '-secondary')"
							title="Fejkovat polohu">
						👇🏼
						Fake
					</button>
				</li>
				<li class="list-inline-item d-none d-lg-inline">
					<button
						@click="state = state !== 'build' ? 'build' : 'none'"
						class="btn btn-sm"
						:class="'btn' + (state === 'build' ? '-success' : '-secondary')"
					>
						🏗️ Stavět
					</button>
				</li>
				<li v-if="playerStore.player?.hero" class="list-inline-item d-none d-lg-inline">
					<button
						@click="inventoryStore.open = !inventoryStore.open"
						class="btn btn-sm"
						:class="inventoryStore.open ? 'btn-success' : 'btn-secondary'"
					>
						💼 Inventář
					</button>
				</li>
				<li v-if="playerStore.current_region" class="list-inline-item">
					<button
						class="btn btn-sm btn-secondary"
						v-if="playerStore.current_region.owner_id !== playerStore.player.id"
						:class="{disabled: !playerStore.canOccupyCurrentRegion}"
						@click.prevent="occupyRegion()"
					>
						{{ playerStore.current_region.owner_id ? '⚔️ Vybojovat' : '👑 Ovládnout' }} -
						<strong>{{ playerStore.current_region.name }}</strong>
					</button>
				</li>
				<li class="badge bg-secondary">state: {{ state }}</li>
			</ul>

		</div>

		<div v-if="playerStore.resources" class="resources-container">
			<ul class="list-unstyled list-inline">
				<li class="list-inline-item" v-for="resource in playerStore.resources" :title="resource.type.name">
					<span class="badge bg-opacity-75 bg-secondary m-1">
						<span v-if="resource.type.slug === 'food'">🍎</span>
						<span v-if="resource.type.slug === 'wood'">🪵</span>
						<span v-if="resource.type.slug === 'clay'">💩</span>
						<span v-if="resource.type.slug === 'stone'">🪨</span>
						<span v-if="resource.type.slug === 'iron'">⚙️</span>
						{{ resource.amount }}
					</span>
				</li>
			</ul>
		</div>

		<div v-if="!playerStore.position" class="alert alert-danger">
			Nepodařilo se získat polohu, zkontroluj si nastavení prohlížeče.
			<button @click="reloadPage()" class="btn btn-sm btn-primary"> Znovu načíst stránku </button>
		</div>

		<l-map v-if="playerStore.position"
		       ref="map"
		       v-model:zoom="zoom"
		       v-model:bounds="bounds"
		       :min-zoom=1
		       :noBlockingAnimations="true"
		       class="map-element">
			<l-tile-layer :url="url" layer-type="base" :attribution="attribution" :keep-buffer="8"></l-tile-layer>
			<l-geo-json ref="gjLayer" :geojson="mapStore.geoJson" :options-style="setGeoJSONStyle" :options="{pointToLayer}" @click="mapClicked"></l-geo-json>
			<l-circle v-if="playerStore.position" :lat-lng="playerStore.position.latLng" :radius="1" :fill-opacity="1" :stroke-opacity="1"></l-circle>
			<l-circle v-if="playerStore.position" :lat-lng="playerStore.position.latLng" :radius="50" :fill-opacity="0.1" :stroke-opacity="0.3"></l-circle>
		</l-map>

		<BuildModal v-if="playerStore.position && map" v-bind:open="state === 'build'" v-bind:map="map" />

		<MapInfoModal />
	</div>
</template>

<style scoped>
	.map-container {
		touch-action: none;
	  position: absolute;
	  top: 55px;
	  left: 0;
	  width: 100%;
	  height: 100%;
	  display: flex;
	  flex-direction: column;
	}

	.map-element {
	  height: calc(100vh - 5rem);
	  width: 100%;
	}

	.map-navbar {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		z-index: 900;
		padding: 0.5rem 0.5rem 0.5rem 80px;
		display: flex;
		justify-content: flex-start;
	}

	.resources-container {
		position: absolute;
		top: 0;
		right: 20px;
		z-index: 901; /* Vyšší než .map-navbar */
		padding: 0.5rem;
		border-radius: 5px;
	}

	@media (max-width: 990px) {
		.resources-container {
			top: 20%;
			right: 0;
			transform: translateY(-50%);
			padding: 0.3rem;
		}
		
		.resources-container .list-inline-item {
			display: block;
			margin: 5px 0;
		}
	}
</style>
