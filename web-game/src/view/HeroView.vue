<script setup lang="ts">

import {computed, onMounted, ref} from "vue";
import {useInventoryStore} from "../store/inventory";
import {usePlayerStore} from "../store/player";

import heroImgMale from '@/assets/images/hero_m.webp';
import heroImgFemale from '@/assets/images/hero_f.webp';
import heroImgNone from '@/assets/images/hero_n.webp';
import {heroSex} from "../type";
import PlayerInventory from "../components/inventory/PlayerInventory.vue";
import PlayerEquipment from "../components/inventory/PlayerEquipment.vue";

const playerStore = usePlayerStore();
const inventoryStore = useInventoryStore();

const hero = computed(() => {
	return playerStore.player?.hero;
});

const statsProgress = computed(() => {
	return {
		health: (hero.value?.health / (hero.value?.max_health / 100)) || 0,
		agility: hero.value?.agility || 0,
		strength: hero.value?.strength || 0,
		defense: hero.value?.defense || 0,
		experiences: hero.value?.experiences || 0,
	};
});

const heroImg = computed(() => {
	if (!playerStore.player?.hero) {
		return undefined;
	}

	if (playerStore.player.hero.sex === heroSex.MALE) {
		return heroImgMale;
	} else if (playerStore.player.hero.sex === heroSex.FEMALE) {
		return heroImgFemale;
	}

	return heroImgNone;
});

onMounted(() => {
	inventoryStore.fetchItems();
});

</script>

<template>
	<h1>Hrdina</h1>
	<div v-if="hero" class="hero-page container mt-4">
		<div class="row">
			<div class="col-12 col-sm-4 text-center mb-4">
				<div class="hero-header">
					<h1 class="hero-name">{{ hero.name }}</h1>
					<img :src="heroImg" alt="Hero Portrait" class="hero-portrait">
					<p><strong>Lvl</strong> {{ hero.level }}</p>
					<p v-if="hero.description" class="hero-description">
						{{ hero.description }}
					</p>
				</div>
			</div>

			<div class="col-12 col-sm-8 mb-4">
				<div class="hero-stats m-3 m-lg-0">
					<h3>Statistiky</h3>
					<div class="stat">
						<span>Zdraví</span>
						<div class="progress">
							<div class="progress-bar bg-danger" role="progressbar" :style="{width: statsProgress.health+'%'}">{{ hero.health }}</div>
						</div>
					</div>
					<div class="stat">
						<span>Obratnost</span>
						<div class="progress">
							<div class="progress-bar bg-success" role="progressbar" :style="{width: statsProgress.agility+'%'}">{{ hero.agility }}</div>
						</div>
					</div>
					<div class="stat">
						<span>Síla</span>
						<div class="progress">
							<div class="progress-bar bg-primary" role="progressbar" :style="{width: statsProgress.strength+'%'}">{{ hero.strength }}</div>
						</div>
					</div>
					<div class="stat">
						<span>Obrana</span>
						<div class="progress">
							<div class="progress-bar bg-primary" role="progressbar" :style="{width: statsProgress.defense+'%'}">{{ hero.defense }}</div>
						</div>
					</div>
					<div class="stat">
						<span>Další úrověň ({{ hero.experiences_next_level }})</span>
						<div class="progress">
							<div class="progress-bar bg-primary" role="progressbar" :style="{width: statsProgress.experiences+'%'}">{{ hero.experiences }}</div>
						</div>
					</div>
				</div>
			</div>

		</div>

		<div class="row">
			<div class="col-xl-4 col-12 mb-4">
				<div class="hero-equipment">
					<h3>Vybavení</h3>
					<PlayerEquipment />
				</div>
			</div>

			<!-- Inventář -->
			<div class="col-xl-8 col-12">
				<div class="hero-inventory">
					<h3>Inventář</h3>
					<PlayerInventory />
				</div>
			</div>

		</div>
	</div>
</template>

<style scoped>

.hero-header {
	text-align: center;
}

.hero-portrait {
	width: 150px;
	height: 150px;
	border-radius: 50%;
	object-fit: cover;
	border: 4px solid #ccc;
	margin-bottom: 15px;
}

.hero-name {
	font-size: 2rem;
	font-weight: bold;
}

.hero-description {
	font-style: italic;
	color: #666;
}

.hero-stats .stat {
	margin-bottom: 15px;
}

.hero-stats .progress {
	height: 20px;
	background-color: #e9ecef;
}

.hero-stats .progress-bar {
	font-size: 0.9rem;
	line-height: 20px;
}

</style>