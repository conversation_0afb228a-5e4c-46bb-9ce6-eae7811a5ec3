import { defineStore } from "pinia";
import { HeroItemService } from "../../common/api.service";
import type {ApiError, Item} from "../../type";

import {usePlayerStore} from "../player";

interface State {
    open: boolean;

    items: { [key: string]: Item };
    equipment: { [key: string]: Item };
    itemsLoading: boolean;
    itemsError: ApiError|null;
    size: number;

    current: Item | null;
    currentLoading: boolean;
    currentError: ApiError|null;

    pickUpLoading: boolean;
    pickUpError: ApiError|null;
}

export const useInventoryStore = defineStore('inventory', {
    state: () : State => ({
        open: false,

        items: {},
        equipment: {},
        itemsLoading: false,
        itemsError: null,
        size: 25,

        current: null,
        currentLoading: false,
        currentError: null,

        pickUpLoading: false,
        pickUpError: null,
    }),
    getters: {},
    actions: {
        async get(id: string) {
            this.currentLoading = true;
            this.currentError = null;

            HeroItemService.getItem(id)
                .then((response) => {
                    this.current = response.data;
                })
                .catch((error) => {
                    this.currentError = error;
                });

            this.currentLoading = false;
        },

        async fetchItems() {
            this.itemsLoading = true;
            this.itemsError = null;

            return HeroItemService.getItems()
                .then((response) => {
                    this.items = response.data.items;
                    this.equipment = response.data.equipment;
                })
                .catch((error) => {
                    this.itemsError = error;
                })
                .finally(() => {
                    this.itemsLoading = false;
                });
        },

        async pickUp(id: string) {
            this.pickUpLoading = true;
            this.pickUpError = null;

            return HeroItemService.pickUp(id)
                .then(() => {
                    this.fetchItems();
                })
                .catch((error) => {
                    this.pickUpError = error;
                })
                .finally(() => {
                    this.pickUpLoading = false;
                });
        },

        async drop(id: string) {
            this.itemsLoading = true;
            this.itemsError = null;

            return HeroItemService.drop(id)
                .then(() => {
                    this.fetchItems();
                });
        },

        async move(from: string, to: string) {
            return HeroItemService.move(from, to)
                .then((response) => {
                    this.items = response.data.items;
                    this.equipment = response.data.equipment;

                    usePlayerStore().fetchPlayer();
                });
        },
    },
})