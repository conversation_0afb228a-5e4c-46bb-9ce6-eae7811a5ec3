<script setup lang="ts">
import {useBuildingStore} from "../store/building";
import {usePlayerStore} from "../store/player";
import {computed, ref} from "vue";
import Timer from "./Timer.vue";
import Loading from "./Loading.vue";
import {Building, BuildingState, Hero, UnitDefinition} from "../type";
import {useHeroStore} from "../store/hero";
import {useInventoryStore} from '../store/inventory';
import {useMapStore} from '../store/map';

const buildingStore = useBuildingStore();
const playerStore = usePlayerStore();
const heroStore = useHeroStore();
const inventoryStore = useInventoryStore();
const mapStore = useMapStore();

const props = defineProps({
	building: {
		type: Object,
		required: true
	},
	buildingLoading: {
		type: Boolean,
		required: true
	},
	isInRange: {
		type: Boolean,
		required: true
	},
});

const hireHeroData = ref<{ name: string }>({name: ''});

const isBuildingOwner = computed(() => {
	return playerStore.player && props.building && props.building.owner?.id === playerStore.player?.id;
});

const canUpgradeBuilding = computed(() => {
	return buildingStore.canUpgradeCurrentBuilding;
});

const canPickUpResources = computed(() => {
	return props.building?.stock?.length > 0 && props.isInRange;
});

function pickUpResources() {
	if (!props.building) {
		return;
	}

	buildingStore.pickUpResources(props.building as Building)
		.then((updatedBuilding: Building | null) => {
			if (updatedBuilding) {
				buildingStore.currentBuilding = updatedBuilding;
			}
			playerStore.fetchPlayer();
		});
}

function upgradeBuilding() {
	if (!props.isInRange) {
		return;
	}

	const currentBuilding = buildingStore.currentBuilding;
	if (!currentBuilding || !canUpgradeBuilding.value) {
		return;
	}

	if (currentBuilding.state !== BuildingState.BUILT) {
		return;
	}

	if (currentBuilding.level >= currentBuilding.type.max_level) {
		return;
	}

	buildingStore.upgradeBuilding(currentBuilding)
		.then((upgradedBuilding) => {
			if (upgradedBuilding) {
				buildingStore.currentBuilding = upgradedBuilding as Building;
			}
			playerStore.fetchPlayer();
		});
}

function demolishBuilding() {
	if (!props.isInRange) {
		return;
	}

	const currentBuilding = buildingStore.currentBuilding;
	if (!currentBuilding || currentBuilding.state !== BuildingState.BUILT) {
		return;
	}

	buildingStore.demolishBuilding(currentBuilding)
		.then((demolishedBuilding: Building) => {
			buildingStore.currentBuilding = demolishedBuilding;
		});
}

function hireHero() {
	if (!props.building || props.building.type.slug !== 'base' || !props.isInRange) {
		return;
	}

	heroStore.hire(hireHeroData.value)
		.then((hero: Hero) => {
			if (playerStore.player) {
				playerStore.player.hero = hero;
			}
		});
}

function stealBuilding() {
	if (!props.isInRange) {
		return;
	}

	const currentBuilding = buildingStore.currentBuilding;
	if (!currentBuilding || currentBuilding.state !== BuildingState.BUILT) {
		return;
	}

	buildingStore.stealBuilding(currentBuilding)
		.then((stolenBuilding) => {
			playerStore.fetchPlayer();
		});
}

function startProduction(unitDefinition: UnitDefinition) {

}

function cancelProduction(queueItem: any) {

}

</script>

<template>
	<div v-if="props.building">
		<ul class="list-unstyled">
			<li>
				typ: {{ props.building.type.name }}
			</li>

			<li v-if="props.building.owner.id !== playerStore.player?.id">
				vlastník: {{ props.building.owner.nickname }}
			</li>

			<li v-if="props.building.state">
				stav: {{ props.building.state }}
				<span v-if="props.building.state_valid_to">
		          (<timer :end-date="new Date(props.building.state_valid_to)"/>)
		        </span>
			</li>

			<li v-if="props.building.state === BuildingState.BUILT && isBuildingOwner && canPickUpResources">
				sklad:
				<ul v-if="props.building?.stock?.length">
					<li v-for="stock in props.building.stock">
						{{ stock.type.name }}: {{ stock.amount }}
					</li>
				</ul>
				<span v-else> prázdný</span>
				<div v-if="canPickUpResources">
					<button class="btn btn-sm btn-primary"
					        @click.prevent="pickUpResources()"
					>
						Vyzvednout suroviny
						<loading v-if="buildingStore.pickUpLoading"/>
					</button>
				</div>
			</li>

			<li v-if="props.building.state === BuildingState.DEMOLISHED">
				suroviny v troskách:
				<ul v-if="props.building?.stock?.length">
					<li v-for="stock in props.building.stock">
						{{ stock.type.name }}: {{ stock.amount }}
					</li>
				</ul>
				<div v-if="canPickUpResources">
					<button class="btn btn-sm btn-primary"
					        @click.prevent="pickUpResources()"
					>
						Vyzvednout trosky
						<loading v-if="buildingStore.pickUpLoading"/>
					</button>
				</div>
			</li>

			<li v-if="props.building.state === BuildingState.BUILT && !playerStore.player?.hero && props.building.type.slug === 'base' && props.isInRange">
				<button class="btn btn-sm btn-secondary" data-bs-toggle="collapse" data-bs-target="#hire_hero">
					Najmout hrdinu
				</button>
				<div id="hire_hero" class="collapse fade">
					<label for="hero_name" class="form-label">Jméno hrdiny</label>
					<input type="text" class="form-control" id="hero_name" v-model="hireHeroData.name">
					<button class="btn btn-sm btn-success mt-2" @click.prevent="hireHero()">
						Najmout
						<loading v-if="heroStore.hireLoading"/>
					</button>
				</div>
			</li>

			<li v-if="props.building.state === BuildingState.BUILT && isBuildingOwner && props.building.level < props.building.type.max_level">
				<button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#upgrade_building">
					Upgrade na {{ props.building.level + 1 }} úroveň
				</button>
				<div id="upgrade_building" class="collapse fade">
					<strong>Náklady:</strong>
					<ul class="list-unstyled">
						<li v-for="cost in props.building.upgrade_cost">
							{{ cost.type.name }}: {{ cost.amount }}
							<strong v-if="playerStore.findResource(cost.type.slug).amount < cost.amount" class="text-danger">
								(chybí {{ cost.amount - playerStore.findResource(cost.type.slug).amount }} !!)
							</strong>
						</li>
					</ul>
					<button
						v-if="canUpgradeBuilding && props.isInRange"
						class="btn btn-sm btn-success"
						@click.prevent="upgradeBuilding()"
					>
						Upgrade na úroveň {{ props.building.level + 1 }}
						<loading v-if="buildingStore.upgradeLoading"/>
					</button>
				</div>
				<div class="mt-1 alert alert-danger" v-if="buildingStore.upgradeError">
					<span v-if="buildingStore.upgradeError.error === 'too-far'">Budova je příliš daleko</span>
					<span v-else>{{ buildingStore.upgradeError.message }}</span>
				</div>
			</li>

			<li v-if="props.building.state === BuildingState.BUILT && isBuildingOwner && props.isInRange">
				<button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#demolish_building">
					Zbourat
				</button>
				<div id="demolish_building" class="collapse fade">
					<strong>Zisk surovin:</strong>
					<ul class="list-unstyled">
						<li v-for="cost in props.building.type.demolish_gain">
							{{ cost.type.name }}: {{ cost.amount }}
						</li>
					</ul>
					<button
						class="btn btn-sm btn-danger"
						@click.prevent="demolishBuilding()"
					>
						Zbourat
						<loading v-if="buildingStore.demolishLoading"/>
					</button>
				</div>
			</li>

			<li v-if="props.building.state === BuildingState.BUILT && isBuildingOwner === false && props.isInRange && playerStore.player?.hero && buildingStore.stealResult?.building.uuid !== props.building.uuid">
				<button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#steal_building">
					Vykrást
				</button>
				<div id="steal_building" class="collapse fade">
					<button
						class="btn btn-sm btn-danger"
						@click.prevent="stealBuilding()"
					>
						Vykrást
						<loading v-if="buildingStore.stealLoading"/>
					</button>
				</div>
			</li>

			<li v-if="isBuildingOwner && props.building.state === BuildingState.BUILT && props.building.type.unit_productions">
				<button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#available_units">
					Dostupné jednotky
				</button>
				<div id="available_units" class="collapse fade">
					<ul class="list-unstyled">
						<li v-for="{unit_definition, cost} in props.building.type.unit_productions">
							<strong>{{ unit_definition.name }}</strong>
							<p class="m-0">{{ unit_definition.description }}</p>

							<strong>Náklady:</strong>
							<ul class="list-unstyled">
								<li v-for="resource in cost">
									{{ resource.type.name }}: {{ resource.amount }}
									<strong v-if="!playerStore.hasEnoughResource(resource)" class="text-danger">
										(chybí {{ resource.amount - playerStore.findResource(resource.type.slug).amount }} !!)
									</strong>
								</li>
							</ul>
							<button class="btn btn-sm btn-success"
							        :class="{'disabled': !playerStore.hasEnoughResources(cost)}"
							        @click.prevent="startProduction(unit_definition)">
								Najmout
							</button>
						</li>
					</ul>
				</div>
			</li>

			<li v-if="isBuildingOwner && props.building.state === BuildingState.BUILT && props.building.queue">
				<h6>Fronta</h6>
				<ul class="list-unstyled">
					<li v-for="queueItem in props.building.production_queue" :key="queueItem.id">
						{{ queueItem.unit_definition.name }} - {{ queueItem.status }}
						<button class="btn btn-sm btn-danger" @click.prevent="cancelProduction(queueItem)">Cancel
						</button>
					</li>
				</ul>
			</li>
		</ul>
	</div>
	<div v-else-if="props.buildingLoading">
		<loading/>
	</div>
	<div v-else>
		<p>Chyba při načítání budovy.</p>
	</div>

	<div v-if="props.building && buildingStore.stealResult">
		<div>
			<h6>Výsledek boje</h6>
			<p>
				Vítěz: {{ buildingStore.stealResult.combat_result.winner }} <br>
				Poražený: {{ buildingStore.stealResult.combat_result.loser }}
			</p>
		</div>
		<div v-if="buildingStore.stealResult.combat_result.is_win">
			<h6>Lup</h6>
			<ul v-if="buildingStore.stealResult.loot.length > 0" class="list-unstyled">
				<li v-for="loot in buildingStore.stealResult.loot">
					{{ loot.type.name }}: {{ loot.amount }}
				</li>
			</ul>
			<p v-else>Nic tu není :(</p>
		</div>
	</div>
</template>