<script setup lang="ts">

import unknownItemImg from '@/assets/images/items/unknown.webp';
import { Popover } from 'bootstrap';
import {onUpdated, ref, watch} from "vue";
import {Item} from "../../type";

const emit = defineEmits<{
	(e: 'dragstart', evt: DragEvent): void
	(e: 'drop', evt: DragEvent): void
	(e: 'dropout', uuid: string): void
	(e: 'move', from: string, to: string): void
}>()

const props = defineProps({
	slotKey: {
		type: String,
		required: true,
	},
	slotTitle: {
		type: String,
		required: false,
	},
	item: {
		type: Object as Item,
		required: false,
	},
});

const itemNode = ref<HTMLDivElement | null>(null);

function startDrag(evt: DragEvent) {
	evt.dataTransfer.dropEffect = 'move'
	evt.dataTransfer.effectAllowed = 'move'
	evt.dataTransfer.setData('slotKey', props.slotKey)

	emit('dragstart', evt);
}

function onDrop(evt: DragEvent) {
	const slotKey= evt.dataTransfer.getData('slotKey')
	if (slotKey) {
		emit('move', slotKey, props.slotKey);
	}

	emit('drop', evt);
}

// Initialize item popovers
onUpdated(() => {
	const popover = itemNode.value.querySelector('[data-bs-toggle="popover"]');
	if (popover) {
		Popover.getOrCreateInstance(popover, {
			trigger: 'click',
			placement: 'top',
			html: true,
			content: document.getElementById(popover.getAttribute('data-bs-target')).innerHTML,
		});
	}
});

document.addEventListener('click', (event) => {
	if (!itemNode.value) {
		return;
	}

	const popover = itemNode.value.querySelector('[data-bs-toggle="popover"]');
	if (popover) {
		const instance = Popover.getInstance(popover);
		if (instance && instance._config.trigger === 'click') {
			instance.hide();
		}
	}
});

</script>
<template>
	<div ref="itemNode"
	     class="inventory-slot"
	     :data-slot="slotKey"
	     @dragover.prevent
	     @dragenter.prevent
	     @drop="onDrop($event)"
	>
		<span v-if="!item && slotTitle">{{ slotTitle }}</span>
		<div v-if="item"
		     :key="item.uuid"
		     draggable="true"
		     @dragstart="startDrag($event)"
		     class="inventory-item"
		     :data-durability="item.durability"
		     data-bs-toggle="popover"
		     :data-bs-title="item.name"
		     :data-bs-content="item.description"
		     :data-bs-target="'item_popover_' + item.uuid"
		>
			<button class="btn btn-sm discard-btn"
			        title="Zahodit"
			        @click="emit('dropout', item.uuid)"
			>✖</button>
			<img :src="unknownItemImg" alt="Item Image" class="item-image">
			<div class="item-name">{{ item.name }}</div>
			<div class="item-durability-bar">
				<div class="durability">{{ item.durability }}%</div>
			</div>
			<div :id="'item_popover_' + item.uuid" class="d-none">
				<p class="text-black-50">{{ item.category }}</p>
				<p>{{ item.description }}</p>
				<h5>Statistiky</h5>
				<ul class="list-unstyled">
					<li v-if="item.durability">Životnost: {{ item.durability }}</li>
				</ul>
			</div>
		</div>
	</div>
</template>

<style scoped>

.inventory-slot {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	border: 1px solid #ccc;
	border-radius: 5px;
	text-align: center;
	aspect-ratio: 1 / 1; /* Slot bude vždy čtvercový */
	//width: 100%; /* Přizpůsobí šířku rodičovskému elementu */
	//max-width: 80px; /* Maximální velikost slotu */
	padding: 5px;
	background-color: #f9f9f9; /* Volitelně, aby byl slot viditelný */
}

.inventory-item {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	align-items: center;
	border-radius: 5px;
	overflow: hidden;
	z-index: 2;
	transition: transform 0.2s, box-shadow 0.2s;
	background-color: transparent;
	width: 100%; /* Vyplní celý slot */
	height: 100%; /* Udrží proporce slotu */
}

.inventory-item:hover {
	transform: scale(1.05);
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.inventory-item[draggable="true"] {
	cursor: grab;
}

.item-name {
	font-weight: bold;
}

.item-image {
	position: absolute;
	inset: 0; /* Natáhne obrázek na celý slot */
	width: 100%;
	height: 100%;
	object-fit: cover; /* Udrží poměr stran, ale vyplní slot */
	opacity: 0.3;
	z-index: 1;
	pointer-events: none; /* Zabrání interakci s obrázkem */
}

.item-durability-bar {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 12px;
	background-color: #e9ecef;
	border-radius: 0 0 4px 4px;
	opacity: 0.8;
	overflow: hidden;
}

.durability {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0 0 4px 4px;
	font-size: 0.75em;
	color: #fff;
	font-weight: bold;
	transition: width 0.3s ease, background-color 0.3s ease;
	width: calc(var(--durability, 100) * 1%);
	background-color: hsl(calc(var(--durability, 100) * 1.2), 80%, 50%);
}

.inventory-item[data-durability="100"] .durability {
	--durability: 100;
}

.inventory-item[data-durability="75"] .durability {
	--durability: 75;
}

.inventory-item[data-durability="50"] .durability {
	--durability: 50;
}

.inventory-item[data-durability="25"] .durability {
	--durability: 25;
}

.inventory-item[data-durability="10"] .durability {
	--durability: 10;
}

.discard-btn {
	position: absolute;
	top: 3px;
	right: 3px;
	font-size: 0.7em;
	padding: 1px 2px;
	border-radius: 50%;
	opacity: 0.6;
	transition: opacity 0.2s ease, transform 0.2s ease;
}

.discard-btn:hover {
	opacity: 1;
	transform: scale(1.2);
}

</style>