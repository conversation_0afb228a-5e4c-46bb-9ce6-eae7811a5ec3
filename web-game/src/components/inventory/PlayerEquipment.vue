<script setup lang="ts">
import {useInventoryStore} from "../../store/inventory";
import {onMounted, ref} from "vue";
import {useMapStore} from "../../store/map";
import Slot from "../../components/inventory/Slot.vue";
import Inventory from "../../components/inventory/Inventory.vue";

const inventoryStore = useInventoryStore();
const mapStore = useMapStore();

const emit = defineEmits<{
	(e: 'dragstart', evt: DragEvent): void
	(e: 'drop', evt: DragEvent): void
	(e: 'dropout', uuid: string): void
	(e: 'move', from: number, to: number): void
}>();

const slots = {
	weapon: {
		title: 'Zbraň',
		key: 'weapon',
		allowed_item_types: ['weapon'],
	},
	ring1: {
		title: 'Prsten 1',
		key: 'ring1',
		allowed_item_types: ['ring'],
	},
	ring2: {
		title: 'Prsten 2',
		key: 'ring2',
		allowed_item_types: ['ring'],
	},
	head: {
		title: 'Hlava',
		key: 'head',
		allowed_item_types: ['hat'],
	},
	necklace: {
		title: 'N<PERSON>hrdeln<PERSON>',
		key: 'necklace',
		allowed_item_types: ['necklace'],
	},
	chest: {
		title: 'Hrudník',
		key: 'chest',
		allowed_item_types: ['armor'],
	},
	legs: {
		title: 'Nohy',
		key: 'legs',
		allowed_item_types: ['trousers'],
	},
	feet: {
		title: 'Chodidla',
		key: 'feet',
		allowed_item_types: ['boots'],
	},
};

const grid = ref<HTMLDivElement>();

function dropout(uuid: string) {
	inventoryStore.drop(uuid).then(() => {
		mapStore.fetchData();
	});
}

function move(from: string, to: string) {
	if (from === to) {
		return;
	}

	const item = inventoryStore.items[from] || inventoryStore.equipment[from];
	if (!item || !slots[to].allowed_item_types.includes(item.type)) {
		const slotEl = grid.value?.querySelector(`[data-slot="${to}"]`);
		if (slotEl) {
			slotEl.style.border = '3px solid red';
			setTimeout(() => slotEl.style.border = '', 50);
		}
		return;
	}

	inventoryStore.move(from, to);
}

onMounted(() => {
	inventoryStore.fetchItems();
});

</script>

<template>
	<div ref="grid" class="equipment-grid">
		<Slot
			v-for="slot in slots"
			:slot-key="slot.key"
			:slot-title="slot.title"
			:item="inventoryStore.equipment[slot.key]"
			@dropout="dropout"
			@move="move"
		/>
	</div>
</template>

<style scoped>

.equipment-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10px;
	//width: 100%;
	//max-width: 300px;
}

@media (max-width: 1199px) {
	.equipment-grid {
		grid-template-columns: repeat(5, 1fr);
	}
}

</style>