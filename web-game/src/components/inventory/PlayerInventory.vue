<script setup lang="ts">

import {useInventoryStore} from "../../store/inventory";
import Inventory from "./Inventory.vue";
import {onMounted} from "vue";
import Loading from "../Loading.vue";
import {useMapStore} from "../../store/map";

const inventoryStore = useInventoryStore();
const mapStore = useMapStore();

function dropout(uuid: string) {
	inventoryStore.drop(uuid).then(() => {
		mapStore.fetchData();
	});
}

function move(from: string, to: string) {
	if (from === to) {
		return;
	}

	inventoryStore.move(from, to);
}

onMounted(() => {
	inventoryStore.fetchItems();
});

</script>

<template>
	<Loading v-if="inventoryStore.itemsLoading" />
	<Inventory
		v-else
		:items="inventoryStore.items"
		:max-items="25"
		slot-key-prefix="i"
		@dropout="dropout"
		@move="move"/>
</template>