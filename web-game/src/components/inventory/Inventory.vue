<script setup lang="ts">

import Slot from "./Slot.vue";

const emit = defineEmits<{
	(e: 'dragstart', evt: DragEvent): void
	(e: 'drop', evt: DragEvent): void
	(e: 'dropout', uuid: string): void
	(e: 'move', from: string, to: string): void
}>()

const props = defineProps({
	items: {
		type: Object,
		required: true,
	},
	maxItems: {
		type: Number,
		required: true,
	},
	slotKeyPrefix: {
		type: String,
		required: true,
	},
});

function moveItem(from: string, to: string) {
	props.items[to] = props.items[from];
	delete props.items[from];

	emit('move', from, to);
}

</script>
<template>
	<div class="inventory-grid">
		<Slot v-for="(slot, slotIndex) in maxItems"
		      :key="slotKeyPrefix+slotIndex"
		      :slot-key="slotKeyPrefix+slotIndex"
		      :item="items[slotKeyPrefix+slotIndex]"
		      @move="moveItem"
		      @dropout="(uuid: string) => emit('dropout', uuid)"
		      @dragstart="(event: DragEvent) => emit('dragstart', event)"
		      @drop="(event: DragEvent) => emit('drop', event)"
		/>
	</div>
</template>

<style scoped>

.inventory-grid {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 10px;
	width: 100%;
	//max-width: 500px;
}

//@media (max-width: 600px) {
//	.inventory-grid {
//		grid-template-columns: repeat(5, minmax(0, 1fr));
//		gap: 8px;
//	}
//}
</style>