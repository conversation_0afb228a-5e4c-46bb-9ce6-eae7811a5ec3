<script setup lang="ts">
import Modal from "../Modal.vue";
import {useInventoryStore} from "../../store/inventory";
import {onMounted} from "vue";
import {useMapStore} from "../../store/map";
import PlayerInventory from "./PlayerInventory.vue";

const inventoryStore = useInventoryStore();
const mapStore = useMapStore();

function dropout(uuid: string) {
	console.log('dropout event', uuid);
	// inventoryStore.drop(uuid).then(() => {
	// 	mapStore.fetchData();
	// });
}

function move(from: number, to: number) {
	console.log('move event', from, to);
	// inventoryStore.move(from, to).then(() => {
	// 	mapStore.fetchData();
	// });
}

onMounted(() => {
	inventoryStore.fetchItems();
});

</script>

<template>
	<Modal id="inventory" title="Inventář" v-bind:show="inventoryStore.open" @hide="inventoryStore.open = false">
		<template v-slot:body>
			<PlayerInventory />
		</template>
	</Modal>
</template>