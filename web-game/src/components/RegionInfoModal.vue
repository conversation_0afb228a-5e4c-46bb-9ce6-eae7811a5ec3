<script setup lang="ts">
import { useRegionStore } from "../store/region";
import { computed } from "vue";

const regionStore = useRegionStore();

const props = defineProps({
  region: {
    type: Object,
    required: true
  },
  regionLoading: {
    type: Boolean,
    required: true
  }
});
</script>

<template>
  <div v-if="props.region">
    <p v-if="props.region.owner_id">Tady kraluje: {{ props.region.owner_name }} #{{ props.region.owner_id }}</p>
    <p v-else>Volné <PERSON>zem<PERSON></p>

    <div v-if="props.region.resource_amounts">
      <h6>Zdroje na území</h6>
      <ul class="list-unstyled">
        <li v-for="production in props.region.resource_amounts">
          {{ production.type.name }}: {{ production.amount }}
        </li>
      </ul>
    </div>
  </div>
  <div v-else-if="props.regionLoading">
    <p>Načítání regionu...</p>
  </div>
  <div v-else>
    <p>Chyba při načítání regionu.</p>
  </div>
</template>