<script setup lang="ts">
import Modal from "./Modal.vue";
import RegionInfoModal from "./RegionInfoModal.vue";
import ResourceInfoModal from "./ResourceInfoModal.vue";
import BuildingInfoModal from "./BuildingInfoModal.vue";
import ItemInfoModal from "./ItemInfoModal.vue";

import {computed, ref, watch} from "vue";

import {useMapStore} from "../store/map";
import {useRegionStore} from "../store/region";
import {useResourceStore} from "../store/resource";
import {useBuildingStore} from "../store/building";
import {usePlayerStore} from "../store/player";
import {LatLng} from "leaflet";
import Loading from "./Loading.vue";

const mapStore = useMapStore();
const regionStore = useRegionStore();
const resourceStore = useResourceStore();
const buildingStore = useBuildingStore();
const playerStore = usePlayerStore();

const show = ref<boolean>(false);
const dataType = ref<string|null>(null);
const data = ref<any|null>(null);

const title = computed(() => {
  if (!mapStore.selectedFeature) {
    return '';
  }

  let title = mapStore.selectedFeature.properties?.name || '';

  if (dataType.value === 'region') {
    title = 'Region: ' + title;
  }
  else if (dataType.value === 'resource') {
    if (!title && data.value?.properties) {
      title = data.value.properties.resource || '';
    }
    title = 'Zdroj: ' + title;
  }
  else if (dataType.value === 'building') {
    title = 'Budova: ' + title;
  }
  else if (dataType.value === 'item') {
    title = 'Předmět: ' + title;
  }

  return title;
});

const position = computed(() => {
    if (mapStore.selectedFeature && mapStore.selectedFeature.geometry.type === 'Point') {
        return new LatLng(
            mapStore.selectedFeature.geometry.coordinates[1],
            mapStore.selectedFeature.geometry.coordinates[0]
        );
    }
    return null;
});

const isInRange = computed(() => {
    if (!position.value || !playerStore.position || !playerStore.player) {
        return false;
    }

    return position.value.distanceTo(playerStore.position.latLng) <= playerStore.player.range;
});

const canPickUpItem = computed(() => {
  return !!playerStore.player?.hero;
});


const clear = () => {
  show.value = false;
  data.value = null;
  mapStore.selectedFeature = null;
}

watch(() => mapStore.selectedFeature, (newVal: any) => { // Type 'any' for simplicity
  if (newVal) {
    dataType.value = newVal.properties.type;
    data.value = newVal;

    if (dataType.value === 'region' && newVal.properties.id) {
      regionStore.fetchRegion(newVal.properties.id);
    }
    else if (dataType.value === 'resource' && newVal.properties.id) {
      resourceStore.fetchData(newVal.properties.id);
    }
    else if (dataType.value === 'building' && newVal.properties.uuid) {
      buildingStore.changeCurrentBuilding(newVal.properties.uuid);
    }

    show.value = true;
  }
});

</script>

<template>
  <Modal id="map_info" v-bind:show="show" v-on:hidden="clear">
    <template v-slot:header>
      <h1 class="modal-title fs-5" v-bind:id="'map_info-label'">
        {{ title }}
        <small :class="isInRange ? 'text-success' : 'text-black-50'">({{ isInRange ? 'v dosahu' : 'mimo dosah' }})</small>
      </h1>
      <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
    </template>
    <template v-slot:body>
      <Loading v-if="regionStore.regionLoading || resourceStore.loading || buildingStore.currentBuildingLoading"></Loading>

      <RegionInfoModal
        v-if="dataType === 'region' && regionStore.region"
        :region="regionStore.region"
        :regionLoading="regionStore.regionLoading"
      />

      <ResourceInfoModal
        v-if="dataType === 'resource' && resourceStore.resource"
        :resource="resourceStore.resource"
        :resourceLoading="resourceStore.loading"
      />

      <BuildingInfoModal
        v-if="dataType === 'building' && buildingStore.currentBuilding"
        :building="buildingStore.currentBuilding"
        :buildingLoading="buildingStore.currentBuildingLoading"
        :isInRange="isInRange"
      />

      <ItemInfoModal
        v-if="dataType === 'item' && data"
        :item="data"
        :isInRange="isInRange"
        :canPickUpItem="canPickUpItem"
      />
    </template>
  </Modal>
</template>