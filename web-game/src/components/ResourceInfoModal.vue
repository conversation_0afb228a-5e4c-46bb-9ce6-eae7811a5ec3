<script setup lang="ts">
import { useResourceStore } from "../store/resource";
import { computed } from "vue";

const resourceStore = useResourceStore();

const props = defineProps({
  resource: {
    type: Object,
    required: true
  },
  resourceLoading: {
    type: Boolean,
    required: true
  }
});
</script>

<template>
  <div v-if="props.resource">
    <div v-if="props.resource.resource_amounts">
      <h6>Zásoba</h6>
      <ul class="list-unstyled">
        <li v-for="production in props.resource.resource_amounts">
          {{ production.type.name }}: {{ production.amount }}
        </li>
      </ul>
    </div>
  </div>
  <div v-else-if="props.resourceLoading">
    <p>Načítání zdroje...</p>
  </div>
  <div v-else>
    <p>Chyba při načítání zdroje.</p>
  </div>
</template>