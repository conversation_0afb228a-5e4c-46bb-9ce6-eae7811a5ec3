<script setup lang="ts">
import { useInventoryStore } from "../store/inventory";
import { computed } from "vue";

const inventoryStore = useInventoryStore();

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isInRange: {
    type: Boolean,
    required: true
  },
  canPickUpItem: {
    type: Boolean,
    required: true
  }
});

function pickUpItem() {
  if (props.item?.properties?.uuid) {
    inventoryStore.pickUp(props.item.properties.uuid)
      .then(() => {
        // emit('item-picked-up'); // TODO: emit event to parent component
      });
  }
}
</script>

<template>
  <div v-if="props.item">
    <p>{{ props.item.properties?.description || '' }}</p>

    <button class="btn btn-sm btn-primary"
            v-if="props.isInRange && props.canPickUpItem"
            @click.prevent="pickUpItem()">
      Vyzvednout
      <loading v-if="inventoryStore.pickUpLoading" />
    </button>
  </div>
  <div v-else>
    <p>Chyba při načítání předmětu.</p>
  </div>
</template>