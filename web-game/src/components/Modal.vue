<script setup lang="ts">
import { onMounted, ref, defineEmits, watch } from 'vue';
import { Modal } from 'bootstrap';

const props = defineProps({
	id: {
		required: true,
		type: String,
	},
	show: {
		default: false,
		required: false,
		type: Boolean,
	},
	title: {
		default: '',
		required: false,
		type: String,
	},
});

const emit = defineEmits(['hide', 'hidden', 'show', 'shown']);

const bootstrapModal = ref<Modal|null>(null);
const modalEl = ref<HTMLDivElement|null>(null);

onMounted(() => {
	bootstrapModal.value = Modal.getOrCreateInstance(modalEl.value);

	modalEl.value.addEventListener('hide.bs.modal', () => emit('hide'));
	modalEl.value.addEventListener('hidden.bs.modal', () => emit('hidden'));
	modalEl.value.addEventListener('show.bs.modal', () => emit('show'));
	modalEl.value.addEventListener('shown.bs.modal', () => emit('shown'));
})

watch(() => props.show, (show) => {
	if (bootstrapModal.value) {
		show ? bootstrapModal.value.show() : bootstrapModal.value.hide();
	}
});

</script>

<template>
	<div
		aria-hidden="true"
		class="fade modal"
		ref="modalEl"
		tabindex="-1"
		v-bind:aria-labelledby="`${id}-label`"
		v-bind:id="id"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<slot name="header">
						<h1 class="modal-title fs-5" v-bind:id="`${id}-label`">{{ title }}</h1>
						<button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
					</slot>
				</div>
				<div class="modal-body">
					<slot name="body"></slot>
				</div>
				<div class="modal-body mt-2" v-if="'footer-before' in $slots">
					<slot name="footer-before"></slot>
				</div>
				<div class="modal-footer" v-if="'footer' in $slots">
					<slot name="footer"></slot>
				</div>
			</div>
		</div>
	</div>
</template>