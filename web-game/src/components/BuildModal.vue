<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue';
import Modal from './Modal.vue'
import { useBuildingStore } from "../store/building";
import { useMapStore } from "../store/map";
import { usePlayerStore } from "../store/player";
import Loading from "../components/Loading.vue";
import { LMap } from "@vue-leaflet/vue-leaflet";
import {Circle, LatLng, Marker} from "leaflet";

const {open, map} = defineProps<{
	open: boolean,
	map: typeof LMap,
}>();

// Must be the same as in the backend
const buildingRadius = 15;

const buildingPosition = ref<Marker>(undefined);
const usabilityRange = ref<Circle>(undefined);

const mapStore = useMapStore();
const buildingStore = useBuildingStore();
const playerStore = usePlayerStore();

const buildingType = ref<string>("");
const buildingName = ref<string>("");

const error = ref<string | null>(null);

async function build() {
	error.value = null;
	buildingStore.build(
		buildingStore.currentType,
		buildingName.value,
		buildingStore.currentPosition ?? playerStore.position.latLng,
		{}
	)
		.then(() => {
			mapStore.fetchData();
			resetForm();
			mapStore.state = 'none';
		})
		.catch((e) => {
			error.value = 'Nepodařilo se postavit budovu (' + buildingStore.buildingsError.message + ')';
		});
}

function preview() {
	mapStore.state = 'build_preview';
}

function selectPosition() {
	mapStore.state = 'build_position';
	buildingStore.currentPosition = playerStore.position.latLng;
}

function cancel() {
	// Do not cancel if we are in preview mode or selecting position
	if (mapStore.state === 'build_preview' || mapStore.state === 'build_position') {
		return;
	}

	buildingStore.resetCurrentType();
	buildingStore.resetCurrentPosition();
	buildingType.value = '';
	mapStore.state = 'none';
}

function resetForm() {
	buildingName.value = '';
}

function changeType() {
	buildingStore.changeCurrentType(buildingType.value);
	buildingStore.fetchResourcesInRange(buildingStore.currentType);
}

const canBuild = computed(() => {
	if (buildingStore.currentType === undefined) {
		return false;
	}

	if (buildingName.value === '') {
		return false;
	}

	for (const cost of buildingStore.currentType.cost) {
		const playerResource = playerStore.findResource(cost.type.slug);
		if (!playerResource || playerResource.amount < cost.amount) {
			return false;
		}
	}

	return isPlaceEmpty.value;
});

const availableTypes = computed(() => {
	if (buildingStore.types === undefined) {
		return [];
	}

	return buildingStore.types.filter(type => {
		if (type.unique_in_world && buildingStore.buildings?.filter(b => b.type.slug === type.slug).length > 0) {
			return false;
		}

		if (type.unique_in_region && playerStore.current_region_buildings?.filter(b => b.type.slug === type.slug).length > 0) {
			return false;
		}

		return true;
	});
});

const isPlaceEmpty = computed(() => {
	let result = true;
	let buildingName = null;

	map.leafletObject.eachLayer((layer) => {
		if (layer.feature?.properties.type !== 'building') {
			return;
		}

		const point = layer.getLatLng();
		const distance = playerStore.position.latLng.distanceTo(point);

		if (distance <= buildingRadius) {
			result = false;
			buildingName = layer.feature.properties.name;
		}
	});

	if (!result) {
		error.value = `V dosahu 15m je již budova ${buildingName}`;
	} else {
		error.value = null;
	}

	return result;
});

// show building range
watch(() => buildingStore.currentType, (newValue) => {
	if (newValue) {
		buildingName.value = newValue.name + ' - ' + playerStore.current_region?.name;

		if (!usabilityRange.value) {
			usabilityRange.value = new Circle(playerStore.position.latLng, {
				radius: buildingStore.currentType.range,
				color: '#000000',
				weight: 1.0,
				fillColor: '#000000',
				fillOpacity: 0.1,
			});

			usabilityRange.value.addTo(map.leafletObject);
		}

		usabilityRange.value
			.setLatLng(playerStore.position.latLng)
			.setRadius(buildingStore.currentType.range);

	} else {
		if (usabilityRange.value) {
			usabilityRange.value.remove();
			usabilityRange.value = undefined;
		}
	}
});

// show building range
watch(() => buildingStore.currentPosition, (newValue) => {
	if (newValue && !buildingPosition.value) {
		buildingPosition.value = new Marker(playerStore.position.latLng, {
			draggable: true,
			title: 'Umístění budovy',
		});

		buildingPosition.value.addTo(map.leafletObject);

		// Check if the market is not too far from the player, if so, move it to the edge of the circle
		buildingPosition.value.on('drag', (event) => {
			const center = playerStore.position.latLng;
			const markerLatLng = event.target.getLatLng();
			const radius = 50;

			const distance = map.leafletObject.distance(center, markerLatLng);

			if (distance > radius) {
				const angle = Math.atan2(
					markerLatLng.lng - center.lng,
					markerLatLng.lat - center.lat
				);

				const newLat = center.lat + (radius * Math.cos(angle)) / 111320;
				const newLng = center.lng + (radius * Math.sin(angle)) / (111320 * Math.cos(center.lat * (Math.PI / 180)));

				buildingPosition.value.setLatLng([newLat, newLng]);
				buildingStore.currentPosition = new LatLng(newLat, newLng);
			}
		});

	} else if (!newValue && buildingPosition.value) {
		buildingPosition.value.remove();
		buildingPosition.value = undefined;
	}
});

onMounted(() => {
	error.value = null;
	buildingStore.fetchTypes();
});

</script>

<template>
	<modal id="build_modal" title="Stavba" v-bind:show="open" v-on:hide="cancel">
		<template v-slot:body>
			<div v-if="buildingStore.types" class="form-group mb-2">
				<label for="type">Typ</label>
				<select id="type" class="form-control" v-model="buildingType" @change="changeType">
					<option value="" selected disabled>Vyber typ</option>
					<option v-for="type in availableTypes" :value="type.slug">{{ type.name }}</option>
				</select>
			</div>
			<loading v-else></loading>

			<div v-if="buildingStore.currentType">
				<div class="form-group mb-2">
					<label for="name">Název</label>
					<input v-model="buildingName" id="name" class="form-control" type="text">
				</div>
				<ul class="list-unstyled">
					<li class="mb-2 text-black-50">{{ buildingStore.currentType.description }}</li>
					<li class="mb-2">Čas stavby: {{ buildingStore.currentType.build_time }}h</li>
					<li class="mb-2">
						<strong>Náklady:</strong>
						<ul class="list-unstyled">
							<li v-for="cost in buildingStore.currentType.cost">
								{{ cost.type.name }}: {{ cost.amount }}
								<strong v-if="playerStore.findResource(cost.type.slug)?.amount < cost.amount" class="text-danger">
									(chybí {{ cost.amount - (playerStore.findResource(cost.type.slug)?.amount ?? 0) }} !!)
								</strong>
							</li>
						</ul>
					</li>
					<li class="mb-2">
						<strong>Produkce:</strong>
						<ul>
							<li v-for="{type, levels} in buildingStore.currentType.productions">
								{{ type.name }}:
								<ol>
									<li v-for="amount in levels">
										lvl - {{ amount }} za hodinu
									</li>
								</ol>
							</li>
						</ul>
					</li>
					<li v-if="buildingStore.currentType.productions">
						<strong>Zdroje v dosahu:</strong>
						<loading v-if="buildingStore.resourcesInRangeLoading"></loading>
						<ul v-if="buildingStore.resourcesInRange?.length > 0" class="list-unstyled sources-list">
							<li v-for="{distance, amounts, resource_region} in buildingStore.resourcesInRange">
								{{ resource_region.type.name }} ({{ distance }}m)
								<ul v-if="amounts">
									<li v-for="{type, amount} in amounts">
										{{ type.name }}: {{ amount }}
									</li>
								</ul>
							</li>
						</ul>
						<div v-else class="text-warning">V dosahu nic využitelného</div>
					</li>
				</ul>
			</div>
		</template>
		<template v-slot:footer-before>
			<div v-if="error" class="alert alert-danger">{{ error }}</div>
		</template>
		<template v-slot:footer>
			<button type="button" class="btn btn-secondary" @click="cancel">Zrušit</button>
			<button v-if="buildingStore.currentType" type="button" class="btn btn-info" @click="selectPosition">Doladit umístění</button>
			<button v-if="buildingStore.currentType && buildingStore.currentType.range" type="button" class="btn btn-info" @click="preview">Ukázat dosah</button>
			<button type="button" class="btn btn-primary" @click="build" :disabled="!canBuild">Postavit</button>
		</template>
	</modal>
</template>

<style scoped>
.sources-list {
	max-height: 300px;
	overflow-y: scroll;
}
</style>