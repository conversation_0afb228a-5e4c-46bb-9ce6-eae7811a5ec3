<template>
	<slot :day="day" :hour="hour" :min="min" :sec="sec">
		<span v-if="millis > 0">zbývá&nbsp;</span>
		<span v-if="millis > 0 && day > 0">
			{{day}} {{ day >= 5 ? 'dní' : (day >= 2 ? 'dny' : 'den') }}&nbsp;
		</span>
		<span v-if="millis > 0 && hour > 0">
			{{ hour }} {{ hour >= 5 ? 'hodin' : (hour >= 2 ? 'hodiny' : 'hodina') }}&nbsp;
		</span>
		<span v-if="millis > 0 && min > 0">
			{{ min }} {{ min >= 5 ? 'minut' : (min >= 2 ? 'minuty' : 'minuta') }}
		</span>
		<span v-if="millis > 0 && !day && !hour && !min && sec >= 20">minutka</span>
		<span v-if="millis > 0 && !day && !hour && !min && sec < 20">chvilinka</span>

		<span v-if="millis === 0 || (millis < 0 && !negative)">hotovo</span>

		<span v-if="negative && millis < 0">před&nbsp;</span>
		<span v-if="negative && day < 0">
			{{ -day }} {{ day <= -5 ? 'dny' : 'dnem' }}&nbsp;
		</span>
		<span v-if="negative && hour < 0">
			{{ -hour }} {{ hour <= -5 ? 'hodinami' : 'hodinou' }}&nbsp;
		</span>
		<span v-if="negative && min < 0">
			{{ -min }} {{ min <= -5 ? 'minutami' : 'minutou' }}
		</span>
		<span v-if="negative && millis < 0 && !day && !hour && !min && sec <= -20">minutka</span>
		<span v-if="negative && millis < 0 && !day && !hour && !min && sec < -20">chvilinka</span>
	</slot>
</template>

<script setup lang="ts">

import {computed, onBeforeUnmount, ref, watch} from "vue";

const props = defineProps({
	endDate: {
		type: [Date],
		required: true
	},
	negative: {
		type: Boolean,
		default: false
	}
});

const emit = defineEmits(['endTime']);

const now = ref(new Date());
const timer = ref<NodeJS.Timeout>(undefined);

const day = computed(() => {
	return Math.trunc((props.endDate.getTime() - now.value.getTime()) / 1000 / 3600 / 24);
});

const hour = computed(() => {
	return Math.trunc((props.endDate.getTime() - now.value.getTime()) / 1000 / 3600);
});

const min = computed(() => {
	return Math.trunc((props.endDate.getTime() - now.value.getTime()) / 1000 / 60) % 60;
});

const sec = computed(() => {
	return Math.trunc((props.endDate.getTime() - now.value.getTime()) / 1000) % 60;
});

const millis = computed(() => {
	return Math.trunc((props.endDate.getTime() - now.value.getTime()));
});

watch(() => props.endDate, (newVal) => {
	if (timer.value) {
		clearInterval(timer.value);
	}
	timer.value = setInterval(() => {
		now.value = new Date();
		if (props.negative) {
			return;
		}
		if (now.value > newVal) {
			now.value = newVal;
			emit('endTime');
			clearInterval(timer.value);
		}
	}, 1000);
}, {immediate: true});

onBeforeUnmount(() => {
	clearInterval(timer.value);
});

</script>