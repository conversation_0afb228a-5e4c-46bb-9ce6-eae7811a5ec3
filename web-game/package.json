{"name": "kokume/web", "version": "0.0.1", "author": "<PERSON><PERSON>", "type": "module", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@hotjar/browser": "^1.0.9", "@mapbox/leaflet-pip": "^1.1.0", "@sentry/browser": "^8.47.0", "@sentry/vue": "^8.47.0", "@sentry/webpack-plugin": "^2.22.7", "@vue-leaflet/vue-leaflet": "^0.10.1", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bootstrap-vue": "^2.23.1", "dragdroptouch-bug-fixed": "^1.0.8", "font-awesome": "^4.7.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "naja": "^2.5.0", "nette-forms": "^3.3.1", "osmtogeojson": "^3.0.0-beta.5", "overpass-frontend": "^3.3.0", "pinia": "^2.2.0", "popper.js": "^1.16.1", "ublaboo-datagrid": "^6.9.1", "vue": "^3.4.27", "vue-axios": "^3.5.2", "vue-router": "^4.4.0"}, "devDependencies": {"@types/bootstrap": "^5.2.10", "@types/leaflet": "^1.9.12", "@types/lodash": "^4.17.7", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "buffer": "^6.0.3", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.37.1", "cross-env": "^7.0.2", "css-loader": "^7.1.2", "esbuild-loader": "^4.1.0", "file-loader": "^6.0.0", "image-minimizer-webpack-plugin": "^4.0.2", "mini-css-extract-plugin": "^2.9.0", "node-polyfill-webpack-plugin": "^4.0.0", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.14", "raw-loader": "^4.0.2", "sass": "^1.86.0", "sass-loader": "^16.0.5", "thread-loader": "^4.0.2", "url-loader": "^4.1.1", "vue-loader": "^17.4.2", "vue-template-compiler": "^2.7.16", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0", "webpack-stats-plugin": "^1.1.3"}, "browserslist": ["last 2 versions", "not dead"], "scripts": {"watch": "cross-env NODE_ENV=development webpack --watch --progress", "dev": "cross-env NODE_ENV=development webpack serve --progress", "build-dev": "cross-env NODE_ENV=development webpack --progress", "build": "cross-env NODE_ENV=production webpack --progress"}}