services:
  backend-php:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    depends_on:
      - postgres
    env_file: prod.env
    volumes:
      # Only mount persistent data directories, not the entire app
      - ./data/logs:/app/var/log
      - ./data/temp:/app/var/temp
      - ./data/files:/app/www/files
      # Mount local.neon configuration
      - ./config/local.neon:/app/config/local.neon

  backend-nginx:
    build:
      context: ./backend
      dockerfile: Dockerfile.nginx
    restart: always
    depends_on:
      - php
    ports:
      - "80:80"
    volumes:
      # Only mount files directory for uploads
      - ./data/files:/app/www/files

  postgres:
    image: postgis/postgis:17-3.5-alpine
    restart: always
    env_file: prod.env
    volumes:
      - ./data/postgres:/var/lib/postgresql/data

  adminer:
    image: dockette/adminer:dg
    restart: always
    ports:
      - 8080:80
