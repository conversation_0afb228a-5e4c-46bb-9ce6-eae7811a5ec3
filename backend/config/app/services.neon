search:
	-
		in: %appDir%
		classes:
			- *Command
			- *Factory
			- *Facade
			- *Service

services:
	# Domain =================
	- App\Domain\Player\PlayerPositionProvider
	- App\Domain\Item\Service\GroqItemDefinitionService(%groq.apiKey%)

	## Serialize =============
	symfony.serializer.serializer:
		factory: Symfony\Component\Serializer\Serializer
		arguments:
			normalizers:
				- Symfony\Component\Serializer\Normalizer\DateTimeNormalizer()
				- Symfony\Component\Serializer\Normalizer\ArrayDenormalizer()
				- @symfony.serializer.objectNormalizer
			encoders:
				- Symfony\Component\Serializer\Encoder\JsonEncoder()

	symfony.serializer.objectNormalizer:
		factory: Symfony\Component\Serializer\Normalizer\ObjectNormalizer(
			classMetadataFactory: @symfony.serializer.classMetadataFactory,
			nameConverter: Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter(),
			propertyAccessor: null,
			propertyTypeExtractor: Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor()
		)
		autowired: false

	symfony.serializer.classMetadataFactory:
		factory: Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory(@symfony.serializer.attributeLoader)
		autowired: false

	symfony.serializer.attributeLoader:
		factory: Symfony\Component\Serializer\Mapping\Loader\AttributeLoader()
		autowired: false

	symfony.serializer.annotationReader:
		factory: Doctrine\Common\Annotations\AnnotationReader
		autowired: false
		setup:
			- addGlobalIgnoredName(phpcsSuppress)

	## Validator =============
	symfony.validator:
		type: Symfony\Component\Validator\Validator\ValidatorInterface
		factory: @symfony.validator.builder::getValidator()

	symfony.validator.builder:
		type: Symfony\Component\Validator\ValidatorBuilder
		factory: Symfony\Component\Validator\Validation::createValidatorBuilder()::enableAttributeMapping()
		autowired: false

	# Forms ===================
	- App\UI\Form\FormFactory

	# Security ================
	nette.userStorage:
		setup:
			- setNamespace("Webapp")

	security.passwords: App\Model\Security\Passwords
	security.user: App\Model\Security\SecurityUser
	security.authenticator: App\Model\Security\Authenticator\UserAuthenticator
	security.authorizator: App\Model\Security\Authorizator\StaticAuthorizator

	# Routing ================
	- App\Model\Router\RouterFactory
	router:
		type: Nette\Application\IRouter
		factory: @App\Model\Router\RouterFactory::create

	# Domain =================
	- App\Domain\User\CreateUserFacade

	# Console ================
	- App\Console\HelloCommand

	- App\Model\Database\QueryManager

	- App\Domain\Unit\Service\UnitProductionService
	- App\Domain\Unit\Service\UnitSustenanceService

latte:
	macros:
		- App\Model\Latte\Macros::register
