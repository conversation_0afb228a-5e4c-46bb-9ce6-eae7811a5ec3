# Core Config
includes:
	# Application
	- ../app/parameters.neon
	- ../app/services.neon

	# Extensions
	- ../ext/apitte.neon
	- ../ext/contributte.neon
	- ../ext/nettrine.neon
	- ../ext/oauth2_server.neon

php:
	date.timezone: Europe/Prague
	output_buffering: 4096

# Nette section
di:
	debugger: false
	export:
		parameters: no
		tags: no

tracy:
	email: %system.error.email%
	logSeverity: E_ALL
	strictMode: yes

application:
	catchExceptions: %productionMode%
	errorPresenter:
		4xx: Error:Error4xx
		5xx: Error:Error5xx
	mapping: App\UI\Modules\*\**Presenter

latte:
	strictTypes: yes
	strictParsing: yes
	extensions:
		- App\Model\Latte\LatteExtension

session:
	autoStart: smart
	debugger: true
	expiration: 14 days

http:
	headers:
		X-XSS-Protection: '1; mode=block'
		X-Powered-By: 'kokume'
		X-Content-Type-Options: nosniff

routing:
	debugger: %debugMode%
