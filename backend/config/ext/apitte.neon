# Extension > Apitte
#
extensions:
	middlewares: Contributte\Middlewares\DI\NetteMiddlewaresExtension
	resource: Contributte\DI\Extension\ResourceExtension
	api: Apitte\Core\DI\ApiExtension
	oauth2.server: Contributte\OAuth2Server\DI\OAuth2ServerExtension

resource:
	resources:
		App\API\:
			paths: [%appDir%/API]

api:
	debug: %debugMode%
	plugins:
		Apitte\Core\DI\Plugin\CoreSchemaPlugin:
		Apitte\Core\DI\Plugin\CoreServicesPlugin:
		Apitte\Core\DI\Plugin\CoreMappingPlugin:
			request:
				validator: Apitte\Core\Mapping\Validator\SymfonyValidator
		Apitte\Debug\DI\DebugPlugin:
		Apitte\Middlewares\DI\MiddlewaresPlugin:
		Apitte\OpenApi\DI\OpenApiPlugin:
			swaggerUi:
				panel: true

services:
	middleware.tryCatch:
		factory: Contributte\Middlewares\TryCatchMiddleware
		tags: [middleware: [priority: 1]]
		setup:
			- setDebugMode(%debugMode%)
			- setCatchExceptions(%productionMode%) # used in debug only
#			- setLogger(@Psr\Log\LoggerInterface, Psr\Log\LogLevel::ERROR)
#	middlewares.logging:
#		factory: Contributte\Middlewares\LoggingMiddleware
#		arguments: [@monolog.logger.default]
#		tags: [middleware: [priority: 100]]
	middleware.cors:
		factory: App\Model\Api\Middleware\CORSMiddleware
		tags: [middleware: [priority: 200]]
	middleware.authenticator:
		factory: App\Model\Api\Middleware\AuthenticationMiddleware(
			App\Model\Api\Security\TokenAuthenticator()
		)
		tags: [middleware: [priority: 250]]

	api.core.dispatcher: App\Model\Api\Dispatcher\JsonDispatcher
	api.core.errorHandler: App\Model\Api\ErrorHandler\ThrowingErrorHandler
