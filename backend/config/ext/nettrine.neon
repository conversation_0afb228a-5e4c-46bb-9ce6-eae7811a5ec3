# Extension > Nettrine
#
extensions:
	# Common
	nettrine.annotations: Nettrine\Annotations\DI\AnnotationsExtension

	# Dbal
	nettrine.dbal: Nettrine\DBAL\DI\DbalExtension
	nettrine.dbal.console: Nettrine\DBAL\DI\DbalConsoleExtension(%consoleMode%)

	# Orm
	nettrine.orm: Nettrine\ORM\DI\OrmExtension
	nettrine.orm.cache: Nettrine\ORM\DI\OrmCacheExtension
	nettrine.orm.console: Nettrine\ORM\DI\OrmConsoleExtension(%consoleMode%)
	nettrine.orm.attributes: Nettrine\ORM\DI\OrmAttributesExtension

	nettrine.migrations: Nettrine\Migrations\DI\MigrationsExtension
	nettrine.fixtures: Nettrine\Fixtures\DI\FixturesExtension
	nettrine.cache: Nettrine\Cache\DI\CacheExtension

nettrine.dbal:
	debug:
		panel: %debugMode%
	configuration:
#		sqlLogger: Nettrine\DBAL\Logger\PsrLogger(@Monolog\Logger)

	connection:
		driver: %database.driver%
		host: %database.host%
		user: %database.user%
		password: %database.password%
		dbname: %database.dbname%
		port: %database.port%
		serverVersion: %database.serverVersion%
		types:
			geography:
				class: 'Jsor\Doctrine\PostGIS\Types\GeographyType'
				commented: false
			geometry:
				class: 'Jsor\Doctrine\PostGIS\Types\GeometryType'
				commented: false
			position: App\Model\Database\Type\PositionType
			uuid: Ramsey\Uuid\Doctrine\UuidType
			uuid_binary_ordered_time:
				class: Ramsey\Uuid\Doctrine\UuidBinaryOrderedTimeType
				commented: false
		typesMapping:
			_text: string
			_int8: bigint
			uuid_binary_ordered_time: binary

nettrine.orm:
	entityManagerDecoratorClass: App\Model\Database\EntityManager
	configuration:
		autoGenerateProxyClasses: %debugMode%
		customStringFunctions:
			ST_Within: Jsor\Doctrine\PostGIS\Functions\ST_Within
			ST_DWithin: Jsor\Doctrine\PostGIS\Functions\ST_DWithin
			ST_SetSRID: Jsor\Doctrine\PostGIS\Functions\ST_SetSRID
			ST_Point: Jsor\Doctrine\PostGIS\Functions\ST_Point
			ST_AsEWKT: Jsor\Doctrine\PostGIS\Functions\ST_AsEWKT
			ST_GeneratePoints: App\Model\Database\Function\ST_GeneratePoints
		customNumericFunctions:
			ST_Distance: Jsor\Doctrine\PostGIS\Functions\ST_Distance

nettrine.orm.attributes:
	mapping:
		App\Domain: %appDir%/Domain

nettrine.orm.cache:

nettrine.migrations:
	table: doctrine_migrations
	column: version
	directory: %rootDir%/db/Migrations
	namespace: Database\Migrations
	versionsOrganization: null

nettrine.fixtures:
	paths:
		- %rootDir%/db/Fixtures

decorator:
	Doctrine\Common\EventSubscriber:
		tags: [nettrine.subscriber]

# PostGIS
services:
	doctrine.postgis.ormSchemaEventSubscriber:
		class: Jsor\Doctrine\PostGIS\Event\ORMSchemaEventSubscriber
		tags:
			name: doctrine.event_subscriber
			connection: default
	doctrine.postgis.dbalSchemaEventSubscriber:
		class: Jsor\Doctrine\PostGIS\Event\DBALSchemaEventSubscriber
		tags:
			name: doctrine.event_subscriber
			connection: default
