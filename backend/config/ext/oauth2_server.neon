extensions:
	oauth2.server: Contributte\OAuth2Server\DI\OAuth2ServerExtension

oauth2.server:
	encryptionKey: %oauth_server.encryptionKey%
	privateKey:
		path: %oauth_server.privateKeyPath%
		passPhrase: %oauth_server.keyPassPhrase%
		permissionCheck: false
	publicKey:
		path: %oauth_server.publicKeyPath%
		permissionCheck: false
	grants:
		authCode: false
		clientCredentials: false
		implicit: false
		password: true
		refreshToken: true

services:
	- App\Domain\OAuth2\Scope\ScopeRepository

	oauth2.server.authorizationServer:
		arguments:
			clientRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\OAuth2\Client\Client)
			accessTokenRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\OAuth2\AccessToken\AccessToken)
			scopeRepository: @App\Domain\OAuth2\Scope\ScopeRepository

	oauth2.server.resourceServer:
		arguments:
			accessTokenRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\OAuth2\AccessToken\AccessToken)

	# Grant types
	oauth2.server.grant.password:
		arguments:
			userRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\User\User)
			refreshTokenRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\OAuth2\RefreshToken\RefreshToken)

	oauth2.server.grant.refreshToken:
		arguments:
			refreshTokenRepository: @App\Model\Database\EntityManager::getRepository(App\Domain\OAuth2\RefreshToken\RefreshToken)