<?php declare(strict_types = 1);

namespace Database\Fixtures;

use App\Domain\User\User;
use App\Model\Fixtures\ReflectionLoader;
use App\Model\Security\Passwords;
use Doctrine\Persistence\ObjectManager;

class UserFixture extends AbstractFixture {
	private ObjectManager $manager;

	public function getOrder() : int {
		return 1;
	}

	public function load(ObjectManager $manager) : void {
		$this->manager = $manager;

		foreach ($this->getStaticUsers() as $user) {
			$this->saveUser($user);
		}

		foreach ($this->getRandomUsers() as $user) {
			$this->manager->persist($user);
		}

		$this->manager->flush();
	}

	/**
	 * @return mixed[]
	 */
	protected function getStaticUsers() : iterable {
		yield [
			'email'	=> '<EMAIL>',
			'role' 	=> User::ROLE_ADMIN,
		];
	}

	/**
	 * @param mixed[] $user
	 */
	protected function saveUser(array $user) : void {
		$entity = new User(
			$user['email'],
			Passwords::create()->hash('admin'),
		);
		$entity->activate();
		$entity->setRole($user['role']);

		$this->manager->persist($entity);
	}

	/**
	 * @return User[]
	 */
	protected function getRandomUsers() : iterable {
		$loader = new ReflectionLoader();
		$objectSet = $loader->loadData([
			User::class => [
				'user{1..1}' => [
					'__construct' => [
						'<email()>',
						'<password()>',
					],
					'id' => '<(intval(strval($current)))>',
				],
			],
		]);

		return $objectSet->getObjects();
	}

}
