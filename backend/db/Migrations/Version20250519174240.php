<?php

declare(strict_types=1);

namespace Database\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250519174240 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
		$this->abortIf(
			!$this->connection->getDatabasePlatform() instanceof \Doctrine\DBAL\Platforms\PostgreSQL120Platform,
			"Migration can only be executed safely on '\Doctrine\DBAL\Platforms\PostgreSQL120Platform'."
		);

		$this->addSql('CREATE SEQUENCE game_player_position_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_player_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE "user_id_seq" INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_building_job_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_building_type_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_building_resource_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_item_definition_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_hero_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_region_production_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_region_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE SEQUENCE game_unit_definition_id_seq INCREMENT BY 1 MINVALUE 1 START 1;');
		$this->addSql('CREATE TABLE game_building_job (id INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id));');
		$this->addSql('ALTER TABLE api_refresh_token ADD CONSTRAINT FK_6F2294192CCB2688 FOREIGN KEY (access_token_id) REFERENCES api_access_token (identifier) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE api_access_token ADD CONSTRAINT FK_BCC804C519EB6921 FOREIGN KEY (client_id) REFERENCES api_client (identifier) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_player_position ADD CONSTRAINT FK_3F4B62F299E6F5DF FOREIGN KEY (player_id) REFERENCES game_player (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_player ADD CONSTRAINT FK_E52CD7ADA76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building_type_unit_production ADD CONSTRAINT FK_D2BE23C3F28401B9 FOREIGN KEY (building_type_id) REFERENCES game_building_type (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building_type_unit_production ADD CONSTRAINT FK_D2BE23C3BFA73914 FOREIGN KEY (unit_definition_id) REFERENCES game_unit_definition (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER INDEX idx_6963bdfbf28401b9 RENAME TO IDX_D2BE23C3F28401B9;');
		$this->addSql('ALTER INDEX idx_6963bdfbbfa73914 RENAME TO IDX_D2BE23C3BFA73914;');
		$this->addSql('ALTER TABLE game_building_resource ADD CONSTRAINT FK_7322B8C54D2A7E12 FOREIGN KEY (building_id) REFERENCES game_building (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building_resource ADD CONSTRAINT FK_7322B8C530EF52CA FOREIGN KEY (resource_region_id) REFERENCES game_region (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building ADD CONSTRAINT FK_4A2B9EF299E6F5DF FOREIGN KEY (player_id) REFERENCES game_player (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building ADD CONSTRAINT FK_4A2B9EF2F28401B9 FOREIGN KEY (building_type_id) REFERENCES game_building_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building ADD CONSTRAINT FK_4A2B9EF298260155 FOREIGN KEY (region_id) REFERENCES game_region (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_item_placed ADD CONSTRAINT FK_25D0DE5F126F525E FOREIGN KEY (item_id) REFERENCES game_item (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_item_placed ADD CONSTRAINT FK_25D0DE5F98260155 FOREIGN KEY (region_id) REFERENCES game_region (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_item ADD CONSTRAINT FK_F40E49323DB201CA FOREIGN KEY (item_definition_id) REFERENCES game_item_definition (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_hero_item ADD CONSTRAINT FK_4D56865A45B0BCD FOREIGN KEY (hero_id) REFERENCES game_hero (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_hero_item ADD CONSTRAINT FK_4D56865A126F525E FOREIGN KEY (item_id) REFERENCES game_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_hero ADD CONSTRAINT FK_BADB02AA99E6F5DF FOREIGN KEY (player_id) REFERENCES game_player (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_region_production ADD CONSTRAINT FK_E635C88998260155 FOREIGN KEY (region_id) REFERENCES game_region (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_region ADD CONSTRAINT FK_72575CBE7E3C61F9 FOREIGN KEY (owner_id) REFERENCES game_player (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_region_has_resource_region ADD CONSTRAINT FK_5E2874DD98260155 FOREIGN KEY (region_id) REFERENCES game_region (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_region_has_resource_region ADD CONSTRAINT FK_5E2874DD30EF52CA FOREIGN KEY (resource_region_id) REFERENCES game_region (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_unit ADD CONSTRAINT FK_37AE607F4D2A7E12 FOREIGN KEY (building_id) REFERENCES game_building (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_unit ADD CONSTRAINT FK_37AE607F45B0BCD FOREIGN KEY (hero_id) REFERENCES game_hero (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_unit ADD CONSTRAINT FK_37AE607FBFA73914 FOREIGN KEY (unit_definition_id) REFERENCES game_unit_definition (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building_production_queue ADD CONSTRAINT FK_99DECB764D2A7E12 FOREIGN KEY (building_id) REFERENCES game_building (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
		$this->addSql('ALTER TABLE game_building_production_queue ADD CONSTRAINT FK_99DECB76BFA73914 FOREIGN KEY (unit_definition_id) REFERENCES game_unit_definition (id) NOT DEFERRABLE INITIALLY IMMEDIATE;');
    }

    public function down(Schema $schema): void
    {

    }
}
