<?php

declare(strict_types=1);

namespace Database\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250519174239 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
		$this->abortIf(
			!$this->connection->getDatabasePlatform() instanceof \Doctrine\DBAL\Platforms\PostgreSQL120Platform,
			"Migration can only be executed safely on '\Doctrine\DBAL\Platforms\PostgreSQL120Platform'."
		);

        $this->addSql(<<<'SQL'
            CREATE TABLE game_unit_definition (id INT NOT NULL, name VARCHAR(255) NOT NULL, description TEXT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, size INT DEFAULT 1 NOT NULL, defense INT DEFAULT 1 NOT NULL, strength INT DEFAULT 1 NOT NULL, agility INT DEFAULT 1 NOT NULL, luck INT DEFAULT 1 NOT NULL, base_health INT DEFAULT 100 NOT NULL, food_cost INT DEFAULT 1 NOT NULL, gold_cost INT DEFAULT 0 NOT NULL, PRIMARY KEY(id))
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE api_access_token (identifier VARCHAR(255) NOT NULL, client_id VARCHAR(255) NOT NULL, scopes JSON NOT NULL, expiry_date_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, user_identifier VARCHAR(255) DEFAULT NULL, PRIMARY KEY(identifier))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bcc804c519eb6921 ON api_access_token (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN api_access_token.expiry_date_time IS '(DC2Type:datetime_immutable)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_player (id INT NOT NULL, user_id INT NOT NULL, nickname VARCHAR(255) NOT NULL, resources JSON DEFAULT '{}' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, range INT DEFAULT 50 NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_e52cd7ada76ed395 ON game_player (user_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_item (id UUID NOT NULL, item_definition_id INT NOT NULL, durability INT NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f40e49323db201ca ON game_item (item_definition_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_item.id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_player_position (id INT NOT NULL, player_id INT NOT NULL, accuracy DOUBLE PRECISION NOT NULL, speed DOUBLE PRECISION DEFAULT NULL, user_agent VARCHAR(255) NOT NULL, ip VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "position" geometry(POINT, 4326) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_3f4b62f299e6f5df ON game_player_position (player_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_building_type_unit_production (building_type_id INT NOT NULL, unit_definition_id INT NOT NULL, production_time INT NOT NULL, cost JSON DEFAULT '{}' NOT NULL, PRIMARY KEY(building_type_id, unit_definition_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6963bdfbbfa73914 ON game_building_type_unit_production (unit_definition_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6963bdfbf28401b9 ON game_building_type_unit_production (building_type_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_building_resource (id INT NOT NULL, building_id UUID NOT NULL, resource_region_id INT NOT NULL, distance DOUBLE PRECISION NOT NULL, total_production JSON DEFAULT '{}' NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7322b8c530ef52ca ON game_building_resource (resource_region_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7322b8c54d2a7e12 ON game_building_resource (building_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_building_resource.building_id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_building (id UUID NOT NULL, player_id INT NOT NULL, building_type_id INT NOT NULL, region_id INT NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, "position" geometry(POINT, 4326) NOT NULL, state VARCHAR(255) DEFAULT 'building' NOT NULL, state_valid_to TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, level INT DEFAULT 1 NOT NULL, stock JSON DEFAULT '{}' NOT NULL, last_resources_update TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, production_accumulator JSON DEFAULT '{}' NOT NULL, health INT DEFAULT 100 NOT NULL, max_units INT DEFAULT 10 NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4a2b9ef298260155 ON game_building (region_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4a2b9ef2f28401b9 ON game_building (building_type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4a2b9ef299e6f5df ON game_building (player_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_building.id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE api_client (identifier VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, redirect_uri VARCHAR(255) NOT NULL, secret VARCHAR(255) DEFAULT NULL, is_confidential BOOLEAN DEFAULT false NOT NULL, grant_types JSON DEFAULT '["password", "refresh_token"]' NOT NULL, PRIMARY KEY(identifier))
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_hero (id INT NOT NULL, player_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, level INT DEFAULT 1 NOT NULL, experiences INT DEFAULT 0 NOT NULL, health INT DEFAULT 100 NOT NULL, max_health INT DEFAULT 100 NOT NULL, defense INT DEFAULT 1 NOT NULL, strength INT DEFAULT 1 NOT NULL, agility INT DEFAULT 1 NOT NULL, max_items INT DEFAULT 25 NOT NULL, sex VARCHAR(255) DEFAULT 'none' NOT NULL, description VARCHAR(255) DEFAULT NULL, defense_bonus INT DEFAULT 0 NOT NULL, strength_bonus INT DEFAULT 0 NOT NULL, agility_bonus INT DEFAULT 0 NOT NULL, max_health_bonus INT DEFAULT 0 NOT NULL, healing_bonus INT DEFAULT 0 NOT NULL, luck INT DEFAULT 1 NOT NULL, luck_bonus INT DEFAULT 0 NOT NULL, max_units INT DEFAULT 10 NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_badb02aa99e6f5df ON game_hero (player_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_building_production_queue (id UUID NOT NULL, building_id UUID NOT NULL, unit_definition_id INT NOT NULL, start_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_99decb76bfa73914 ON game_building_production_queue (unit_definition_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_99decb764d2a7e12 ON game_building_production_queue (building_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_building_production_queue.id IS '(DC2Type:uuid)'
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_building_production_queue.building_id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_hero_item (item_id UUID NOT NULL, hero_id INT NOT NULL, slot_key VARCHAR(255) NOT NULL, PRIMARY KEY(item_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX hero_item_slot ON game_hero_item (hero_id, slot_key)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4d56865a45b0bcd ON game_hero_item (hero_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_hero_item.item_id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_region (id INT NOT NULL, owner_id INT DEFAULT NULL, osm_id INT NOT NULL, level VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, "position" geometry(POINT, 4326) NOT NULL, type VARCHAR(255) NOT NULL, resource_region_type VARCHAR(255) DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_72575cbe7e3c61f9 ON game_region (owner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_72575cbea65eb5cf ON game_region (osm_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE api_refresh_token (identifier VARCHAR(255) NOT NULL, access_token_id VARCHAR(255) NOT NULL, expiry_date_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(identifier))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6f2294192ccb2688 ON api_refresh_token (access_token_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN api_refresh_token.expiry_date_time IS '(DC2Type:datetime_immutable)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE "user" (id INT NOT NULL, email VARCHAR(255) NOT NULL, state INT NOT NULL, password VARCHAR(255) NOT NULL, role VARCHAR(255) NOT NULL, last_logged_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, banned_to TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_8d93d649e7927c74 ON "user" (email)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_unit (id UUID NOT NULL, building_id UUID DEFAULT NULL, hero_id INT DEFAULT NULL, unit_definition_id INT NOT NULL, hungry BOOLEAN DEFAULT false NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, health INT DEFAULT 100 NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37ae607fbfa73914 ON game_unit (unit_definition_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37ae607f45b0bcd ON game_unit (hero_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37ae607f4d2a7e12 ON game_unit (building_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_unit.id IS '(DC2Type:uuid)'
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_unit.building_id IS '(DC2Type:uuid)'
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_building_type (id INT NOT NULL, slug VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, description TEXT NOT NULL, build_time INT DEFAULT 30 NOT NULL, build_cost JSON DEFAULT '{}' NOT NULL, range INT DEFAULT 150 NOT NULL, productions JSON DEFAULT '{}' NOT NULL, unique_in_world BOOLEAN DEFAULT false NOT NULL, demolish_time INT DEFAULT 5 NOT NULL, unique_in_region BOOLEAN DEFAULT false NOT NULL, demolish_gain JSON DEFAULT '{}' NOT NULL, attack INT DEFAULT 0 NOT NULL, defense INT DEFAULT 0 NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_801ebc16989d9b62 ON game_building_type (slug)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_item_definition (id INT NOT NULL, type VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, description TEXT NOT NULL, spawnable BOOLEAN DEFAULT false NOT NULL, commonness INT DEFAULT 0 NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, category VARCHAR(255) NOT NULL, min_level INT DEFAULT NULL, attributes JSON DEFAULT '{}' NOT NULL, PRIMARY KEY(id))
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_region_production (id INT NOT NULL, region_id INT NOT NULL, resource_type VARCHAR(255) NOT NULL, amount INT NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX region_resource_type_unique ON game_region_production (region_id, resource_type)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e635c88998260155 ON game_region_production (region_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_region_has_resource_region (region_id INT NOT NULL, resource_region_id INT NOT NULL, PRIMARY KEY(region_id, resource_region_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5e2874dd30ef52ca ON game_region_has_resource_region (resource_region_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5e2874dd98260155 ON game_region_has_resource_region (region_id)
        SQL);
        

        $this->addSql(<<<'SQL'
            CREATE TABLE game_item_placed (item_id UUID NOT NULL, region_id INT NOT NULL, "position" geometry(POINT, 4326) NOT NULL, PRIMARY KEY(item_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_25d0de5f98260155 ON game_item_placed (region_id)
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN game_item_placed.item_id IS '(DC2Type:uuid)'
        SQL);
    }

    public function down(Schema $schema): void
    {

    }
}
