server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    server_tokens off;

    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    index index.php index.html;
    root /app/www;

 	location / {
		# First attempt to serve request as file, then
		# as directory, then fall back to index.php
		#try_files $uri $uri/ /index.php?q=$uri&$args;
		try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass backend-php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
}