{"name": "kokume/backend", "version": "0.0.1", "author": "<PERSON><PERSON>", "type": "module", "dependencies": {"font-awesome": "^4.7.0", "@hotjar/browser": "^1.0.9", "bootstrap": "^5.3.3", "naja": "^2.5.0", "nette-forms": "^3.3.1", "ublaboo-datagrid": "^6.9.1", "jquery": "^3.7.1"}, "devDependencies": {"@types/bootstrap": "^5.2.10", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "buffer": "^6.0.3", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.37.1", "cross-env": "^7.0.2", "css-loader": "^6.8.1", "esbuild-loader": "^4.1.0", "file-loader": "^6.0.0", "image-minimizer-webpack-plugin": "^4.0.2", "mini-css-extract-plugin": "^2.9.0", "node-polyfill-webpack-plugin": "^4.0.0", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.14", "raw-loader": "^4.0.2", "sass": "^1.77.5", "sass-loader": "^14.2.1", "thread-loader": "^4.0.2", "url-loader": "^4.1.1", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0", "webpack-stats-plugin": "^1.1.3"}, "browserslist": ["last 2 versions", "not dead"], "scripts": {"watch": "cross-env NODE_ENV=development webpack --watch --progress", "dev": "cross-env NODE_ENV=development webpack serve --progress", "build-dev": "cross-env NODE_ENV=development webpack --progress", "build": "cross-env NODE_ENV=production webpack --progress"}}