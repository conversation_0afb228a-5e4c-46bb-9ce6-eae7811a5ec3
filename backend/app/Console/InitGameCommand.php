<?php declare(strict_types = 1);

namespace App\Console;

use App\Domain\Region\CreateRegionFacade;
use App\Domain\Region\Region;
use App\Domain\Region\ResourceRegion;
use App\Domain\Region\ResourceRegionType;
use App\Model\Database\EntityManager;
use App\Model\Utils\Caster;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: self::NAME)]
class InitGameCommand extends Command {

	public const NAME = 'kokume:init-game';

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly CreateRegionFacade 	$createRegionFacade,
	) {
		parent::__construct();
	}

	protected function configure() : void {
		$this->setName(self::NAME);
		$this->setDescription('Initialize the game.');
		$this->addOption('update', 'u', InputOption::VALUE_NONE, 'Update the game');
		$this->addOption('force', 'f', InputOption::VALUE_NONE, 'Force reinitialization');
	}

	protected function execute(InputInterface $input, OutputInterface $output) : int {
		$update = Caster::toBool($input->getOption('update'));

		$db = $this->em->getConnection();

		if ($update) {
			$output->writeln('Updating the game...');
		} else {
			$output->writeln('Initializing the game...');

			if ($this->em->getRepository(Region::class)->count([]) > 0) {
				if (!Caster::toBool($input->getOption('force'))) {
					$output->writeln('The game is already initialized. Use --force to reinitialize or --update to update.');
					return 0;
				}
			}

			$db->executeQuery('TRUNCATE game_region, game_region_production, game_region_has_resource_region, game_building CASCADE');

			// Add default building types
//			$db->executeQuery('game_building, game_building_type CASCADE');
//			$db->executeStatement(<<<SQL
//				INSERT INTO game_building_type (id, slug, name, description, build_time, build_cost, range, products)
//				VALUES
//					(1, 'base', 'Domeček', 'Tvoje base', 0, '{"wood": 100, "clay": 55, "stone": 50}', 0, '[]'),
//					(2, 'sawmill', 'Pila', 'Pila no... co myslíš, že se v ní děje?!', 1, '{"wood": 150, "stone": 50, "iron": 10}', 150, '{"wood": {"1": 2, "2": 5, "3": 10}}'),
//					(3, 'quarry', 'Kamenolom', 'Vezůů kámeny...', 1, '{"wood": 150, "stone": 20, "iron": 15}', 50, '{"stone": {"1": 2, "2": 5, "3": 10}}'),
//					(4, 'iron_mine', 'Železný důl', 'Kopeme tady železo.', 1, '{"wood": 150, "stone": 20, "iron": 15}', 50, '{"iron": {"1": 1, "2": 2, "3": 5}}'),
//					(5, 'fishing_hut', 'Rybářský stan', 'Dones teplýho Braníčka a dostaneš rybu.', 1, '{"wood": 80, "stone": 5, "iron": 8}', 50, '{"food": {"1": 5, "2": 12, "3": 20}}'),
//					(6, 'farm', 'Farmářský dvůr', 'Krávy, prasata, slepice, ovce, kozel, králík, pes, kočka, kohout, kohoutice, kohoutíček, kohoutíčice, kohoutíčíček, kohoutíčíčice, kohoutíčíčíček, kohoutíčíčíčice, kohoutíčíčíčíček, kohoutíčíčíčíčice, kohoutíčíčíčíčíček, kohoutíčíčíčíčíčice, kohoutíčíčíčíčíčíček, kohoutíčíčíčíčíčíčice, kohoutíčíčíčíčíčíčíček, kohoutíčíčíčíčíčíčíčice, kohoutíčíčíčíčíčíčíčíček, kohoutíčíčíčíčíčíčíčíčice, kohoutíčíčíčíčíčíčíčíčíček, kohoutíčíčíčíčíčíčíčíčíčice, kohoutíčíčíčíčíčíčíčíčíčíček, kohoutíčíčíčíčíčíčíčíčíčíčice, kohoutíčíčíčíčíčíčíčíčíčíčíček, kohoutíčíčíčíčíčíčíčíčíčí', 1, '{"wood": 100, "stone": 50, "iron": 10}', 200, '{"food": {"1": 10, "2": 20, "3": 30}}')
//					(7, 'sub_base', 'Věž', '', 120, '{"wood": 150, "stone": 20, "iron": 15}', 50, '', false, false, 30);
//					(8, 'clay_mine', 'Jílový důl', 'Těžíme tady jíl.', 120, '{"wood": 150, "stone": 20, "iron": 15}', 50, '{"clay": {"1": 1, "2": 2, "3": 5}}', false, false, 30);
//			SQL);
		}

		$osmRegionsCount = Caster::toInt($db->fetchOne('SELECT COUNT(DISTINCT osm_id) FROM osm_polygon WHERE admin_level IS NOT NULL'));

		$limit = 50;
		$offset = 0;
		$newRegions = 0;

		$progress = new ProgressBar($output, $osmRegionsCount);
		$progress->start();

		while (TRUE) {
			/** @var array{osm_id: string, name: string, admin_level:string|null, position:string}[] $osmRegions */
			$osmRegions = $db->fetchAllAssociative('
					SELECT osm_id, name, admin_level, ST_AsText(ST_PointOnSurface(way)) as position FROM osm_polygon WHERE admin_level IS NOT NULL ORDER BY osm_id LIMIT ? OFFSET ? 
				', [$limit, $offset]);

			if (count($osmRegions) === 0) {
				break;
			}

			$regions = [];
			$resourceRegions = [];

			foreach ($osmRegions as $osmRegion) {

				// Get resources only for region with admin_level 9 and 10
				if (in_array($osmRegion['admin_level'], ["9", "10"], true)) {
					/** @var array{osm_id: string, name: string, landuse: string|null, natural:string|null, water:string|null, position:string, area_m2:float}[] $resources */
					$resources = $db->fetchAllAssociative(<<<SQL
						SELECT DISTINCT 
							region.osm_id, 
							region.name, 
							region.landuse, 
							region.natural, 
							region.water, 
							ST_AsText(ST_PointOnSurface(region.way)) AS position, 
							ST_Area(ST_Transform(region.way, 4326)::geography) AS area_m2 
						FROM osm_polygon region 
						JOIN osm_polygon op ON ST_Contains(op.way, region.way)
						WHERE region.admin_level IS NULL
							AND (region.landuse IS NOT NULL OR region."natural" IS NOT NULL)
							AND op.osm_id = ?
						SQL, [$osmRegion['osm_id']]);
				} else {
					$resources = [];
				}

				$region = $regions[$osmRegion['osm_id']] ?? $this->em->getRepository(Region::class)->findOneBy(['osmId' => $osmRegion['osm_id']]);

				if ($region === NULL) {
					$region = $this->createRegionFacade->createRegion($osmRegion);
					$newRegions++;
				}

				foreach ($resources as $resource) {
					$resourceRegion = $resourceRegions[$resource['osm_id']] ?? $this->em->getRepository(ResourceRegion::class)->findOneBy(['osmId' => $resource['osm_id']]);

					$landuse = Caster::toString($resource['landuse'] ?? $resource['natural']);
					$subtype = Caster::toNString($resource['water'] ?? NULL);
					$resourceRegionType = ResourceRegionType::mapFromLanduse($landuse, $subtype);

					if ($resourceRegionType === NULL) {
						if ($output->isVerbose()) {
							$output->writeln("Unknown landuse/natural type: $landuse ($subtype)");
						}
						continue;
					}

					// Skip small reservoirs, fields and forests
					if ($resource['area_m2'] < 200 && in_array($resourceRegionType, [ResourceRegionType::reservoir, ResourceRegionType::field, ResourceRegionType::forest], true)) {
						continue;
					}

					if ($resourceRegion === NULL) {
						$resourceRegion = $this->createRegionFacade->createResourceRegion($resourceRegionType, $resource);
						$newRegions++;
					} else {
						$this->createRegionFacade->fillResourceRegion($resourceRegion, $resource);
					}

					$region->assignResourceRegion($resourceRegion);

					// Store resource to prevent duplicates
					if (!isset($resourceRegions[$resource['osm_id']])) {
						$resourceRegions[$resource['osm_id']] = $resourceRegion;
					}
				}

				$this->em->persist($region);

				// Store region to prevent duplicates
				if (!isset($regions[$osmRegion['osm_id']])) {
					$regions[$osmRegion['osm_id']] = $region;
				}

				$progress->advance();
			}

			$this->em->flush();
			$this->em->clear();

			$offset += $limit;
		}

		$progress->finish();

		$output->writeln('');
		$output->writeln("{$newRegions} new regions created.");

		return 1;
	}

}
