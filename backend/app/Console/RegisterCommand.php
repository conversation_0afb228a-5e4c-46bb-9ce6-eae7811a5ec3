<?php declare(strict_types = 1);

namespace App\Console;

use App\Domain\Player\CreatePlayerFacade;
use App\Domain\User\CreateUserFacade;
use App\Model\Utils\Caster;
use App\Model\Utils\Validators;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: self::NAME)]
class RegisterCommand extends Command {

	public const NAME = 'kokume:register';

	public function __construct(
		private readonly CreateUserFacade $createUserFacade,
		private readonly CreatePlayerFacade $createPlayerFacade,
	) {
		parent::__construct();
	}

	protected function configure() : void {
		$this->setName(self::NAME);
		$this->addArgument('email', InputArgument::REQUIRED, 'User email');
		$this->addArgument('password', InputArgument::OPTIONAL, 'User password');
		$this->addArgument('nickname', InputArgument::OPTIONAL, 'Nickname');
		$this->setDescription('Register new user');
	}

	protected function execute(InputInterface $input, OutputInterface $output) : int {
		$email = Caster::toString($input->getArgument('email'));
		$password = Caster::toString($input->getArgument('password'));
		$nickname = Caster::toString($input->getArgument('nickname')) ?: $email;

		if (Validators::isEmail($email) === FALSE) {
			$output->writeln('Email is not valid.');
			return 1;
		}

		try {
			$user = $this->createUserFacade->createUser($email, $password);
			$this->createPlayerFacade->createPlayer($user, $nickname);

			$output->writeln('User ' . $user->getEmail() . ' #' . $user->getId() . ' has been registered.');

			return 0;
		} catch (\Throwable $e) {
			$output->writeln('Error: ' . $e->getMessage());
			return 1;
		}
	}

}
