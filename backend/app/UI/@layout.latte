<!DOCTYPE html>
<html lang="cs">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">

	<link rel="icon" type="image/x-icon" href="{$basePath}{webpack images/minified/logo.webp}">

	<title>{ifset title}{include title|stripHtml} | {/ifset}Kokume</title>

	{include head}
</head>

<body class="min-vh-100 d-flex flex-column">
	{block header}{/block}

	<div class="container flex-grow-1 p-0">
		<div n:foreach="$flashes as $flash" n:class="flash, $flash->type">{$flash->message}</div>

		{include content}
	</div>

	{block footer}{/block}

	{include scripts}
</body>
</html>
