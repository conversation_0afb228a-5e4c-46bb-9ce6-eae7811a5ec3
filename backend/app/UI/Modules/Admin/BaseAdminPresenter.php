<?php declare(strict_types = 1);

namespace App\UI\Modules\Admin;

use App\Domain\User\User;
use App\UI\Control\Breadcrumb\Breadcrumb;
use App\UI\Modules\BasePresenter;
use Nette\DI\Attributes\Inject;
use Nette\Security\User as SecurityUser;

abstract class BaseAdminPresenter extends BasePresenter {

	#[Inject]
	public SecurityUser $securityUser;

	protected function startup() : void {
		parent::startup();

		if ($this->isLinkCurrent('Sign:*') === FALSE) {
			if ($this->securityUser->isLoggedIn() === FALSE) {
				$this->redirect('Sign:in', ['backlink' => $this->storeRequest()]);
			}

			if ($this->securityUser->isInRole(User::ROLE_ADMIN) === FALSE) {
				$this->securityUser->logout(TRUE);
				$this->redirect('Sign:in', ['backlink' => $this->storeRequest()]);
			}
		}

		$this->template->breadcrumb = $this->createComponentBreadcrumb();
	}

	protected function createComponentBreadcrumb(): Breadcrumb
	{
		$breadcrumb = new Breadcrumb();
		$breadcrumb->addItem('Dashboard', $this->link('Dashboard:'));
		return $breadcrumb;
	}

	protected function addBreadcrumbItem(string $label, ?string $link = null): void
	{
		$this['breadcrumb']->addItem($label, $link);
	}
}
