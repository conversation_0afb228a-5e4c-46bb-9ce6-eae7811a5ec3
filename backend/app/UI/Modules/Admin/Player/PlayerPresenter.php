<?php
declare(strict_types = 1);

namespace App\UI\Modules\Admin\Player;

use App\Domain\Player\Player;
use App\Domain\Player\Repository\PlayerRepository;
use App\Model\Database\EntityManager;
use App\UI\DataGrid\DataGridFactory;
use App\UI\Modules\Admin\BaseAdminPresenter;
use Contributte\Datagrid\Column\Action\Confirmation\StringConfirmation;
use Contributte\Datagrid\Datagrid;
use Contributte\Datagrid\Exception\DatagridException;

final class PlayerPresenter extends BaseAdminPresenter {

	private PlayerRepository $playerRepository;

	private ?Player $player = null;

	public function __construct(
		private readonly EntityManager $em,
		private readonly DataGridFactory $dataGridFactory,
	) {
		parent::__construct();
		$this->playerRepository = $em->getRepository(Player::class);
	}

	public function actionDetail(int $id): void {
		$this->player = $this->playerRepository->find($id);
		if ($this->player === NULL) {
			$this->redirect('list');
		}

		$this->addBreadcrumbItem('Detail hráče');
	}

	public function renderDetail() : void {
		$this->template->player = $this->player;
	}

	public function handleDelete(int $id): void {
		if ($this->playerRepository->delete($id)) {
			$this->flashMessage('Hráč byl smazán', 'success');
		} else {
			$this->flashMessage('Něco se nepovedlo :(', 'danger');
		}

		if ($this->isAjax()) {
			$this['grid']->reload();
			$this->redrawControl('flashes');
		} else {
			$this->redirect('this');
		}
	}

	public function handleBan(int $id): void {
		$player = $this->playerRepository->find($id);

		if ($player === NULL) {
			$this->flashMessage('Hráč nebyl nalezen', 'danger');
			$this->redirect('this');
		}

		$user = $player->getUser();
		$user->setBannedTo(new \DateTime('+1 day'));
		$this->em->flush();

		$this->flashMessage('Hráč byl zabanován na 1 den', 'success');
		$this->redirect('this');
	}

	public function handleUnban(int $id): void {
		$player = $this->playerRepository->find($id);
		if ($player === NULL) {
			$this->flashMessage('Hráč nebyl nalezen', 'danger');
			$this->redirect('this');
		}

		$user = $player->getUser();
		$user->setBannedTo(NULL);
		$this->em->flush();

		$this->flashMessage('Hráč byl odbanován', 'success');
		$this->redirect('this');
	}

	public function renderList(): void
	{
		$this->addBreadcrumbItem('Hráči', $this->link('list'));
	}

	/**
	 * @throws DatagridException
	 */
	public function createComponentGrid(): Datagrid {
		$grid = $this->dataGridFactory->create();
		$grid->setDataSource($this->playerRepository->createQueryBuilder('p'));

		$grid->addColumnText('nickname', 'Přezdívka')
			 ->setSortable()
			 ->setFilterText();

		$grid->addColumnText('email', 'E-mail', 'user.email')
			->setSortable()
			->setFilterText();

		$grid->addColumnDateTime('lastLoggedAt', 'Poslední přihlášení', 'user.lastLoggedAt')
			 ->setFormat('d.m.Y H:i')
			 ->setSortable()
			 ->setRenderer(
				 function(Player $player) {
					 return $player->getUser()->getLastLoggedAt()?->format('d.m.Y H:i') ?? 'Nikdy';
				 }
			 );

		$grid->addColumnDateTime('createdAt', 'Registrace', 'user.createdAt')
			->setFormat('d.m.Y H:i')
			->setSortable();

		$grid->addAction('detail', 'Detail')
			->setIcon('pencil')
			->setClass('btn btn-xs btn-primary');

		$grid->addAction('unban', 'Odbanovat', 'unban!')
			 ->setClass('btn btn-xs btn-success');

		$grid->allowRowsAction('unban', function ($id) {
			$player = $this->playerRepository->find($id);
			return $player !== NULL && $player->getUser()->isBanned();
		});

		$grid->addAction('ban', 'Ban 1d', 'ban!')
			 ->setConfirmation(new StringConfirmation('Opravdu zabanovat hráče na 1den?'))
			 ->setClass('btn btn-xs btn-warning');

		$grid->allowRowsAction('ban', function ($id) {
			$player = $this->playerRepository->find($id);
			return $player !== NULL && !$player->getUser()->isBanned();
		});

		$grid->addAction('delete', 'Smazat', 'delete!')
			->setConfirmation(new StringConfirmation('Opravdu smazat hráče? Tato akce je nevratná!'))
			->setIcon('trash')
			->setClass('btn btn-xs btn-danger');

		return $grid;
	}
}
