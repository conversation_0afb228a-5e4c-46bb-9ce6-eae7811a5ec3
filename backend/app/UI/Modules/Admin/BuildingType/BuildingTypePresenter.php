<?php
declare(strict_types = 1);

namespace App\UI\Modules\Admin\BuildingType;

use App\Domain\Building\BuildingType;
use App\Domain\Building\BuildingTypeRepository;
use App\Domain\Resource\ResourceType;
use App\Model\Database\EntityManager;
use Nette\Utils\Strings;
use App\UI\DataGrid\BaseDataGrid;
use App\UI\DataGrid\DataGridFactory;
use App\UI\Form\BaseForm;
use App\UI\Form\FormFactory;
use App\UI\Modules\Admin\BaseAdminPresenter;
use Contributte\Datagrid\Column\Action\Confirmation\StringConfirmation;

class BuildingTypePresenter extends BaseAdminPresenter {
	private BuildingTypeRepository $buildingTypeRepository;

	private ?BuildingType $buildingType = NULL;

	public function __construct(
		private readonly EntityManager 		$em,
		private readonly DataGridFactory 	$dataGridFactory,
		private readonly FormFactory 		$formFactory,
	) {
		$this->buildingTypeRepository = $em->getRepository(BuildingType::class);
		parent::__construct();
	}

	public function actionEdit(?int $id = NULL) : void {
		if ($id !== NULL) {
			$this->buildingType = $this->buildingTypeRepository->find($id);
			if ($this->buildingType === NULL) {
				$this->redirect('list');
			}
		}

		$this->addBreadcrumbItem('Editace typu budovy');
	}

	public function actionAdd() : void {
		$this->setView('edit');
		$this->addBreadcrumbItem('Přidání typu budovy');
	}

	public function handleDelete(int $id) : void {
		if ($this->buildingTypeRepository->delete($id)) {
			$this->flashMessage('Typ budovy byl smazán', 'success');
		} else {
			$this->flashMessage('Něco se nepovedlo :(', 'danger');
		}

		$this->redirect('list');
	}

	public function renderList(): void
	{
		$this->addBreadcrumbItem('Typy budov', $this->link('list'));
	}

	protected function createComponentGrid() : BaseDataGrid {
		$grid = $this->dataGridFactory->create();

		$grid->setDataSource($this->buildingTypeRepository->createQueryBuilder('b'));

		$grid->addColumnText('name', 'Název')
			 ->setSortable();

		$grid->addColumnText('slug', 'Slug')
			 ->setSortable();

		$grid->addColumnText('description', 'Popis')
			 ->setRenderer(function (BuildingType $buildingType) {
				 return Strings::truncate($buildingType->getDescription(), 50);
			 });

		$grid->addColumnText('buildTime', 'Čas stavby (min)')
			 ->setSortable();

		$grid->addColumnText('demolishTime', 'Čas demolice (min)')
			 ->setSortable();

		$grid->addColumnText('range', 'Dosah (m)')
			 ->setSortable();

		$grid->addAction('edit', 'Upravit', 'edit', ['id' => 'id'])
			 ->setIcon('pencil')
			 ->setClass('btn btn-xs btn-primary');

		$grid->addAction('delete', 'Smazat', 'delete!', ['id' => 'id'])
			->setClass('btn btn-xs btn-danger')
			->setIcon('trash')
			->setConfirmation(new StringConfirmation('Opravdu chcete smazat tento typ budovy?'));

		return $grid;
	}

	protected function createComponentForm() : BaseForm {
		$form = $this->formFactory->createAdmin();

		$form->addGroup('Základní informace');

		$form->addText('slug', 'Slug')
			->setDisabled($this->buildingType !== NULL)
			->setRequired();

		$form->addText('name', 'Název')
			 ->setRequired();

		$form->addTextArea('description', 'Popis')
			 ->setRequired();

		$form->addInteger('buildTime', 'Čas stavby (minuty)')
			 ->setDefaultValue(30)
			 ->setRequired();

		$form->addInteger('demolishTime', 'Čas demolice (minuty)')
			 ->setDefaultValue(5)
			 ->setRequired();

		$form->addInteger('range', 'Dosah (m)')
			 ->setDefaultValue(50)
			 ->setRequired();

		$form->addCheckbox('uniqueInWorld', 'Jedinečná ve světě');

		$form->addCheckbox('uniqueInRegion', 'Jedinečná v regionu');

		$form->addInteger('attack', 'Útok')
			 ->setDefaultValue(0)
			 ->setRequired();

		$form->addInteger('defense', 'Obrana')
			 ->setDefaultValue(0)
			 ->setRequired();

		$form->addGroup('Náklady na stavbu');
		$buildCostContainer = $form->addContainer('buildCost');
		foreach (ResourceType::getAll() as $resourceType) {
			$buildCostContainer->addInteger($resourceType->value, $resourceType->getName())
							   ->setHtmlAttribute('placeholder', '---')
							   ->setNullable();
		}

		$form->addGroup('Získané suroviny při demolici');
		$demolishGainContainer = $form->addContainer('demolishGain');
		foreach (ResourceType::getAll() as $resourceType) {
			$demolishGainContainer->addInteger($resourceType->value, $resourceType->getName())
				->setHtmlAttribute('placeholder', '---')
				->setNullable();
		}

		$form->addGroup('Produkce');
		$productionsContainer = $form->addContainer('productions');
		foreach (ResourceType::getAll() as $resourceType) {
			$resourceContainer = $productionsContainer->addContainer($resourceType->value);
			foreach (range(1, BuildingType::getMaxLevel()) as $level) {
				$resourceContainer->addInteger((string)$level, $resourceType->getName() . '- lvl ' . $level)
								  ->setHtmlAttribute('placeholder', '0')
								  ->setNullable();
			}
		}

		$form->addGroup();

		$form->addSubmit('save', 'Uložit');

		if ($this->buildingType !== NULL) {
			$form->setDefaults([
				'slug' => $this->buildingType->getSlug(),
				'name' => $this->buildingType->getName(),
				'description' => $this->buildingType->getDescription(),
				'buildTime' => $this->buildingType->getBuildTime(),
				'demolishTime' => $this->buildingType->getDemolishTime(),
				'range' => $this->buildingType->getRange(),
				'uniqueInWorld' => $this->buildingType->isUniqueInWorld(),
				'uniqueInRegion' => $this->buildingType->isUniqueInRegion(),
				'attack' => $this->buildingType->getAttack(),
				'defense' => $this->buildingType->getDefense(),
			]);

			$buildCost = [];
			foreach ($this->buildingType->getBuildCost() as ['type' => $resourceType, 'amount' => $amount]) {
				$buildCost[$resourceType->value] = $amount;
			}
			/** @phpstan-ignore-next-line */
			$form['buildCost']->setDefaults($buildCost);

			$demolishGain = [];
			foreach ($this->buildingType->getDemolishGain() as ['type' => $resourceType, 'amount' => $amount]) {
				$demolishGain[$resourceType->value] = $amount;
			}
			/** @phpstan-ignore-next-line */
			$form['demolishGain']->setDefaults($demolishGain);

			foreach ($this->buildingType->getProductions() as ['type' => $resourceType, 'levels' => $levels]) {
				$form['productions'][$resourceType->value]->setDefaults($levels);
			}
		}

		$form->onSuccess[] = function (BaseForm $formData, \stdClass $values) {
			if ($this->buildingType === NULL) {
				$this->buildingType = new BuildingType($values->slug, $values->name, $values->description);
			} else {
				$this->buildingType->setName($values->name);
				$this->buildingType->setDescription($values->description);
			}

			$this->buildingType->setBuildTime($values->buildTime);
			$this->buildingType->setDemolishTime($values->demolishTime);

			$this->buildingType->setRange($values->range);

			$this->buildingType->setUniqueInWorld($values->uniqueInWorld);
			$this->buildingType->setUniqueInRegion($values->uniqueInRegion);

			$this->buildingType->setAttack($values->attack);
			$this->buildingType->setDefense($values->defense);

			$this->buildingType->clearBuildCost();
			foreach ($values->buildCost as $resourceType => $amount) {
				$this->buildingType->setBuildCost(ResourceType::from($resourceType), $amount);
			}

			$this->buildingType->clearDemolishGain();
			foreach ($values->demolishGain as $resourceType => $amount) {
				$this->buildingType->setDemolishGain(ResourceType::from($resourceType), $amount);
			}

			$this->buildingType->clearProductions();
			foreach ($values->productions as $resourceType => $levels) {
				$this->buildingType->addProduction(ResourceType::from($resourceType), $levels);
			}

			$this->em->persist($this->buildingType);
			$this->em->flush();

			$this->flashMessage('Typ budovy byl uložen', 'success');
			$this->redirect('list');
		};

		return $form;
	}

}
