<?php declare(strict_types = 1);

namespace App\UI\Modules\Admin\Sign;

use App\Domain\User\User;
use App\UI\Form\BaseForm;
use App\UI\Form\FormFactory;
use App\UI\Modules\Admin\BaseAdminPresenter;
use Nette\DI\Attributes\Inject;
use Nette\Security\AuthenticationException;

class SignPresenter extends BaseAdminPresenter {

	#[Inject]
	public FormFactory $formFactory;

	public function actionIn() : void {
		if ($this->securityUser->isLoggedIn()) {
			$this->redirect('Dashboard:');
		}
	}

	public function actionOut() : void {
		$this->securityUser->logout();
		$this->redirect('in');
	}

	protected function createComponentSignInForm() : BaseForm {
		$form = $this->formFactory->createAdmin();

		$form->addText('email', 'E-mail')
			->setRequired();

		$form->addPassword('password', 'Heslo')
			->setRequired();

		$form->addText('antiSpam', 'Nice!')
			->setRequired();

		$form->addCheckbox('remember', 'Zapamatovat si mě')
			 ->setDefaultValue(TRUE);

		$form->addHidden('backlink', 'Backlink')
			->setDefaultValue($this->getParameter('backlink'));

		$form->addSubmit('submit', 'Přihlásit se');

		$form->onValidate[] = function (BaseForm $form) : void {
			/** @var array{antiSpam:string} $values */
			$values = $form->getValues('array');

			// Anti spam
			if ($values['antiSpam'] !== '69') {
				$form->addError('Nice try!');
			}
		};

		$form->onSuccess[] = function (BaseForm $form) : void {
			/** @var array{email:string, password:string, remember:bool, backlink:string} $values */
			$values = $form->getValues('array');

			try {
				$this->securityUser->login($values['email'], $values['password']);

				if ($values['remember']) {
					$this->securityUser->setExpiration('14 days');
				} else {
					$this->securityUser->setExpiration('20 minutes');
				}

				// Check if user is admin
				if ($this->securityUser->isInRole(User::ROLE_ADMIN) === FALSE) {
					$this->securityUser->logout(TRUE);
					throw new AuthenticationException();
				}

				if ($values['backlink'] !== '') {
					$this->restoreRequest($values['backlink']);
				}
				$this->redirect('Dashboard:');
			} catch (AuthenticationException) {
				$form->addError('Neplatné přihlašovací údaje.');
			}
		};

		return $form;
	}

}