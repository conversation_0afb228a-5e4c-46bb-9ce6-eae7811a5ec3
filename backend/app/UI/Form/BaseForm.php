<?php declare(strict_types = 1);

namespace App\UI\Form;

use Contributte\FormsBootstrap\BootstrapForm;
use Nette\Forms\Controls\TextInput;

class BaseForm extends BootstrapForm {

	public function addNumeric(string $name, ?string $label = NULL) : TextInput {
		$input = $this->addText($name, $label);
		$input->addCondition($this::Filled)
			  ->addRule($this::MaxLength, NULL, 255)
			  ->addRule($this::Numeric);

		return $input;
	}

}
