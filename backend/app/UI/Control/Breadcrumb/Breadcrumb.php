<?php

namespace App\UI\Control\Breadcrumb;

use Nette\Application\UI\Control;

class Breadcrumb extends Control
{
    /** @var array<string, string> */
    private array $items = [];

    public function addItem(string $label, ?string $link = null): void
    {
        $this->items[] = ['label' => $label, 'link' => $link];
    }

    public function render(): void
    {
        $this->template->items = $this->items;
        $this->template->setFile(__DIR__ . '/Breadcrumb.latte');
        $this->template->render();
    }
}
