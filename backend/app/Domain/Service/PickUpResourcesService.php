<?php declare(strict_types = 1);

namespace App\Domain\Service;

use App\Domain\Api\Response\BuildingResDto;
use App\Domain\Building\Building;
use App\Domain\Building\BuildingQuery;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Resource\ResourceType;
use App\Domain\Settings\Settings;
use App\Model\Database\EntityManager;
use App\Model\Database\QueryManager;
use App\Model\Exception\Runtime\NotFoundException;

class PickUpResourcesService {

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly QueryManager 			$qm,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {}

	public function pickUpResources(Player $player, string $buildingId) : Building {
		/** @var Building|null $building */
		$building = $this->qm->findOne(BuildingQuery::ofPlayer($player)->withId($buildingId));

		if ($building === null) {
			throw new NotFoundException("Building not found");
		}

		// Check if player is in range of the building
		$this->playerPositionProvider->validateRange($player, $building->getPosition(), $player->getRange());

		try {
			$this->em->beginTransaction();

			foreach ($building->getStock() as $typeSlug => $amount) {
				$type = ResourceType::from($typeSlug);
				$player->increaseResource($type, $amount);
				$building->decreaseResourceStock($type, $amount);
			}

			$this->em->flush();
			$this->em->commit();
		} catch (\Throwable $e) {
			$this->em->rollback();

			throw new \RuntimeException('Failed to pick up resources', 0, $e);
		}

		return $building;
	}

}