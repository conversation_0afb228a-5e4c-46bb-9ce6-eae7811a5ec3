<?php declare(strict_types = 1);

namespace App\Domain\Combat;

use Doctrine\ORM\Mapping as ORM;

trait THealthStats {

	#[ORM\Column(type: 'integer', options: ['default' => 100])]
	private int $health = 100;

	public function getHealth() : int {
		return $this->health;
	}

	public function setHealth(int $health) : void {
		$this->health = min(max($health, 0), $this->getMaxHealth());
	}

	public function increaseHealth(int $change) : void {
		$this->setHealth($this->health + $change);
	}

	public function decreaseHealth(int $change) : void {
		$this->setHealth($this->health - $change);
	}

	abstract public function getMaxHealth() : int;

}
