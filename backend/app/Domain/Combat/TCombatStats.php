<?php declare(strict_types = 1);

namespace App\Domain\Combat;

use Doctrine\ORM\Mapping as ORM;

trait TCombatStats {

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $defense = 1;

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $strength = 1;

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $agility = 1;

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $luck = 1;

	public function getDefence() : int {
		return $this->defense;
	}

	public function setDefense(int $defense) : void {
		$this->defense = $defense;
	}

	public function getStrength() : int {
		return $this->strength;
	}

	public function setStrength(int $strength) : void {
		$this->strength = $strength;
	}

	public function getAgility() : int {
		return $this->agility;
	}

	public function setAgility(int $agility) : void {
		$this->agility = $agility;
	}

	public function getLuck() : int {
		return $this->luck;
	}

	public function setLuck(int $luck) : void {
		$this->luck = $luck;
	}

}
