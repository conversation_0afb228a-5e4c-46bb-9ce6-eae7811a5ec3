<?php declare(strict_types = 1);

namespace App\Domain\Combat;

use Doctrine\ORM\Mapping as ORM;

trait TCombatStatsWithBonus {

	use TCombatStats;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $defenseBonus = 0;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $strengthBonus = 0;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $agilityBonus = 0;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $luckBonus = 0;

	public function getDefenseBase() : int {
		return $this->defense;
	}

	public function getDefenseBonus() : int {
		return $this->defenseBonus;
	}

	public function getDefence() : int {
		return $this->defense + $this->defenseBonus;
	}

	public function setDefenseBase(int $defense) : void {
		$this->defense = $defense;
	}

	public function setDefenseBonus(int $defense) : void {
		$this->defenseBonus = $defense;
	}

	public function getStrengthBase() : int {
		return $this->strength;
	}

	public function getStrengthBonus() : int {
		return $this->strengthBonus;
	}

	public function getStrength() : int {
		return $this->strength + $this->strengthBonus;
	}

	public function setStrengthBase(int $strength) : void {
		$this->strength = $strength;
	}

	public function setStrengthBonus(int $strength) : void {
		$this->strengthBonus = $strength;
	}

	public function getAgilityBase() : int {
		return $this->agility;
	}

	public function getAgilityBonus() : int {
		return $this->agilityBonus;
	}

	public function getAgility() : int {
		return $this->agility + $this->agilityBonus;
	}

	public function setAgilityBase(int $agility) : void {
		$this->agility = $agility;
	}

	public function setAgilityBonus(int $agility) : void {
		$this->agilityBonus = $agility;
	}

	public function getLuck() : int {
		return $this->luck + $this->luckBonus;
	}

	public function getLuckBase() : int {
		return $this->luck;
	}

	public function setLuckBase(int $luck) : void {
		$this->luck = $luck;
	}

	public function getLuckBonus() : int {
		return $this->luckBonus;
	}

	public function setLuckBonus(int $luck) : void {
		$this->luckBonus = $luck;
	}

}
