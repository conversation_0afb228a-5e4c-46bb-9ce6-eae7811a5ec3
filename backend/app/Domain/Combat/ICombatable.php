<?php declare(strict_types = 1);

namespace App\Domain\Combat;

interface ICombatable {

	public function getId() : mixed;

	public function getName() : string;

	public function getDefence() : int;

	public function getStrength() : int;

	public function getHealth() : int;

	public function getAgility() : int;

	public function getLuck() : int;

	public function increaseHealth(int $change) : void;

	public function decreaseHealth(int $change) : void;

}