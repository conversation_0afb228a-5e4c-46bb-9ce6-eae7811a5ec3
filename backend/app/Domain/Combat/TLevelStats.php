<?php declare(strict_types = 1);

namespace App\Domain\Combat;

use Doctrine\ORM\Mapping as ORM;

trait TLevelStats {

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $level = 1;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $experiences = 0;

	public function getLevel() : int {
		return $this->level;
	}

	public function setLevel(int $level) : void {
		$this->level = $level;
	}

	public function getExperiences() : int {
		return $this->experiences;
	}

	public function setExperiences(int $experiences) : void {
		$this->experiences = $experiences;
	}

	public function getExperiencesNextLevel() : int {
		return $this->level * $this->getExperienceBase() * 2;
	}

	public function getExperienceBase() : int {
		return 1000;
	}

}
