<?php declare(strict_types = 1);

namespace App\Domain\Combat;

use App\Domain\Hero\Hero;

class CombatService {

	public function attack(ICombatable $attacker, ICombatable $defender, CombatParams $params) : int {
		$hitChance = $this->calculateHitChance($attacker, $defender);

		if (rand(0, 100) / 100 > $hitChance) {
			return 0;
		}

		$damage = max(0, $attacker->getStrength() - $defender->getDefence());

		$criticalHitChance = $this->calculateCriticalHitChance($attacker);

		if (rand(0, 100) / 100 < $criticalHitChance) {
			$damage *= 2;
		}

		return $damage;
	}

	public function applyDamage(ICombatable $defender, int $damage) : void {
		$defender->decreaseHealth($damage);
	}

	public function isAlive(ICombatable $entity) : bool {
		if ($entity instanceof Hero) {
			return $entity->getHealth() > 1;
		}

		return $entity->getHealth() > 0;
	}

	public function battle(ICombatable $attacker, ICombatable $defender, CombatParams $params) : CombatResult {
		$entity1 = $attacker;
		$entity2 = $defender;
		$log = [];

		while ($this->isAlive($attacker) && $this->isAlive($defender)) {
			$damage = $this->attack($entity1, $entity2, $params);
			$this->applyDamage($entity2, $damage);

			$log[] = new CombatLog(
				$entity1,
				$entity2,
				$damage,
				$entity2->getHealth(),
			);

			if (!$this->isAlive($entity2)) {
				break;
			}

			// Switch roles
			$temp = $entity1;
			$entity1 = $entity2;
			$entity2 = $temp;
		}

		return new CombatResult($attacker, $entity1, $entity2, $log);
	}

	private function calculateHitChance(ICombatable $attacker, ICombatable $defender) : float {
		$agility = $attacker->getAgility();
		$luck = $defender->getLuck();

		$hitChance = $agility / ($agility + $luck);

		return min(1, max(0, $hitChance));
	}

	private function calculateCriticalHitChance(ICombatable $attacker) : float {
		$strength = $attacker->getStrength();

		$criticalHitChance = $strength / 100;

		return min(0.5, max(0, $criticalHitChance));
	}

}