<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Domain\Resource\ResourceType;

enum ResourceRegionType : string {
	case forest 	= 'forest';
	case field 		= 'field';
	case meadow 	= 'meadow';
	case quarry 	= 'quarry';
	case mine 		= 'mine';

	case water 		= 'water';
	case reservoir 	= 'reservoir';
	case pond 		= 'pond';
	case river 		= 'river';

	private const NAMES = [
		self::forest->value 	=> 'Les',
		self::field->value 		=> 'Pole',
		self::meadow->value 	=> 'Louka',
		self::quarry->value 	=> 'Lom',
		self::mine->value 		=> 'Důl',
		self::water->value 		=> 'Voda',
		self::reservoir->value 	=> 'Vodní nádrž',
		self::pond->value 		=> 'Rybník',
		self::river->value 		=> 'Řeka',
	];

	/**
	 * allotments
	 * basin
	 * brownfield
	 * cemetery
	 * commercial
	 * construction
	 * farmland
	 * farmyard
	 * forest
	 * garages
	 * grass
	 * greenfield
	 * industrial
	 * landfill
	 * logging
	 * meadow
	 * military
	 * orchard
	 * plant_nursery
	 * quarry
	 * railway
	 * recreation_ground
	 * religious
	 * reservoir
	 * residential
	 * sand
	 * service
	 * village_green
	 * vineyard
	 * winter_sports
 */
	public static function mapFromLanduse(string $landuse, ?string $subtype) : ?self {
		return match ($landuse) {
			'forest', 'scrub', 'wood'
			=> self::forest,

			'farmland', 'grassland'
			=> self::field,

			'meadow', 'grass', 'wetland', 'scree'
			=> self::meadow,

			'quarry', 'rock', 'bare_rock'
			=> self::quarry,

			'mine'
			=> self::mine,

			'water', 'pond'
			=> match ($subtype) {
				'reservoir' => self::reservoir,
				'pond' => self::pond,
				'river' => self::river,
				default => self::water,
			},

			default => NULL,
		};
	}

	public function getName() : string {
		return self::NAMES[$this->value];
	}

	public function getResources() : array {
		return match ($this) {
			self::forest => [
				ResourceType::WOOD->value => 0.8,
				ResourceType::FOOD->value => 0.2,
			],
			self::field => [
				ResourceType::FOOD->value => 1.0,
			],
			self::quarry => [
				ResourceType::STONE->value => 8.0,
				ResourceType::CLAY->value => 0.2,
			],
			self::mine => [
				ResourceType::IRON->value => 0.3,
				ResourceType::STONE->value => 0.6,
				ResourceType::CLAY->value => 0.1,
			],
			self::reservoir => [
				ResourceType::FOOD->value => 0.7,
				ResourceType::STONE->value => 0.2,
				ResourceType::CLAY->value => 0.1,
			],
			self::water => [
				ResourceType::FOOD->value => 0.2,
			],
			self::pond => [
				ResourceType::FOOD->value => 0.7,
			],
			self::river => [
				ResourceType::FOOD->value => 0.6,
				ResourceType::STONE->value => 0.2,
				ResourceType::CLAY->value => 0.2,
			],
			self::meadow => [
				ResourceType::FOOD->value => 0.4,
				ResourceType::WOOD->value => 0.1,
			],
		};
	}

}
