<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Domain\Player\Player;
use App\Domain\Region\Repository\RegionRepository;
use App\Model\Utils\Position;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: RegionRepository::class)]
class Region extends AbstractRegion {

	#[ORM\ManyToOne(targetEntity: Player::class)]
	#[ORM\JoinColumn(name: 'owner_id', referencedColumnName: 'id', nullable: true, onDelete: 'SET NULL')]
	protected ?Player $owner;

	/**
	 * @var Collection<int, ResourceRegion>
	 */
	#[ORM\ManyToMany(targetEntity: ResourceRegion::class, cascade: ['persist'])]
	#[ORM\JoinTable(name: 'game_region_has_resource_region')]
	#[ORM\JoinColumn(name: 'region_id', referencedColumnName: 'id')]
	#[ORM\InverseJoinColumn(name: 'resource_region_id', referencedColumnName: 'id')]
	private Collection $resourceRegions;

	public function __construct(string $name, int $osmId, RegionLevel $level, Position $position) {
		parent::__construct($osmId, $level, $position);
		$this->name = $name;

		$this->resourceRegions = new ArrayCollection();
	}

	public function getOwner() : ?Player {
		return $this->owner;
	}

	public function setOwner(?Player $owner) : void {
		$this->owner = $owner;
	}

	/**
	 * @return Collection<int, ResourceRegion>
	 */
	public function getResourceRegions() : Collection {
		return $this->resourceRegions;
	}

	public function assignResourceRegion(ResourceRegion $resourceRegion) : void {
		if ($this->resourceRegions->contains($resourceRegion)) {
			return;
		}

		$this->resourceRegions->add($resourceRegion);
	}

	public function discardResourceRegions(ResourceRegion $resourceRegion) : bool {
		return $this->resourceRegions->removeElement($resourceRegion);
	}

}