<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TPosition;
use App\Model\Database\Entity\TUpdatedAt;
use App\Model\Utils\Position;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_region')]
#[ORM\InheritanceType('SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'type', type: 'string', enumType: RegionType::class)]
#[ORM\DiscriminatorMap([
	RegionType::REGION->value 	=> Region::class,
	RegionType::RESOURCE->value	=> ResourceRegion::class,
])]
#[ORM\HasLifecycleCallbacks]
abstract class AbstractRegion extends AbstractEntity {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;
	use TPosition;
	#[ORM\Column(type: 'integer', unique: true, nullable: false)]
	protected int $osmId;

	#[ORM\Column(type: 'string', nullable: true)]
	protected ?string $name = NULL;

	#[ORM\Column(type: 'string', nullable: false, enumType: RegionLevel::class)]
	protected RegionLevel $level;

	public function __construct(int $osmId, RegionLevel $level, Position $position) {
		$this->osmId = $osmId;
		$this->level = $level;
		$this->setPosition($position);
	}

	public function getOsmId() : int {
		return $this->osmId;
	}

	public function getName() : ?string {
		return $this->name;
	}

	public function setName(?string $name) : void {
		$this->name = $name;
	}

	public function getLevel() : RegionLevel {
		return $this->level;
	}

}