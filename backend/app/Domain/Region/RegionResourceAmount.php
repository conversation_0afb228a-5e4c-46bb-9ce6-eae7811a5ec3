<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Domain\Resource\ResourceType;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_region_production')]
#[ORM\UniqueConstraint(name: 'region_resource_type_unique', fields: ['region', 'resourceType'])]
class RegionResourceAmount extends AbstractEntity {

	use TId;
	use TUpdatedAt;

	#[ORM\ManyToOne(targetEntity: ResourceRegion::class, inversedBy: 'resourceAmounts')]
	#[ORM\JoinColumn(name: 'region_id', referencedColumnName: 'id', nullable: false)]
	private ResourceRegion $region;

	#[ORM\Column(type: 'string', nullable: false, enumType: ResourceType::class)]
	private ResourceType $resourceType;

	#[ORM\Column(type: 'integer', nullable: false)]
	private int $amount;

	public function __construct(ResourceRegion $region, ResourceType $resourceType, int $amount) {
		$this->region = $region;
		$this->resourceType = $resourceType;
		$this->amount = $amount;
	}

	public function getRegion() : ResourceRegion {
		return $this->region;
	}

	public function getResourceType() : ResourceType {
		return $this->resourceType;
	}

	public function getAmount() : int {
		return $this->amount;
	}

	public function setAmount(int $amount) : void {
		$this->amount = $amount;
	}

	public function increaseAmount(int $amount) : void {
		$this->amount += $amount;
	}

	public function decreaseAmount(int $amount) : void {
		if ($this->amount < $amount) {
			throw new \InvalidArgumentException('Cannot decrease amount below zero');
		}

		$this->amount -= $amount;
	}
}