<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Domain\Region\Repository\ResourceRegionRepository;
use App\Domain\Resource\ResourceType;
use App\Model\Utils\Position;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use InvalidArgumentException;

#[ORM\Entity(repositoryClass: ResourceRegionRepository::class)]
class ResourceRegion extends AbstractRegion {

	#[ORM\Column(type: 'string', nullable: true, enumType: ResourceRegionType::class)]
	private ResourceRegionType $resourceRegionType;

	/**
	 * @var Collection<string, RegionResourceAmount>
	 */
	#[ORM\OneToMany(mappedBy: 'region', targetEntity: RegionResourceAmount::class, cascade: ['persist', 'remove'])]
	protected Collection $resourceAmounts;

	public function __construct(int $osmId, RegionLevel $level, Position $position, ResourceRegionType $resourceRegionType) {
		parent::__construct($osmId, $level, $position);
		$this->resourceRegionType = $resourceRegionType;
		$this->resourceAmounts = new ArrayCollection();
	}

	public function getResourceRegionType() : ResourceRegionType {
		return $this->resourceRegionType;
	}

	/**
	 * @return Collection<string, RegionResourceAmount>
	 */
	public function getResourceAmounts() : Collection {
		return $this->resourceAmounts;
	}

	/**
	 * @return ResourceType[]
	 */
	public function getResourceTypes() : array {
		return $this->resourceAmounts->map(fn(RegionResourceAmount $resourceAmount) => $resourceAmount->getResourceType())->toArray();
	}

	public function hasResourceAmount(ResourceType $type) : bool {
		return (bool)$this->resourceAmounts
			->filter(fn(RegionResourceAmount $resourceAmount) => $resourceAmount->getResourceType() === $type)
			->first();
	}

	public function getResourceAmount(ResourceType $type) : RegionResourceAmount {
		$result = $this->resourceAmounts
			->filter(fn(RegionResourceAmount $resourceAmount) => $resourceAmount->getResourceType() === $type)
			->first();
		return $result !== false ? $result : throw new InvalidArgumentException("Resource type {$type->value} not found.");
	}

	public function setResourceAmount(ResourceType $type, int $amount) : void {
		try {
			$this->getResourceAmount($type)->setAmount($amount);
		} catch (InvalidArgumentException) {
			$this->resourceAmounts->add(new RegionResourceAmount($this, $type, $amount));
		}
	}

	public function increaseResourceAmount(ResourceType $type, int $amount) : void {
		$this->resourceAmounts->get($type->value)->increaseAmount($amount);
	}

	public function decreaseResourceAmount(ResourceType $type, int $amount) : void {
		$this->resourceAmounts->get($type->value)->decreaseAmount($amount);
	}

	public function removeResourceAmount(RegionResourceAmount $resourceAmount) : bool {
		return $this->resourceAmounts->removeElement($resourceAmount);
	}

}