<?php declare(strict_types = 1);

namespace App\Domain\Region\Repository;

use App\Domain\Region\Region;
use App\Domain\Region\RegionLevel;
use App\Model\Utils\Position;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\ResultSetMappingBuilder;

/**
 * @method Region|NULL find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method Region|NULL findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL)
 * @method Region[] findAll()
 * @method Region[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 *
 * @extends EntityRepository<Region>
 */
class RegionRepository extends EntityRepository {

	public function findBaseRegionByPosition(Position $position) : ?Region {
		$rms = new ResultSetMappingBuilder($this->_em);
		$rms->addRootEntityFromClassMetadata(Region::class, 'gr');

		return $this
			->_em
			->createNativeQuery(
				'
					SELECT gr.*, ST_AsEWKT(gr.position) AS position
					FROM game_region gr
					JOIN osm_polygon op ON gr.osm_id = op.osm_id
					WHERE ST_Contains(op.way, ST_SetSRID(ST_Point(:lng, :lat), 4326))
					AND gr.level = :regionLevel
					LIMIT 1',
				$rms,
			)
			->setParameter('lng', $position->getLng())
			->setParameter('lat', $position->getLat())
			->setParameter('regionLevel', RegionLevel::lvl5)
			->getOneOrNullResult();
	}

	public function getRandomPositionInRegion(Region $region) : Position {
		/** @var array{lng:string, lat:string} $pos */
		$pos = $this
			->_em
			->getConnection()
			->executeQuery(
				'
					SELECT 
						ST_Y(points::geometry) AS lat, 
						ST_X(points::geometry) AS lng
					FROM (
						SELECT 
							 (ST_Dump(ST_GeneratePoints(op.way, 1))).geom AS points
						FROM 
							game_region r
						JOIN 
							osm_polygon op ON r.osm_id = op.osm_id
						WHERE 
							r.id = :regionId
					) AS generated_points;
				',
				[
					'regionId' => $region->getId()
				])
			->fetchAssociative();

		return Position::fromArray($pos);
	}

}