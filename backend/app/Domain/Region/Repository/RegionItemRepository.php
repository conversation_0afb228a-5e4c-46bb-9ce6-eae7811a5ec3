<?php declare(strict_types = 1);

namespace App\Domain\Region\Repository;

use App\Domain\Item\ItemPlaced;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method ItemPlaced|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method ItemPlaced|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItemPlaced[] findAll()
 * @method ItemPlaced[] findBy(array $criteria, array $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class RegionItemRepository extends AbstractRepository {

}