<?php declare(strict_types = 1);

namespace App\Domain\Region\Repository;

use App\Domain\Item\ItemPlaced;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method ItemPlaced|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method ItemPlaced|null findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = null)
 * @method ItemPlaced[] findAll()
 * @method ItemPlaced[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = null, ?int $limit = null, ?int $offset = null)
 * @extends AbstractRepository<ItemPlaced>
 */
class RegionItemRepository extends AbstractRepository {

}