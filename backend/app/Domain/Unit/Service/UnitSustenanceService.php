<?php declare(strict_types = 1);

namespace App\Domain\Unit\Service;

use App\Domain\Resource\ResourceType;
use App\Domain\Unit\Unit;
use App\Model\Database\EntityManager;

class UnitSustenanceService {

	public function __construct(
		private readonly EntityManager $entityManager,
	) {}

	public function processSustenance() : void {
		$units = $this->entityManager->getRepository(Unit::class)->findAll();

		/** @var Unit $unit */
		foreach ($units as $unit) {
			$foodConsumption = $unit->getUnitDefinition()->getFoodCost();

			$player = $unit->getPlayer();

			// Check if unit has player and if player has enough food
			if ($player === NULL || $player->hasResourceAmount(ResourceType::FOOD, $foodConsumption) === FALSE) {
				if ($unit->isHungry()) {
					$unit->decreaseHealth(1);
				}
				$unit->setHungry(TRUE);
			} else {
				$player->decreaseResource(ResourceType::FOOD, $foodConsumption);
				$unit->setHungry(FALSE);

				// If the unit is not hungry, it can recover health
				$unit->setHealth($unit->getHealth() + 1);
			}

			if ($unit->isHungry() && $unit->getHealth() <= 0) {
				$this->entityManager->remove($unit);
				// TODO: Notify player about unit death
			}
		}
		$this->entityManager->flush();
	}
}