<?php declare(strict_types = 1);

namespace App\Domain\Unit\Service;

use App\Domain\Building\Building;
use App\Domain\Player\Player;
use App\Domain\Unit\ProductionQueueItem;
use App\Domain\Unit\UnitDefinition;
use App\Model\Database\EntityManager;

class UnitProductionService {
	private EntityManager $entityManager;

	public function __construct(EntityManager $entityManager) {
		$this->entityManager = $entityManager;
	}

	public function startProduction(Player $player, Building $building, UnitDefinition $unitDefinition) : void {
		$buildingType = $building->getType();
		$unitProduction = $buildingType->getUnitProduction($unitDefinition);

		if ($unitProduction === NULL) {
			throw new \InvalidArgumentException('This building cannot produce this unit type.');
		}

		$productionCost = $unitProduction->getCost();

		// Resources check
		foreach ($productionCost as ['type' => $resourceType, 'amount' => $amount]) {
			if (!$player->hasResourceAmount($resourceType, $amount)) {
				throw new \InvalidArgumentException('Insufficient resources to produce this unit.');
			}
		}

		try {
			$this->entityManager->beginTransaction();

			// Deduct resources
			foreach ($productionCost as ['type' => $resourceType, 'amount' => $amount]) {
				$player->decreaseResource($resourceType, $amount);
			}

			// Add to production queue
			$startTime = new \DateTime('now');
			$endTime = new \DateTime('+' . $unitProduction->getProductionTime() . ' minutes');
			$queueItem = new ProductionQueueItem($building, $unitDefinition, $startTime, $endTime);

			$this->entityManager->persist($queueItem);
			$this->entityManager->flush();
			$this->entityManager->commit();

		} catch (\Throwable $e) {
			$this->entityManager->rollback();
			throw $e;
		}
	}

	public function cancelProduction(string $uuid) : void {
		// Find the production queue item
		$queueItem = $this->entityManager->getRepository(ProductionQueueItem::class)->findOneBy(['uuid' => $uuid]);

		if ($queueItem === null) {
			throw new \InvalidArgumentException('Production queue item not found.');
		}

		$this->entityManager->remove($queueItem);
		$this->entityManager->flush();
	}
}