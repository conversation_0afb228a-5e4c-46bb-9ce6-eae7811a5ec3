<?php declare(strict_types = 1);

namespace App\Domain\Unit;

use App\Domain\Building\Building;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TUuid;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_building_production_queue')]
class ProductionQueueItem extends AbstractEntity {

	use TUuid;

	#[ORM\ManyToOne(targetEntity: Building::class)]
	#[ORM\JoinColumn(name: 'building_id', referencedColumnName: 'id', nullable: FALSE)]
	private Building $building;

	#[ORM\ManyToOne(targetEntity: UnitDefinition::class)]
	#[ORM\JoinColumn(name: 'unit_definition_id', referencedColumnName: 'id', nullable: FALSE)]
	private UnitDefinition $unitDefinition;

	#[ORM\Column(type: 'datetime')]
	private DateTime $startTime;

	#[ORM\Column(type: 'datetime')]
	private DateTime $endTime;

	public function __construct(
		Building $building,
		UnitDefinition $unitDefinition,
		DateTime $startTime,
		DateTime $endTime,
	) {
		$this->building = $building;
		$this->unitDefinition = $unitDefinition;
		$this->startTime = $startTime;
		$this->endTime = $endTime;
	}

	public function getBuilding() : Building {
		return $this->building;
	}

	public function getUnitDefinition() : UnitDefinition {
		return $this->unitDefinition;
	}

	public function getStartTime() : DateTime {
		return $this->startTime;
	}

	public function getEndTime() : DateTime {
		return $this->endTime;
	}
}