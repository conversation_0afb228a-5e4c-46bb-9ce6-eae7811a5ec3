<?php declare(strict_types = 1);

namespace App\Domain\User\Exception;

class UserExistsException extends \InvalidArgumentException {

	private const MESSAGE_EMAIL = 'User with email "%s" already exists.';
	private const MESSAGE_NICKNAME = 'User with nickname "%s" already exists.';

	public static function fromEmail(string $email) : self {
		return new self(sprintf(self::MESSAGE_EMAIL, $email));
	}

	public static function fromNickname(string $nickname) : self {
		return new self(sprintf(self::MESSAGE_NICKNAME, $nickname));
	}

}