<?php declare(strict_types = 1);

namespace App\Domain\User;

use App\Domain\Player\Player;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use App\Model\Security\Identity;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\UserEntityInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[ORM\HasLifecycleCallbacks]
class User extends AbstractEntity implements UserEntityInterface {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;

	public const string ROLE_ADMIN = 'admin';

	#[ORM\Column(type: 'string', length: 255, unique: true, nullable: false)]
	private string $email;

	#[ORM\Column(type: 'integer', length: 10, nullable: false, enumType: UserState::class)]
	private UserState $state;

	#[ORM\Column(type: 'string', length: 255, nullable: false)]
	private string $password;

	#[ORM\Column(type: 'string', length: 255, nullable: false, enumType: UserRole::class)]
	private UserRole $role;

	#[ORM\Column(type: 'datetime', nullable: TRUE)]
	private ?DateTime $lastLoggedAt = NULL;

	#[ORM\OneToOne(mappedBy: 'user', targetEntity: Player::class)]
	private ?Player $player = NULL;

	#[ORM\Column(type: 'datetime', nullable: TRUE)]
	private ?DateTime $bannedTo = NULL;

	public function __construct(string $email, string $passwordHash) {
		$this->email = $email;
		$this->password = $passwordHash;

		$this->role = UserRole::PLAYER;
		$this->state = UserState::NEW;
	}

	public function changeLoggedAt() : void {
		$this->lastLoggedAt = new DateTime();
	}

	public function getEmail() : string {
		return $this->email;
	}

	public function getLastLoggedAt() : ?DateTime {
		return $this->lastLoggedAt;
	}

	public function getRole() : UserRole {
		return $this->role;
	}

	public function setRole(UserRole $role) : void {
		$this->role = $role;
	}

	public function getPasswordHash() : string {
		return $this->password;
	}

	public function changePasswordHash(string $password) : void {
		$this->password = $password;
	}

	public function block() : void {
		$this->state = UserState::BLOCKED;
	}

	public function activate() : void {
		$this->state = UserState::ACTIVATED;
	}

	public function isActivated() : bool {
		return $this->state === UserState::ACTIVATED;
	}

	public function getState() : UserState {
		return $this->state;
	}

	public function setState(UserState $state) : void {
		$this->state = $state;
	}

	public function getPlayer() : ?Player {
		return $this->player;
	}

	public function getIdentifier() {
		return $this->getId();
	}

	public function getBannedTo() : ?DateTime {
		return $this->bannedTo;
	}

	public function isBanned() : bool {
		return $this->bannedTo !== NULL && $this->bannedTo > new DateTime();
	}

	public function setBannedTo(?DateTime $bannedTo) : void {
		$this->bannedTo = $bannedTo;
	}

	public function toIdentity() : Identity {
		return new Identity($this->getId(), [$this->getRole()->value], ['email' => $this->getEmail()]);
	}

}
