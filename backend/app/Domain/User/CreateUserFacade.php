<?php declare(strict_types = 1);

namespace App\Domain\User;

use App\Model\Database\EntityManager;
use App\Model\Security\Passwords;

class CreateUserFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	public function createUser(string $email, ?string $password = NULL, UserRole $role = UserRole::PLAYER) : User {
		// Create User
		$user = new User(
			$email,
			Passwords::create()->hash(strval($password ?? md5(microtime()))),
		);

		$user->setRole($role);
		$user->setState(UserState::ACTIVATED);

		// Save user
		$this->em->persist($user);
		$this->em->flush();

		return $user;
	}

}
