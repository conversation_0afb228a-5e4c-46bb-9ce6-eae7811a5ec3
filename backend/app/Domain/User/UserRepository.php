<?php declare(strict_types = 1);

namespace App\Domain\User;

use App\Model\Database\Repository\AbstractRepository;
use App\Model\Security\Passwords;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Repositories\UserRepositoryInterface;
use <PERSON>\Debugger;

/**
 * @method User|NULL find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method User|NULL findOneBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = NULL)
 * @method User[] findAll()
 * @method User[] findBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 *
 * @extends AbstractRepository<User>
 */
class UserRepository extends AbstractRepository implements UserRepositoryInterface {

	public function findOneByEmail(string $email) : ?User {
		return $this->findOneBy(['email' => $email]);
	}

	public function findOneByNickname(string $nickname) : ?User {
		return $this->findOneBy(['nickname' => $nickname]);
	}

	/**
	 * @inheritDoc
	 */
	public function getUserEntityByUserCredentials(
		$username,
		$password,
		$grantType,
		ClientEntityInterface $clientEntity
	) : ?User {
		$user = $this->findOneByEmail($username);
		if ($user === NULL) {
			return NULL;
		}

		if (Passwords::create()->verify($password, $user->getPasswordHash()) === FALSE) {
			return NULL;
		}

		return $user;
	}

}
