<?php declare(strict_types = 1);

namespace App\Domain\Api\Request;

use Symfony\Component\Validator\Constraints as Assert;

class CreateUnitReqDto extends ReqDto
{
    #[Assert\NotBlank]
    #[Assert\Type('string')]
    public string $buildingId;

    #[Assert\NotBlank]
    #[Assert\Type('string')]
    public string $unitDefinitionId;
    
    #[Assert\NotBlank]
    #[Assert\Type('integer')]
    #[Assert\GreaterThan(0)]
    public int $quantity = 1;
}