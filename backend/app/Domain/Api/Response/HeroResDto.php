<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Hero\Hero;

class HeroResDto extends ResDto {

	public string $name;
	public ?string $description;
	public string $sex;

	public int $level;
	public int $experiences;
	public int $experiencesNextLevel;

	public int $health;
	public int $maxHealth;
	public int $defense;
	public int $strength;
	public int $agility;

	public int $maxItems;

	public static function from(Hero $hero) : self {
		$self = new self();

		$self->name = $hero->getName();
		$self->description = $hero->getDescription();
		$self->sex = $hero->getSex()->value;

		$self->level = $hero->getLevel();
		$self->experiences = $hero->getExperiences();
		$self->experiencesNextLevel = $hero->getExperiencesNextLevel();

		$self->health = $hero->getHealth();
		$self->maxHealth = $hero->getMaxHealth();
		$self->defense = $hero->getDefence();
		$self->strength = $hero->getStrength();
		$self->agility = $hero->getAgility();

		$self->maxItems = $hero->getMaxItems();

		return $self;
	}

}