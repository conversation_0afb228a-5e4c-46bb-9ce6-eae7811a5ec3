<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Combat\CombatLog;

class CombatLogResDto extends ResDto {

	public string $attacker;
	public string $defender;
	public int $damage;
	public int $defenderHealth;

	public static function from(CombatLog $log): CombatLogResDto {
		$self = new self();
		$self->attacker = $log->attacker->getName();
		$self->defender = $log->defender->getName();
		$self->damage = $log->damage;
		$self->defenderHealth = $log->defenderHealth;

		return $self;

	}

}