<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Unit\UnitDefinition;

class UnitDefinitionResDto {

	public int $id;
	public string $name;
	public string $description;
	public int $size;
	public int $baseHealth;
	public int $goldCost;
	public int $foodCost;

	public static function from(UnitDefinition $unitDefinition) : self {
		$self = new self();
		$self->id = $unitDefinition->getId();
		$self->name = $unitDefinition->getName();
		$self->description = $unitDefinition->getDescription();
		$self->size = $unitDefinition->getSize();
		$self->baseHealth = $unitDefinition->getBaseHealth();
		$self->goldCost = $unitDefinition->getGoldCost();
		$self->foodCost = $unitDefinition->getFoodCost();

		return $self;
	}

}