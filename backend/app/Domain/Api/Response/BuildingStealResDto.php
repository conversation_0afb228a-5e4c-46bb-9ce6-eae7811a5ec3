<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Building\Building;
use App\Domain\Combat\CombatResult;
use App\Domain\Resource\ResourceType;

class BuildingStealResDto extends ResDto {

	public BuildingResDto $building;
	public CombatResDto $combatResult;

	/** @var ResourceAmountResDto[]|null */
	public ?array $loot = NULL;

	/**
	 * @param array<array{type:ResourceType, amount:int}> $loot
	 */
	public static function from(Building $building, CombatResult $combatResult, array $loot = NULL): BuildingStealResDto {
		$self = new self();
		$self->building = BuildingResDto::from($building);
		$self->combatResult = CombatResDto::from($combatResult);

		if ($loot !== NULL) {
			$self->loot = [];
			foreach ($loot as ['type' => $type, 'amount' => $amount]) {
				if ($amount === 0) {
					continue;
				}
				$self->loot[] = ResourceAmountResDto::from($type, $amount);
			}
		}

		return $self;
	}

}