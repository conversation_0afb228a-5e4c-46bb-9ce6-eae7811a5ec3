<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Building\Building;
use App\Domain\Resource\ResourceType;
use DateTime;
use App\Model\Utils\Position;

class BuildingResDto extends ResDto {

	public string $uuid;
	public BuildingTypeResDto $type;
	public string $name;

	public RegionResDto $region;
	public Position $position;
	public PlayerResDto $owner;

	public ?string $state = NULL;
	public ?int $level = NULL;

	/** @phpstan-var null|array{type:array{slug: string, name: string}, amount:int}[] */
	public ?array $upgradeCost = NULL;

	public ?DateTime $stateValidTo;

	/** @var ResourceAmountResDto[]|null */
	public ?array $stock = NULL;

	/** @var array<mixed>|null */
	public ?array $queue = NULL;

	public static function from(Building $building, bool $forOwner = TRUE) : self {
		$self = new self();
		$self->uuid = $building->getId()->toString();
		$self->type = BuildingTypeResDto::from($building->getType());
		$self->name = $building->getName();

		$self->region = RegionResDto::from($building->getRegion());
		$self->position = $building->getPosition();
		$self->owner = PlayerResDto::from($building->getPlayer());

		if ($forOwner) {
			$self->state = $building->getState()->value;
			$self->level = $building->getLevel();

			$self->upgradeCost = array_map(
				fn($resource) => ['type' => $resource['type']->toArray(), 'amount' => $resource['amount']],
				$building->getType()->getBuildCost($building->getLevel() + 1),
			);

			$self->stateValidTo = $building->getStateValidTo();

			$self->stock = [];
			foreach ($building->getStock() as $type => $amount) {
				if ($amount === 0) {
					continue;
				}
				$self->stock[] = ResourceAmountResDto::from(ResourceType::from($type), $amount);
			}


		}

		return $self;
	}

}