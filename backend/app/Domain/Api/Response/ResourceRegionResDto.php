<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Region\ResourceRegion;

class ResourceRegionResDto extends ResDto {

	public int $id;

	public ?string $name;

	public ResourceRegionTypeResDto $type;

	/** @var array<ResourceAmountResDto> */
	public array $resourceAmounts = [];

	public static function from(ResourceRegion $resourceRegion) : self {
		$self = new self();
		$self->id = $resourceRegion->getId();
		$self->name = $resourceRegion->getName();

		$self->type = ResourceRegionTypeResDto::from($resourceRegion->getResourceRegionType());

		foreach ($resourceRegion->getResourceAmounts() as $resourceAmount) {
			$self->resourceAmounts[] = ResourceAmountResDto::from($resourceAmount->getResourceType(), $resourceAmount->getAmount());
		}

		return $self;
	}
}