<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Item\Item;

class ItemResDto extends ResDto {

	public string $uuid;

	public string $type;
	public string $category;

	public string $name;
	public string $description;
	public int $durability;

	public static function from(Item $item) : self {
		$self = new self();

		$self->uuid = $item->getId()->toString();

		$self->type = $item->getItemDefinition()->getType()->value;
		$self->category = $item->getItemDefinition()->getCategory()->value;

		$self->name = $item->getItemDefinition()->getName();
		$self->description = $item->getItemDefinition()->getDescription();

		$self->durability = $item->getDurability();

		return $self;
	}

}