<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Player\Player;
use DateTimeInterface;

class CurrentPlayerResDto extends ResDto {

	public PlayerResDto $player;
	public ?DateTimeInterface $lastLoggedAt = NULL;

	/** @var PlayerResourceResDto[] */
	public array $resources = [];

	public static function from(Player $player) : self {
		$self = new self();
		$self->player = PlayerResDto::from($player);
		$self->lastLoggedAt = $player->getUser()->getLastLoggedAt();

		$self->resources = array_map(
			fn(array $data) => PlayerResourceResDto::from($data['resource'], $data['amount']),
			$player->getResources(),
		);

		return $self;
	}

}