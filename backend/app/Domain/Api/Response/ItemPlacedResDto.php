<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Item\ItemPlaced;
use App\Model\Utils\Position;

class ItemPlacedResDto extends ResDto {

	public ItemResDto $item;

	public Position $position;

	public static function from(ItemPlaced $itemInstance) : self {
		$self = new self();
		$self->item = ItemResDto::from($itemInstance->getItem());
		$self->position = $itemInstance->getPosition();

		return $self;
	}

}