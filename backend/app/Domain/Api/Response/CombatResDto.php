<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Combat\CombatResult;

class CombatResDto extends ResDto {

	/** @var CombatLogResDto[] */
	public array $logs;
	public bool $isWin;
	public string $winner;
	public string $loser;

	public static function from(CombatResult $result): CombatResDto {
		$self = new self();
		foreach ($result->logs as $log) {
			$self->logs[] = CombatLogResDto::from($log);
		}

		$self->isWin = $result->attacker === $result->winner;
		$self->winner = $result->winner->getName();
		$self->loser = $result->loser->getName();

		return $self;
	}

}