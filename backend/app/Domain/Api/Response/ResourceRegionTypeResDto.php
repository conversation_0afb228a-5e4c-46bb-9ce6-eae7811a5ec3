<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Region\ResourceRegionType;

class ResourceRegionTypeResDto extends ResDto {

	public string $slug;
	public string $name;

	public static function from(ResourceRegionType $resourceRegionType) : self {
		$self = new self();
		$self->slug = $resourceRegionType->value;
		$self->name = $resourceRegionType->getName();

		return $self;
	}

}