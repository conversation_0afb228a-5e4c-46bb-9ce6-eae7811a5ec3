<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Building\BuildingTypeUnitProduction;

class UnitProductionResDto extends ResDto {

	public UnitDefinitionResDto $unitDefinition;
	public int $productionTime;

	/** @var ResourceAmountResDto[] */
	public array $cost;

	public static function fromBuildingUnitProduction(BuildingTypeUnitProduction $production) : self {
		$self = new self();
		$self->unitDefinition = UnitDefinitionResDto::from($production->getUnitDefinition());
		$self->productionTime = $production->getProductionTime();
		$self->cost = array_map(fn($resource) => ResourceAmountResDto::from($resource['type'], $resource['amount']), $production->getCost());

		return $self;
	}

}