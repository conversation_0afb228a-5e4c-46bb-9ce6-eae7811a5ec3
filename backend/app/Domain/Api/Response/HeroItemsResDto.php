<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Hero\HeroItem;
use Doctrine\Common\Collections\Collection;

class HeroItemsResDto extends ResDto {

	/** @var ItemResDto[] */
	public array $items = [];

	/** @var ItemResDto[] */
	public array $equipment = [];

	/**
	 * @param Collection<string, HeroItem> $heroItems
	 */
	public static function from(Collection $heroItems) : self {
		$self = new self();

		foreach ($heroItems as $heroItem) {
			$itemResDto = ItemResDto::from($heroItem->getItem());
			if ($heroItem->isInventorySlot()) {
				$self->items[$heroItem->getSlotKey()] = $itemResDto;
			}
			elseif ($heroItem->isEquipmentSlot()) {
				$self->equipment[$heroItem->getSlotKey()] = $itemResDto;
			}
		}

		return $self;
	}

}