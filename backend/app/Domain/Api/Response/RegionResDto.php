<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Region\Region;

class RegionResDto extends ResDto {

	public int $id;

	public ?string $name;

	public string $level;
	public string $levelName;

	public ?int $ownerId = NULL;
	public ?string $ownerName = NULL;

	/** @var ResourceAmountResDto[] */
	public array $resourceAmounts = [];

	public static function from(Region $region) : self {
		$self = new self();
		$self->id = $region->getId();
		$self->name = $region->getName();

		$self->level = $region->getLevel()->value;
		$self->levelName = $region->getLevel()->getName();

		$self->ownerId = $region->getOwner()?->getId();
		$self->ownerName = $region->getOwner()?->getNickname();

		foreach ($region->getResourceRegions() as $resourceRegion) {
			foreach ($resourceRegion->getResourceAmounts() as $resourceAmount) {
				if (isset($self->resourceAmounts[$resourceAmount->getResourceType()->value])) {
					$self->resourceAmounts[$resourceAmount->getResourceType()->value]->amount += $resourceAmount->getAmount();
				} else {
					$self->resourceAmounts[$resourceAmount->getResourceType()->value] = ResourceAmountResDto::from($resourceAmount->getResourceType(), $resourceAmount->getAmount());
				}
			}
		}

		// Reindex array
		$self->resourceAmounts = array_values($self->resourceAmounts);

		return $self;
	}
}