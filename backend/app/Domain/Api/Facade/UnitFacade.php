<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Request\CreateUnitReqDto;
use App\Domain\Api\Response\UnitResDto;
use App\Domain\Building\Building;
use App\Domain\Building\BuildingRepository;
use App\Domain\Player\Player;
use App\Domain\Unit\Unit;
use App\Domain\Unit\UnitDefinition;
use App\Domain\Unit\UnitRepository;
use App\Domain\Unit\Service\UnitProductionService;
use App\Model\Api\Exception\ApiException;
use App\Model\Database\EntityManager;
use Doctrine\ORM\EntityRepository;

class UnitFacade {
	private UnitRepository $unitRepository;
	private BuildingRepository $buildingRepository;
	/** @var EntityRepository<UnitDefinition> */
	private EntityRepository $unitDefinitionRepository;

	public function __construct(
		EntityManager $entityManager,
		private readonly UnitProductionService $unitProductionService,
	) {
		$this->unitRepository = $entityManager->getRepository(Unit::class);
		$this->buildingRepository = $entityManager->getRepository(Building::class);
		$this->unitDefinitionRepository = $entityManager->getRepository(UnitDefinition::class);
	}

	/**
	 * Get a specific unit for a player
	 */
	public function getOneForPlayer(string $uuid, Player $player) : UnitResDto {
		$unit = $this->unitRepository->find($uuid);

		if ($unit === NULL) {
			throw new ApiException('Unit not found');
		}

		// Check if unit belongs to player's building
		$building = $unit->getBuilding();
		if ($building === null || $building->getPlayer()->getId() !== $player->getId()) {
			throw new ApiException('Unit does not belong to player');
		}

		return UnitResDto::from($unit);
	}

	/**
	 * Find all units belonging to player's buildings
	 * @return array<UnitResDto>
	 */
	public function findAllByPlayer(Player $player) : array {
		$units = $this->unitRepository->findAllByPlayer($player);
		return array_map(fn(Unit $unit) => UnitResDto::from($unit), $units);
	}

	/**
	 * Produce units for a player
	 */
	public function produceUnits(Player $player, CreateUnitReqDto $dto) : UnitResDto {
		$building = $this->buildingRepository->find($dto->buildingId);
		if ($building === NULL) {
			throw new ApiException('Building not found');
		}

		// Check if building belongs to player
		if ($building->getPlayer()->getId() !== $player->getId()) {
			throw new ApiException('Building does not belong to player');
		}

		$unitDefinition = $this->unitDefinitionRepository->find($dto->unitDefinitionId);
		if ($unitDefinition === NULL) {
			throw new ApiException('Unit definition not found');
		}

		// Type is already guaranteed by repository

		// Start production for the specified quantity
		for ($i = 0; $i < $dto->quantity; $i++) {
			$this->unitProductionService->startProduction($player, $building, $unitDefinition);
		}

		// Return a response DTO representing the production request
		// Since production is queued, we create a temporary response
		return new UnitResDto(
			$dto->unitDefinitionId,
			$unitDefinition->getName(),
			$unitDefinition->getDescription(),
			$dto->buildingId,
			[],
			false,
			0.0
		);
	}

}