<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Exception\Building\InvalidBuildingTypeApiException;
use App\Domain\Api\Request\CreateBuildingReqDto;
use App\Domain\Api\Response\BuildingResDto;
use App\Domain\Building\Building;
use App\Domain\Building\BuildingQuery;
use App\Domain\Building\BuildingType;
use App\Domain\Building\BuildingTypeQuery;
use App\Domain\Building\Facade\BuildingCreateFacade;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Settings\Settings;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Database\QueryManager;
use App\Model\Utils\Position;

class BuildingFacade {

	public function __construct(
		private readonly QueryManager 			$qm,
		private readonly PlayerPositionProvider $playerPositionProvider,
		private readonly BuildingCreateFacade 	$buildingCreateFacade,
	) {}

	public function getOneForPlayer(string $id, Player $player) : Building {
		/** @var Building|null $building */
		$building = $this->qm->findOne(BuildingQuery::create()->withId($id));
		if ($building === null) {
			throw new NotFoundException('Building not found');
		}
		return $building;
	}

	/**
	 * @return array<BuildingResDto>
	 */
	public function findAllByPlayer(Player $player) : array {
		/** @var Building[] $buildings */
		$buildings = $this->qm->findAll(BuildingQuery::ofPlayer($player));
		return array_map(
			fn(Building $building) => BuildingResDto::from($building),
			$buildings,
		);
	}

	public function createBuildingOnPlayerPos(Player $player, CreateBuildingReqDto $dto) : BuildingResDto {
		$position = $dto->position !== null ? Position::fromDto($dto->position) : null;

		$position = $this->playerPositionProvider->validateRange($player, $position, $player->getRange());

		/** @var BuildingType|null $type */
		$type = $this->qm->findOne(BuildingTypeQuery::ofSlug($dto->type));

		if ($type === null) {
			throw InvalidBuildingTypeApiException::create();
		}

		return BuildingResDto::from($this->buildingCreateFacade->createBuilding($player, $dto->name, $position, $type));
	}

}