<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\PlayerResDto;
use App\Domain\Player\Player;
use App\Model\Database\EntityManager;
use App\Model\Exception\Runtime\Database\EntityNotFoundException;

class PlayersFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	/**
	 * @return PlayerResDto[]
	 */
	public function findAll(int $limit = 10, int $offset = 0) : array {
		return $this->findBy([], ['id' => 'ASC'], $limit, $offset);
	}

	/**
	 * @param mixed[]  $criteria
	 * @param string[] $orderBy
	 *
	 * @return PlayerResDto[]
	 */
	public function findBy(
		array $criteria = [],
		array $orderBy = ['id' => 'ASC'],
		int   $limit = 10,
		int   $offset = 0,
	) : array {
		$entities = $this->em->getRepository(Player::class)->findBy($criteria, $orderBy, $limit, $offset);
		$result = [];

		foreach ($entities as $entity) {
			$result[] = PlayerResDto::from($entity);
		}

		return $result;
	}

	public function findOne(int $id) : PlayerResDto {
		return $this->findOneBy(['id' => $id]);
	}

	/**
	 * @param mixed[]  $criteria
	 * @param string[] $orderBy
	 */
	public function findOneBy(array $criteria, ?array $orderBy = NULL) : PlayerResDto {
		$entity = $this->em->getRepository(Player::class)->findOneBy($criteria, $orderBy);

		if ($entity === NULL) {
			throw new EntityNotFoundException();
		}

		return PlayerResDto::from($entity);
	}

}