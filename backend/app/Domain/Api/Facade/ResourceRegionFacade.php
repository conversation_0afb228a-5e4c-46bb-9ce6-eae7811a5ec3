<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\ProductionResDto;
use App\Domain\Api\Response\ResourceAmountResDto;
use App\Domain\Api\Response\ResourceInRangeResDto;
use App\Domain\Api\Response\ResourceRegionResDto;
use App\Domain\Building\BuildingType;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Region\Region;
use App\Domain\Region\Repository\RegionRepository;
use App\Domain\Region\Repository\ResourceRegionRepository;
use App\Domain\Region\ResourceRegion;
use App\Domain\Settings\Settings;
use App\Model\Database\EntityManager;
use App\Model\Utils\Position;

class ResourceRegionFacade {

	private RegionRepository $regionRepository;
	private ResourceRegionRepository $resourceRegionRepository;

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {
		$this->regionRepository = $this->em->getRepository(Region::class);
		$this->resourceRegionRepository = $this->em->getRepository(ResourceRegion::class);
	}

	public function find(int $id) : ?ResourceRegionResDto {
		$resourceRegion = $this->em->find(ResourceRegion::class, $id);
		if ($resourceRegion === null) {
			return NULL;
		}

		return ResourceRegionResDto::from($resourceRegion);
	}

	/**
	 * @return ResourceInRangeResDto[]
	 */
	public function findResourcesInRangeForBuild(Player $player, BuildingType $buildingType, int $buildingLvl = 1, ?Position $position = NULL) : array {
		$position = $this->playerPositionProvider->validateRange($player, $position, $player->getRange());

		$region = $this->regionRepository->findBaseRegionByPosition($position);

		if ($region === null) {
			throw new \InvalidArgumentException('Region not found');
		}

		$resourceRegions = $this->resourceRegionRepository->findInRadius($position, $buildingType->getRange());

		$resources = [];
		foreach ($resourceRegions as $resourceRegion) {
			$resourceDistance = (int) round($position->distance($resourceRegion->getPosition()));

			$amounts = [];
			foreach ($resourceRegion->getResourceAmounts() as $resourceAmount) {
				if (!$buildingType->canProduce($resourceAmount->getResourceType())) {
					continue;
				}

				$amounts[] = ResourceAmountResDto::from(
					$resourceAmount->getResourceType(),
					$resourceAmount->getAmount(),
				);
			}

			if ($amounts === []) {
				continue;
			}

			$resources[] = ResourceInRangeResDto::from($resourceDistance, $resourceRegion, $amounts);
		}

		return $resources;
	}

}