<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Request\PlayerHearthBeatReqDto;
use App\Domain\Api\Response\HearthBeatResDto;
use App\Domain\Building\Building;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPosition;
use App\Domain\Region\Region;
use App\Domain\Region\Repository\RegionRepository;
use App\Model\Api\Exception\ApiException;
use App\Model\Database\EntityManager;
use App\Model\Utils\Position;

class PlayerHearthBeatFacade {

	private RegionRepository $regionRepository;

	public function __construct(
		private readonly EntityManager $em
	) {
		$this->regionRepository = $this->em->getRepository(Region::class);
	}

	public function processPlayerHearthBeat(
		Player $player,
		PlayerHearthBeatReqDto $dto,
		string $userAgent,
		string $ip,
	) : HearthBeatResDto {

		try {
			$position = Position::fromDto($dto->latLng);
		} catch (\InvalidArgumentException) {
			throw new ApiException('invalid_position', 'Invalid position');
		}

		$playerPosition = new PlayerPosition($player, $position, $dto->accuracy, $dto->speed, $userAgent, $ip);
		$this->em->persist($playerPosition);
		$this->em->flush();

		$region = $this->regionRepository->findBaseRegionByPosition(Position::fromDto($dto->latLng));

		if ($region === null) {
			throw new ApiException('region_not_found', 'Region not found');
		}

		$buildings = $this->em->getRepository(Building::class)->findBy(['player' => $player, 'region' => $region]);

		return HearthBeatResDto::from($player, $region, $buildings);
	}

}