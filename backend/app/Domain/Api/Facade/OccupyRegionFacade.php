<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\RegionResDto;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Region\Region;
use App\Domain\Region\Repository\RegionRepository;
use App\Model\Database\EntityManager;
use InvalidArgumentException;

class OccupyRegionFacade {

	private RegionRepository $regionRepository;

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {
		$this->regionRepository = $em->getRepository(Region::class);
	}

	public function occupyCurrentRegion(Player $player) : RegionResDto {
		$playerPosition = $this->playerPositionProvider->getPosition($player);

		if ($playerPosition === null) {
			throw new InvalidArgumentException('Player has no position');
		}

		$region = $this->regionRepository->findBaseRegionByPosition($playerPosition);

		if ($region === null) {
			throw new InvalidArgumentException('Region not found');
		}

		// TODO more complex logic
		$region->setOwner($player);
		$this->em->flush();

		return RegionResDto::from($region);
	}

}