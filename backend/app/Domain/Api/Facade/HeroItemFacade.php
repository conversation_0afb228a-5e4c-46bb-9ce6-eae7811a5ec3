<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\ItemPlacedResDto;
use App\Domain\Api\Response\ItemResDto;
use App\Domain\Api\Response\HeroItemsResDto;
use App\Domain\Hero\Hero;
use App\Domain\Hero\HeroItem;
use App\Domain\Hero\HeroItemQuery;
use App\Domain\Item\Facade\DropItemFacade;
use App\Domain\Item\Facade\MoveItemFacade;
use App\Domain\Item\Facade\PickUpItemFacade;
use App\Domain\Item\ItemPlaced;
use App\Domain\Item\ItemPlacedQuery;
use App\Model\Api\Exception\ApiException;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Database\QueryManager;

class HeroItemFacade {

	public function __construct(
		private readonly QueryManager 		$qm,
		private readonly PickUpItemFacade 	$pickUpItemFacade,
		private readonly DropItemFacade 	$dropItemFacade,
		private readonly MoveItemFacade 	$moveItemFacade,
	) {}

	public function findItem(Hero $hero, string $uuid) : ItemResDto {
		/** @var HeroItem|null $heroItem */
		$heroItem = $this->qm->findOne(HeroItemQuery::ofHero($hero)->withUuid($uuid));
		if ($heroItem === null) {
			throw new NotFoundException("Hero item not found");
		}

		return ItemResDto::from($heroItem->getItem());
	}

	public function pickUp(Hero $hero, string $uuid) : ItemResDto {
		/** @var ItemPlaced $itemPlaced */
		$itemPlaced = $this->qm->findOne(ItemPlacedQuery::create()->withUuid($uuid));
		$heroItem = $this->pickUpItemFacade->pickUpItem($hero, $itemPlaced);
		if ($heroItem === null) {
			throw new ApiException("Failed to pick up item");
		}

		return ItemResDto::from($heroItem->getItem());
	}

	public function dropItem(Hero $hero, string $uuid) : ItemPlacedResDto {
		/** @var HeroItem|null $heroItem */
		$heroItem = $this->qm->findOne(HeroItemQuery::ofHero($hero)->withUuid($uuid));
		if ($heroItem === null) {
			throw new NotFoundException("Hero item not found");
		}

		$itemPlaced = $this->dropItemFacade->dropItem($hero, $heroItem);
		if ($itemPlaced === null) {
			throw new ApiException("Failed to drop item");
		}

		return ItemPlacedResDto::from($itemPlaced);
	}

	public function getAllItems(Hero $hero) : HeroItemsResDto {
		return HeroItemsResDto::from($hero->getItems());
	}

	public function moveItem(Hero $hero, string $from, string $to) : HeroItemsResDto {
		$this->moveItemFacade->moveItem($hero, $from, $to);

		return $this->getAllItems($hero);
	}

}