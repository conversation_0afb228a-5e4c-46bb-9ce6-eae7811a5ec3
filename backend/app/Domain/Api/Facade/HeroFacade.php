<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Request\CreateHeroReqDto;
use App\Domain\Api\Request\UpdateHeroReqDto;
use App\Domain\Api\Response\HeroResDto;
use App\Domain\Hero\Facade\HeroCreateFacade;
use App\Domain\Hero\Facade\HeroUpdateFacade;
use App\Domain\Hero\Hero;
use App\Domain\Player\Player;

class HeroFacade {

	public function __construct(
		private readonly HeroCreateFacade $heroCreateFacade,
		private readonly HeroUpdateFacade $heroUpdateFacade,
	) {}

	public function createHero(Player $player, CreateHeroReqDto $dto) : HeroResDto {
		$hero = $this->heroCreateFacade->createHero($player, $dto->name);
		return HeroResDto::from($hero);
	}

	public function updateHero(Hero $hero, UpdateHeroReqDto $dto) : HeroResDto {
		$hero = $this->heroUpdateFacade->updateHero($hero, $dto->name);
		return HeroResDto::from($hero);
	}

}