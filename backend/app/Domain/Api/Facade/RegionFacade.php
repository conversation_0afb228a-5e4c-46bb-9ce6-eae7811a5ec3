<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\RegionResDto;
use App\Domain\Player\Player;
use App\Domain\Region\Region;
use App\Domain\Region\Repository\RegionRepository;
use App\Model\Database\EntityManager;

class RegionFacade {

	private RegionRepository $regionRepository;

	public function __construct(
		EntityManager $em,
	) {
		$this->regionRepository = $em->getRepository(Region::class);
	}

	public function find(int $id) : ?RegionResDto {
		$region = $this->regionRepository->find($id);
		if ($region === null) {
			return NULL;
		}

		return RegionResDto::from($region);
	}

	/**
	 * @return array<RegionResDto>
	 */
	public function findByPlayer(Player $player) : array {
		$regions = $this->regionRepository->findBy(['owner' => $player]);
		return array_map(fn(Region $region) => RegionResDto::from($region), $regions);
	}

}