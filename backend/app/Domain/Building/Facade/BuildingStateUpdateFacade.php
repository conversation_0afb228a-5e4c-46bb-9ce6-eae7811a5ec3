<?php declare(strict_types = 1);

namespace App\Domain\Building\Facade;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingRepository;
use App\Model\Database\EntityManager;

class BuildingStateUpdateFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	/**
	 * @param Building[] $buildings
	 */
	public function updateStates(array $buildings) : void {
		foreach ($buildings as $building) {
			$this->updateState($building, FALSE);
		}

		$this->em->flush();

	}

	public function updateState(Building $building, bool $flush = TRUE) : void {
		if ($building->isStateValid()) {
			return;
		}

		if ($building->isBuilding()) {
			$building->setBuilt();
		}
		elseif ($building->isDemolishing()) {
			$building->setDemolished();
		}
		elseif ($building->isUpgrading()) {
			$building->setBuilt();
		}
		elseif ($building->isDemolished()) {
			$this->em->remove($building);
		}

		if ($flush) {
			$this->em->flush();
		}
	}

}