<?php declare(strict_types = 1);

namespace App\Domain\Building\Facade;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingQuery;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Model\Database\EntityManager;
use App\Model\Database\QueryManager;
use App\Model\Exception\Logic\InvalidArgumentException;
use App\Model\Exception\Runtime\NotFoundException;

class BuildingDemolishFacade {

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly QueryManager 			$qm,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {}

	public function demolish(Player $player, string $buildingId) : Building {
		/** @var Building|null $building */
		$building = $this->qm->findOne(BuildingQuery::ofPlayer($player)->withId($buildingId));

		if ($building === null) {
			throw new NotFoundException("Building not found");
		}

		// Check if player is in range of the building
		$this->playerPositionProvider->validateRange($player, $building->getPosition(), $player->getRange());

		// Upgrade building
		$building->setDemolishing();

		$this->em->flush();

		return $building;
	}

}