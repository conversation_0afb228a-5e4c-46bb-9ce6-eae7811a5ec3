<?php declare(strict_types = 1);

namespace App\Domain\Building\Facade;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingRepository;
use App\Domain\Building\BuildingType;
use App\Domain\Player\Player;
use App\Domain\Region\Region;
use App\Domain\Region\Repository\RegionRepository;
use App\Domain\Region\Repository\ResourceRegionRepository;
use App\Domain\Region\ResourceRegion;
use App\Domain\Settings\Settings;
use App\Model\Database\EntityManager;
use App\Model\Exception\Logic\InvalidArgumentException;
use App\Model\Utils\Position;

class BuildingCreateFacade {

	private RegionRepository $regionRepository;

	private ResourceRegionRepository $resourceRegionRepository;

	private BuildingRepository $buildingRepository;

	public function __construct(
		private readonly EntityManager $em,
	) {
		$this->regionRepository = $this->em->getRepository(Region::class);
		$this->resourceRegionRepository = $this->em->getRepository(ResourceRegion::class);
		$this->buildingRepository = $this->em->getRepository(Building::class);
	}

	/**
	 * @throws InvalidArgumentException
	 */
	public function createBuilding(Player $player, string $name, Position $position, BuildingType $type) : Building {
		$region = $this->regionRepository->findBaseRegionByPosition($position);

		if ($region === null) {
			throw new InvalidArgumentException('Region not found');
		}

		if ($type->isUniqueInWorld() && $this->em->getRepository(Building::class)->findOneBy(['type' => $type, 'player' => $player]) !== null) {
			throw new InvalidArgumentException('Building already exists. (unique in world)');
		}

		if ($type->isUniqueInRegion() && $this->em->getRepository(Building::class)->findOneBy(['type' => $type, 'player' => $player, 'region' => $region]) !== null) {
			throw new InvalidArgumentException('Building already exists. (unique in region)');
		}

		$atSamePosition = $this->buildingRepository->findInRadius($position, Settings::getInt(Settings::MIN_BUILDING_DISTANCE));
		if ($atSamePosition !== []) {
			throw new InvalidArgumentException('Building too close to another building');
		}

		$building = new Building($player, $name, $position, $type, $region);

		// Add resource regions in range if building has production
		if ($type->hasProduction()) {
			$resourceRegions = $this->resourceRegionRepository->findInRadius($position, $type->getRange());

			// Sort by distance
			$distances = [];
			foreach ($resourceRegions as $key => $resource) {
				$distances[$key] = $position->distance($resource->getPosition());
			}
			asort($distances);

			// Add resources sorted by distance
			foreach ($distances as $key => $distance) {
				$resource = $resourceRegions[$key];

				// Check if any resource type can be produced
				foreach ($resource->getResourceTypes() as $resType) {
					if ($type->canProduce($resType)) {
						$building->addResource($resource);
						break;
					}
				}
			}
		}

		// Decrease player's resources
		foreach ($type->getBuildCost() as ['type' => $resourceType, 'amount' => $amount]) {
			$player->decreaseResource($resourceType, $amount);
		}

		$this->em->persist($building);
		$this->em->flush();

		return $building;
	}

}