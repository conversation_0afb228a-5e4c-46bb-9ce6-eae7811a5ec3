<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @extends AbstractRepository<BuildingJob>
 *
 * @method BuildingJob|null find(int $id)
 * @method BuildingJob[] findAll()
 * @method BuildingJob[] findBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null, int|null $limit = null, int|null $offset = null)
 * @method BuildingJob|null findOneBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null)
 */
class BuildingJobRepository extends AbstractRepository {

}