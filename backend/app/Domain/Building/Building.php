<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Combat\ICombatable;
use App\Domain\Combat\THealthStats;
use App\Domain\Player\Player;
use App\Domain\Region\Region;
use App\Domain\Region\ResourceRegion;
use App\Domain\Resource\ResourceType;
use App\Domain\Unit\Unit;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TPosition;
use App\Model\Database\Entity\TUpdatedAt;
use App\Model\Database\Entity\TUuid;
use App\Model\Utils\Position;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BuildingRepository::class)]
#[ORM\Table(name: 'game_building')]
#[ORM\HasLifecycleCallbacks]
class Building extends AbstractEntity implements ICombatable {

	use TUuid;
	use TCreatedAt;
	use TUpdatedAt;
	use TPosition;
	use THealthStats;

	#[ORM\ManyToOne(targetEntity: Player::class)]
	#[ORM\JoinColumn(name: 'player_id', referencedColumnName: 'id', nullable: FALSE)]
	private Player $player;

	#[ORM\Column(type: 'string', nullable: FALSE)]
	private string $name;

	#[ORM\ManyToOne(targetEntity: BuildingType::class)]
	#[ORM\JoinColumn(name: 'building_type_id', referencedColumnName: 'id', nullable: FALSE)]
	private BuildingType $type;

	#[ORM\ManyToOne(targetEntity: Region::class)]
	#[ORM\JoinColumn(name: 'region_id', referencedColumnName: 'id', nullable: FALSE)]
	private Region $region;

	#[ORM\Column(type: 'string', nullable: FALSE, enumType: BuildingState::class, options: ['default' => BuildingState::BUILDING])]
	private BuildingState $state;

	#[ORM\Column(type: 'datetime', nullable: TRUE)]
	private ?DateTime $stateValidTo = NULL;

	#[ORM\Column(type: 'integer', nullable: FALSE, options: ['default' => 1])]
	private int $level = 1;

	/** @var Collection<int, BuildingResource> */
	#[ORM\OneToMany(mappedBy: 'building', targetEntity: BuildingResource::class, cascade: ['persist'])]
	private Collection $resources;

	/**
	 * Production accumulator (for production calculation)
	 * @var array<string, float>
	 */
	#[ORM\Column(type: 'json', nullable: FALSE, options: ['default' => '{}'])]
	private array $productionAccumulator = [];

	/** @var array<string, int> */
	#[ORM\Column(type: 'json', nullable: FALSE, options: ['default' => '{}'])]
	private array $stock = [];

	#[ORM\Column(type: 'datetime', nullable: TRUE)]
	private ?DateTime $lastResourcesUpdate = NULL;

	#[ORM\Column(type: 'integer', options: ['default' => 10])]
	private int $maxUnits = 10;

	/**
	 * @var Collection<int, Unit>
	 */
	#[ORM\OneToMany(mappedBy: 'building', targetEntity: Unit::class)]
	private Collection $units;

	public function __construct(Player $player, string $name, Position $position, BuildingType $type, Region $region) {
		$this->player = $player;
		$this->name = $name;
		$this->setPosition($position);
		$this->type = $type;
		$this->region = $region;
		$this->units = new ArrayCollection();

		$this->state = BuildingState::BUILDING;
		$this->stateValidTo = new DateTime('+ ' . $type->getBuildTime() . ' minutes');

		$this->resources = new ArrayCollection();

		// Initialize stock
		foreach ($type->getProductions() as $production) {
			$this->stock[$production['type']->value] = 0;
		}

		// Initialize production accumulator
		foreach ($type->getProductions() as $production) {
			$this->productionAccumulator[$production['type']->value] = 0;
		}
	}

	public function getMaxUnits() : int {
		return $this->maxUnits;
	}

	public function setMaxUnits(int $maxUnits) : void {
		$this->maxUnits = $maxUnits;
	}

	/**
	 * @return Collection<int, Unit>
	 */
	public function getUnits() : Collection {
		return $this->units;
	}

	public function addUnit(Unit $unit) : void {
		if (!$this->units->contains($unit)) {
			$this->units->add($unit);
			$unit->setBuilding($this);
		}
	}

	public function removeUnit(Unit $unit) : void {
		if ($this->units->contains($unit)) {
			$this->units->removeElement($unit);
			$unit->setBuilding(NULL);
		}
	}

	public function getPlayer() : Player {
		return $this->player;
	}

	public function getName() : string {
		return $this->name;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	public function getType() : BuildingType {
		return $this->type;
	}

	public function getRegion() : Region {
		return $this->region;
	}

	public function getState() : BuildingState {
		return $this->state;
	}

	public function isBuilding() : bool {
		return $this->state === BuildingState::BUILDING;
	}

	public function isBuilt() : bool {
		return $this->state === BuildingState::BUILT;
	}

	public function isDemolishing() : bool {
		return $this->state === BuildingState::DEMOLISHING;
	}

	public function isDemolished() : bool {
		return $this->state === BuildingState::DEMOLISHED;
	}

	public function isUpgrading() : bool {
		return $this->state === BuildingState::UPGRADING;
	}

	public function getStateValidTo() : ?DateTime {
		return $this->stateValidTo;
	}

	public function isStateValid() : bool {
		return $this->stateValidTo === NULL || $this->stateValidTo > new DateTime('now');
	}

	public function setBuilt() : void {
		$this->state = BuildingState::BUILT;
		$this->stateValidTo = NULL;
	}

	public function setDemolishing() : void {
		$this->state = BuildingState::DEMOLISHING;
		$this->stateValidTo = new DateTime('+ ' . $this->type->getDemolishTime() . ' minutes');
	}

	public function setDemolished() : void {
		$this->state = BuildingState::DEMOLISHED;
		$this->stateValidTo = new DateTime('+ 1 day');;

		$demolishGain = $this->getType()->getDemolishGain($this->getLevel());

		// Decrease player resources
		foreach ($demolishGain as ['type' => $type, 'amount' => $amount]) {
			$this->increaseResourceStock($type, $amount);
		}
	}

	public function setUpgrading() : void {
		$this->state = BuildingState::UPGRADING;
		$this->stateValidTo = new DateTime('+ ' . $this->type->getBuildTime() . ' minutes');
	}

	public function getLevel() : int {
		return $this->level;
	}

	public function setLevel(int $level) : void {
		$this->level = $level;
	}

	public function getProduction(ResourceType $resource) : int {
		return $this->type->getProductionLvl($resource, $this->level);
	}

	/**
	 * @return Collection<int, BuildingResource>
	 */
	public function getResources() : Collection {
		return $this->resources;
	}

	public function addResource(ResourceRegion $resourceRegion) : void {
		$this->resources->add(new BuildingResource($this, $resourceRegion,
			$this->getPosition()->distance($resourceRegion->getPosition())));
	}

	public function removeResource(BuildingResource $buildingResource) : bool {
		return $this->resources->removeElement($buildingResource);
	}

	public function getProductionAccumulator(ResourceType $resource) : float {
		return $this->productionAccumulator[$resource->value] ?? 0;
	}

	public function setProductionAccumulator(ResourceType $resource, float $amount) : void {
		$this->productionAccumulator[$resource->value] = $amount;
	}

	public function getResourceStock(ResourceType $resource) : int {
		return $this->stock[$resource->value] ?? 0;
	}

	/**
	 * @return array<string, int>
	 */
	public function getStock() : array {
		return $this->stock;
	}

	public function increaseResourceStock(ResourceType $resource, int $amount) : void {
		if (!isset($this->stock[$resource->value])) {
			$this->stock[$resource->value] = 0;
		}

		$this->stock[$resource->value] += $amount;
	}

	public function decreaseResourceStock(ResourceType $resource, int $amount) : void {
		if (!isset($this->stock[$resource->value])) {
			$this->stock[$resource->value] = 0;
		}

		if ($this->stock[$resource->value] - $amount < 0) {
			throw new \InvalidArgumentException('Not enough resources');
		}

		$this->stock[$resource->value] -= $amount;
	}

	public function getLastResourcesUpdate() : ?DateTime {
		return $this->lastResourcesUpdate;
	}

	public function setLastResourcesUpdate() : void {
		$this->lastResourcesUpdate = new DateTime();
	}

	// ----- Combat ----------------------------------------------------------------------------------------------------

	public function getDefence() : int {
		return $this->type->getDefense();
	}

	public function getStrength() : int {
		return $this->type->getAttack();
	}

	public function getAgility() : int {
		return 10;
	}

	public function getLuck() : int {
		return 5;
	}

	public function getMaxHealth() : int {
		return 100;
	}

}