<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BuildingJobRepository::class)]
#[ORM\Table(name: 'game_building_job')]
class BuildingJob extends AbstractEntity {

	use TId;
	use TCreatedAt;

	private ?\DateTime $startTime = NULL;

	private ?\DateTime $finishTime = NULL;

	/**
	 * @var int minutes
	 */
	private int $duration = 0;

	private ?BuildingTypeUnitProduction $unitProduction = NULL;

	public function __construct() {}

	public function getStartTime() : ?\DateTime {
		return $this->startTime;
	}

	public function setStartTime(?\DateTime $startTime) : void {
		$this->startTime = $startTime;
	}

	public function getFinishTime() : ?\DateTime {
		return $this->finishTime;
	}

	public function setFinishTime(?\DateTime $finishTime) : void {
		$this->finishTime = $finishTime;
	}

	public function getDuration() : int {
		return $this->duration;
	}

	public function setDuration(int $duration) : void {
		$this->duration = $duration;
	}

	public function isUnitProduction() : bool {
		return $this->unitProduction !== NULL;
	}

	public function getUnitProduction() : ?BuildingTypeUnitProduction {
		return $this->unitProduction;
	}

	public function setUnitProduction(?BuildingTypeUnitProduction $unitProduction) : void {
		$this->unitProduction = $unitProduction;
	}

}