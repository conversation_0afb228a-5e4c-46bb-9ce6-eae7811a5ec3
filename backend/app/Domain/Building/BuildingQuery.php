<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Player\Player;
use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class BuildingQuery extends AbstractQuery {

	public static function create() : self {
		return new self();
	}

	public static function ofPlayer(Player $player) : self {
		$self = new self();
		$self->ons[] = function (QueryBuilder $qb) use ($player) : QueryBuilder {
			$qb->andWhere('b.player = :player')
			   ->setParameter('player', $player);

			return $qb;
		};

		return $self;
	}

	public function withId(string $id) : self {
		$this->ons[] = function (QueryBuilder $qb) use ($id) : QueryBuilder {
			$qb->andWhere('b.id = :id')
			   ->setParameter('id', $id);

			return $qb;
		};

		return $this;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('b')
			   ->from(Building::class, 'b');

			return $qb;
		};
	}

}
