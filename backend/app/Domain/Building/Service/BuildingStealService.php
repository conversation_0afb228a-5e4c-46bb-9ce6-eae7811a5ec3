<?php declare(strict_types = 1);

namespace App\Domain\Building\Service;

use App\Domain\Building\Building;
use App\Domain\Combat\CombatParams;
use App\Domain\Combat\CombatResult;
use App\Domain\Combat\CombatService;
use App\Domain\Combat\Exception\CombatException;
use App\Domain\Hero\Hero;
use App\Domain\Player\Player;
use App\Domain\Resource\ResourceType;
use App\Model\Api\Exception\ApiException;
use App\Model\Database\EntityManager;

class BuildingStealService {

	public function __construct(
		private readonly CombatService $combatService,
		private readonly EntityManager $em,
	) {}

	/**
	 * @return array{combatResult: CombatResult, loot: array<array{type: ResourceType, amount: int}>|null}
	 */
	public function stealResources(Building $building, Hero $hero) : array {
		$combatParams = new CombatParams();
		$combatParams->combatType = CombatParams::TYPE_SIEGE;

		try {
			$this->em->beginTransaction();

			$combatResult = $this->combatService->battle($hero, $building, $combatParams);

			$loot = NULL;
			if ($combatResult->winner === $hero) {
				$loot = [];
				$resources = $building->getStock();

				foreach ($resources as $resourceName => $amount) {
					$resType = ResourceType::from($resourceName);
					$hero->getPlayer()->increaseResource($resType, $amount);
					$building->decreaseResourceStock($resType, $amount);

					$loot[] = [
						'type' => $resType,
						'amount' => $amount,
					];
				}
			}

			$this->em->flush();
			$this->em->commit();

			return [
				'combatResult' => $combatResult,
				'loot' => $loot,
			];

		} catch (\Throwable $e) {
			$this->em->rollback();
			throw new CombatException('Combat failed: ' . $e->getMessage(), 0, $e);
		}
	}
}