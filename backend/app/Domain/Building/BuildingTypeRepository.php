<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @method BuildingType|null find(int $id)
 * @method BuildingType|null findOneBy(array<string, mixed> $criteria, array<string, mixed> $orderBy = [])
 * @method BuildingType[] findAll()
 * @method BuildingType[] findBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null, int|null $limit = null, int|null $offset = null)
 *
 * @extends AbstractRepository<BuildingType>
 */
class BuildingTypeRepository extends AbstractRepository {

	public function delete(BuildingType|int $buildingType) : bool {
		$buildingType = $buildingType instanceof BuildingType ? $buildingType : $this->find($buildingType);
		if ($buildingType === NULL) {
			return FALSE;
		}

		$this->_em->remove($buildingType);
		$this->_em->flush();

		return TRUE;
	}

}