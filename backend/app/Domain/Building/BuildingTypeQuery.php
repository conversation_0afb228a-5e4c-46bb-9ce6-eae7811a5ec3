<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Player\Player;
use App\Domain\User\User;
use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class BuildingTypeQuery extends AbstractQuery {

	public static function ofSlug(string $slug) : self {
		$self = new self();
		$self->ons[] = function (QueryBuilder $qb) use ($slug) : QueryBuilder {
			$qb->andWhere('bt.slug = :slug')
			   ->setParameter('slug', $slug);

			return $qb;
		};

		return $self;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('bt')
			   ->from(BuildingType::class, 'bt');

			return $qb;
		};
	}

}
