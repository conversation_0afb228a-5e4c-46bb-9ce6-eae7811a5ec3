<?php declare(strict_types = 1);

namespace App\Domain\Player;

use App\Domain\Player\Repository\PlayerPositionRepository;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TPosition;
use App\Model\Utils\Position;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlayerPositionRepository::class)]
#[ORM\Table(name: 'game_player_position')]
#[ORM\HasLifecycleCallbacks]
class PlayerPosition extends AbstractEntity {

	use TId;
	use TCreatedAt;
	use TPosition;

	#[ORM\ManyToOne(targetEntity: Player::class, inversedBy: 'positions')]
	#[ORM\JoinColumn(name: 'player_id', referencedColumnName: 'id', nullable: false, onDelete: 'CASCADE')]
	private Player $player;

	#[ORM\Column(type: 'float', nullable: false)]
	private float $accuracy;

	#[ORM\Column(type: 'float', nullable: TRUE)]
	private ?float $speed;

	#[ORM\Column(type: 'string', nullable: false)]
	private string $userAgent;

	#[ORM\Column(type: 'string', nullable: false)]
	private string $ip;

	public function __construct(
		Player $player,
		Position $position,
		float $accuracy,
		?float $speed,
		string $userAgent,
		string $ip,
	) {
		$this->player = $player;
		$this->setPosition($position);
		$this->accuracy = $accuracy;
		$this->speed = $speed;
		$this->userAgent = $userAgent;
		$this->ip = $ip;
	}

	public function getUserAgent() : string {
		return $this->userAgent;
	}

	public function getIp() : string {
		return $this->ip;
	}
}