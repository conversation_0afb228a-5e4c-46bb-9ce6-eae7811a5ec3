<?php declare(strict_types = 1);

namespace App\Domain\Player;

use App\Domain\Player\Exception\IsTooFarException;
use App\Domain\Player\Exception\PlayerHasNoPositionException;
use App\Domain\Player\Repository\PlayerPositionRepository;
use App\Domain\Settings\Settings;
use App\Model\Database\EntityManager;
use App\Model\Utils\Position;
use DateTime;

/**
 * Provides player's current position.
 * Validate if the position is not older than 30 seconds.
 * @todo Check that the player is not cheating
 */
class PlayerPositionProvider {

	private PlayerPositionRepository $playerPositionRepository;

	public function __construct(
		EntityManager $em,
	) {
		$this->playerPositionRepository = $em->getRepository(PlayerPosition::class);
	}

	public function getPosition(Player $player) : ?Position {
		$playerPos = $this->playerPositionRepository->findLastPlayerPosition($player);

		if ($playerPos === null) {
			return null;
		}

//		if ($playerPos->getCreatedAt() < new DateTime('-' . Settings::getInt(Settings::PLAYER_POSITION_MAX_AGE) . ' seconds')) {
//			return null;
//		}

		return $playerPos->getPosition();
	}

	/**
	 * @throws PlayerHasNoPositionException
	 * @throws IsTooFarException
	 */
	public function validateRange(Player $player, ?Position $position, int $range) : Position {
		$playerPosition = $this->getPosition($player);

		if ($playerPosition === null) {
			throw new PlayerHasNoPositionException('Player has no position or position is expired');
		}

		if ($position !== null) {
			$distance = $position->distance($playerPosition);
			if ($distance > $range) {
				throw new IsTooFarException($distance, $range);
			}
		}

		if ($position === null) {
			$position = $playerPosition;
		}

		return $position;
	}

}