<?php declare(strict_types = 1);

namespace App\Domain\Player\Repository;

use App\Domain\Player\Player;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method Player|NULL find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method Player|NULL findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL)
 * @method Player[] findAll()
 * @method Player[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 *
 * @extends AbstractRepository<Player>
 */class PlayerRepository extends AbstractRepository {

	public function delete(Player|int $player) : bool {
		$player = $player instanceof Player ? $player : $this->find($player);

		if ($player === NULL) {
			return FALSE;
		}

		$this->_em->remove($player);

		return TRUE;
	}

}