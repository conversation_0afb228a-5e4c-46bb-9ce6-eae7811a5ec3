<?php declare(strict_types = 1);

namespace App\Domain\Player\Repository;

use App\Domain\Player\Player;
use App\Domain\Player\PlayerPosition;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method PlayerPosition|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method PlayerPosition|null findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = null)
 * @method PlayerPosition[] findAll()
 * @method PlayerPosition[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = null, ?int $limit = null, ?int $offset = null)
 *
 * @extends AbstractRepository<PlayerPosition>
 */
class PlayerPositionRepository extends AbstractRepository {

	public function findLastPlayerPosition(Player $player) : ?PlayerPosition {
		return $this->findOneBy(['player' => $player], ['createdAt' => 'DESC']);
	}

}