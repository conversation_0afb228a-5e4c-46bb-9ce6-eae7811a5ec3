<?php declare(strict_types = 1);

namespace App\Domain\Player;

use App\Domain\Hero\Hero;
use App\Domain\Hero\HeroSexType;
use App\Domain\Player\Repository\PlayerRepository;
use App\Domain\Resource\ResourceType;
use App\Domain\Settings\Settings;
use App\Domain\User\User;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use App\Model\Security\Identity;
use App\Model\Utils\Position;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlayerRepository::class)]
#[ORM\Table(name: 'game_player')]
#[ORM\HasLifecycleCallbacks]
class Player extends AbstractEntity {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;

	#[ORM\OneToOne(targetEntity: User::class)]
	#[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: false)]
	private User $user;

	#[ORM\OneToOne(mappedBy: 'player', targetEntity: Hero::class)]
	private ?Hero $hero = NULL;

	#[ORM\Column(type: 'string', nullable: false)]
	private string $nickname;

	/** @var Collection<int, PlayerPosition> */
	#[ORM\OneToMany(mappedBy: 'player', targetEntity: PlayerPosition::class, cascade: ['persist'])]
	private Collection $positions;

	/** @var array<string, int> */
	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $resources = [];

	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 50])]
	private int $range = 50;

	public function __construct(User $user, string $nickname) {
		$this->user = $user;
		$this->nickname = $nickname;

		// Initialize default resources
		$this->resources = Settings::getArray(Settings::PLAYER_DEFAULT_RESOURCES);
		foreach (ResourceType::getAll() as $resource) {
			if (!isset($this->resources[$resource->value])) {
				$this->resources[$resource->value] = 0;
			}
		}

		$this->positions = new ArrayCollection();
	}

	public function getUser() : User {
		return $this->user;
	}

	public function getHero() : ?Hero {
		return $this->hero;
	}

	public function setHero(?Hero $hero) : void {
		$this->hero = $hero;
	}

	public function getNickname() : string {
		return $this->nickname;
	}

	public function setNickname(string $nickname) : void {
		$this->nickname = $nickname;
	}

	public function getRange() : int {
		return $this->range;
	}

	public function setRange(int $range) : void {
		$this->range = $range;
	}

	/**
	 * @return Collection<int, PlayerPosition>
	 */
	public function getPositions() : Collection {
		return $this->positions;
	}

	public function addPosition(
		Player $player,
		Position $position,
		float $accuracy,
		?float $speed,
		string $userAgent,
		string $ip,
	) : PlayerPosition {
		$playerPos = new PlayerPosition($player, $position, $accuracy, $speed, $userAgent, $ip);
		$this->positions->add($playerPos);

		return $playerPos;
	}

	public function getResource(ResourceType $resource) : int {
		return $this->resources[$resource->value] ?? 0;
	}

	/**
	 * @return array<string, array{resource: ResourceType, amount: int}>
	 */
	public function getResources() : array {
		$result = [];
		foreach ($this->resources as $resource => $amount) {
			$result[$resource] = [
				'resource' => ResourceType::from($resource),
				'amount' => $amount,
			];
		}
		return $result;
	}

	public function increaseResource(ResourceType $resource, int $amount) : void {
		if (!isset($this->resources[$resource->value])) {
			$this->resources[$resource->value] = 0;
		}

		$this->resources[$resource->value] += $amount;
	}

	public function decreaseResource(ResourceType $resource, int $amount) : void {
		if (!isset($this->resources[$resource->value])) {
			$this->resources[$resource->value] = 0;
		}

		if ($this->resources[$resource->value] - $amount < 0) {
			throw new \InvalidArgumentException('Not enough resources');
		}

		$this->resources[$resource->value] -= $amount;
	}

	public function hasResourceAmount(ResourceType $resource, int $amount) : bool {
		return ($this->resources[$resource->value] ?? 0) >= $amount;
	}

	public function toIdentity() : Identity {
		$user = $this->getUser();
		return new Identity(
			$this->getId(),
			[$user->getRole()->value],
			[
				'email' => $user->getEmail(),
				'player' => [
					'nickname' => $this->getNickname(),
					'lastLoggedAt' => $user->getLastLoggedAt(),
				],
			]);
	}

}