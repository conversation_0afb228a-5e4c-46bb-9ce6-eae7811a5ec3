<?php declare(strict_types = 1);

namespace App\Domain\Player;

use App\Domain\User\User;
use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class PlayerQuery extends AbstractQuery {

	public static function ofUser(User $user) : self {
		$self = new self();
		$self->ons[] = function (QueryBuilder $qb) use ($user) : QueryBuilder {
			$qb->andWhere('p.user = :user')
			   ->setParameter('user', $user);

			return $qb;
		};

		return $self;
	}
	public static function ofNickname(string $nickname) : self {
		$self = new self();
		$self->ons[] = function (QueryBuilder $qb) use ($nickname) : QueryBuilder {
			$qb->andWhere('LOWER(p.nickname) = LOWER(:nickname)')
			   ->setParameter('nickname', $nickname);

			return $qb;
		};

		return $self;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('p')
			   ->from(Player::class, 'p');

			return $qb;
		};
	}

}
