<?php declare(strict_types = 1);

namespace App\Domain\Player\Exception;

use Apitte\Core\Http\ApiResponse;
use App\Model\Api\Exception\ApiException;
use App\Model\Exception\LogicException;

class IsTooFarException extends LogicException {

	public function __construct(
		private readonly float $distance,
		private readonly int $range,
	) {
		parent::__construct("Required position is too far, current distance $distance, max distance is " . $range);
	}

	public function getDistance() : float {
		return $this->distance;
	}

	public function getRange() : int {
		return $this->range;
	}

	public function toApiException() : ApiException {
		return ApiException::create()
			->withError('too-far')
			->withMessage($this->getMessage())
			->withCode(ApiResponse::S400_BAD_REQUEST)
			->withContext([
				'distance' => $this->distance,
				'range' => $this->range,
			]);
	}

}