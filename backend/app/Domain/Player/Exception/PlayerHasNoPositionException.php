<?php declare(strict_types = 1);

namespace App\Domain\Player\Exception;

use Apitte\Core\Http\ApiResponse;
use App\Model\Api\Exception\ApiException;
use App\Model\Exception\LogicException;

class PlayerHasNoPositionException extends LogicException {

	public function toApiException() : ApiException {
		return ApiException::create()
						   ->withError('no-position')
						   ->withMessage($this->getMessage())
						   ->withCode(ApiResponse::S400_BAD_REQUEST);
	}

}