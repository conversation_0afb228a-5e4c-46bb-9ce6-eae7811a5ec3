<?php declare(strict_types = 1);

namespace App\Domain\Player;

use App\Domain\User\User;
use App\Model\Database\EntityManager;

class CreatePlayerFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	public function createPlayer(User $user, string $nickname) : Player {
		$player = new Player($user, $nickname);
		$this->em->persist($player);
		$this->em->flush();

		return $player;
	}

}