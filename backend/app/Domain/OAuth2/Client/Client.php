<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Client;

use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TId;
use App\Model\Utils\Caster;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Entities\Traits\ClientTrait;


#[ORM\Entity(repositoryClass: ClientRepository::class)]
#[ORM\Table(name: 'api_client')]
class Client extends AbstractEntity implements ClientEntityInterface {


	use ClientTrait;

	#[ORM\Id]
	#[ORM\Column(type: 'string', nullable: false)]
	protected string $identifier;

	#[ORM\Column(type: 'string', nullable: false)]
	protected string $name;

	#[ORM\Column(type: 'string', nullable: false)]
	protected string $redirectUri;

	#[ORM\Column(type: 'string', nullable: true)]
	protected ?string $secret = NULL;

	#[ORM\Column(type: 'boolean', nullable: false, options: ['default' => FALSE])]
	protected bool $isConfidential = FALSE;

	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '["password", "refresh_token"]'])]
	protected array $grantTypes = ['password', 'refresh_token'];

	public function __construct(string $name, string $redirectUri) {
		$this->name = $name;
		$this->redirectUri = $redirectUri;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	/**
	 * @inheritDoc
	 */
	public function getRedirectUri() : string|array {
		if (str_contains($this->redirectUri, PHP_EOL)) {
			return explode(PHP_EOL, $this->redirectUri);
		}

		return $this->redirectUri;
	}

	public function setRedirectUri(string $redirectUri) : void {
		$this->redirectUri = $redirectUri;
	}

	public function getSecret() : ?string {
		return $this->secret;
	}

	public function setSecret(?string $secret) : void {
		$this->secret = $secret;
	}

	/**
	 * @inheritDoc
	 */
	public function isConfidential() : bool {
		return $this->isConfidential;
	}

	public function setIsConfidential(bool $isConfidential) : void {
		$this->isConfidential = $isConfidential;
	}

	/**
	 * @return string[]
	 */
	public function getGrantTypes() : array {
		return $this->grantTypes;
	}

	/**
	 * @param string[] $grantTypes
	 */
	public function setGrantTypes(array $grantTypes) : void {
		$this->grantTypes = $grantTypes;
	}

	public function getIdentifier() : string {
		return $this->identifier;
	}

}