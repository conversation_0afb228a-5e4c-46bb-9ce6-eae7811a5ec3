<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\AccessToken;

use App\Domain\OAuth2\Client\Client;
use App\Model\Database\Repository\AbstractRepository;
use League\OAuth2\Server\Entities\AccessTokenEntityInterface;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Repositories\AccessTokenRepositoryInterface;

/**
 * @method AccessToken|null find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method AccessToken|null findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL)
 * @method AccessToken[] findAll()
 * @method AccessToken[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 * @extends AbstractRepository<AccessToken>
 */
class AccessTokenRepository extends AbstractRepository implements AccessTokenRepositoryInterface {

	/**
	 * @inheritDoc
	 * @param ClientEntityInterface $clientEntity
	 */
	public function getNewToken(ClientEntityInterface $clientEntity, array $scopes, $userIdentifier = NULL) : AccessToken {
		// Cast ClientEntityInterface to our Client class
		if (!$clientEntity instanceof Client) {
			throw new \InvalidArgumentException('Client entity must be an instance of App\Domain\OAuth2\Client\Client');
		}
		return new AccessToken($clientEntity, $scopes, $userIdentifier);
	}

	/**
	 * @inheritDoc
	 *
	 * @param AccessTokenEntityInterface $accessTokenEntity
	 */
	public function persistNewAccessToken(AccessTokenEntityInterface $accessTokenEntity) : void {
		$this->getEntityManager()->persist($accessTokenEntity);
		$this->getEntityManager()->flush();
	}

	/**
	 * @inheritDoc
	 */
	public function revokeAccessToken($tokenId) : void {
		$accessToken = $this->find($tokenId);
		if ($accessToken === NULL) {
			return;
		}

		$this->getEntityManager()->remove($accessToken);
	}

	/**
	 * @inheritDoc
	 */
	public function isAccessTokenRevoked($tokenId) : bool {
		return $this->find($tokenId) === NULL;
	}

}