<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Scope;

use App\Model\Database\Entity\AbstractEntity;
use League\OAuth2\Server\Entities\ScopeEntityInterface;

use League\OAuth2\Server\Entities\Traits\ScopeTrait;

class Scope extends AbstractEntity implements ScopeEntityInterface {

	use ScopeTrait;

	private string $identifier;

	public function __construct(string $identifier) {
		$this->identifier = $identifier;
	}

	public function getIdentifier() : string {
		return $this->identifier;
	}

}