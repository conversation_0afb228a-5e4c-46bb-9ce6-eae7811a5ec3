<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Scope;

use App\Model\Database\Entity\AbstractEntity;
use League\OAuth2\Server\Entities\ScopeEntityInterface;
use League\OAuth2\Server\Entities\Traits\EntityTrait;
use League\OAuth2\Server\Entities\Traits\ScopeTrait;

class Scope extends AbstractEntity implements ScopeEntityInterface {

	use EntityTrait;
	use ScopeTrait;

	public function __construct(string $identifier) {
		$this->identifier = $identifier;
	}

}