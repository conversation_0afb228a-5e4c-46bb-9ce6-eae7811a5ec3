<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Scope;

use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Repositories\ScopeRepositoryInterface;

class ScopeRepository implements ScopeRepositoryInterface {

	/**
	 * @inheritDoc
	 */
	public function getScopeEntityByIdentifier($identifier) : Scope {
		return new Scope($identifier);
	}

	/**
	 * @inheritDoc
	 * @return Scope[]
	 */
	public function finalizeScopes(
		array $scopes,
		$grantType,
		ClientEntityInterface $clientEntity,
		$userIdentifier = NULL
	) : array {
		return array_map(fn($scope) => $scope instanceof Scope ? $scope : new Scope($scope->getIdentifier()), $scopes);
	}

}