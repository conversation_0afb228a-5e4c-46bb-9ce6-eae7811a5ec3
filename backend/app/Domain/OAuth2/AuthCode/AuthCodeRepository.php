<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\AuthCode;

use League\OAuth2\Server\Entities\AuthCodeEntityInterface;
use League\OAuth2\Server\Repositories\AuthCodeRepositoryInterface;

class AuthCodeRepository implements AuthCodeRepositoryInterface {

	/**
	 * @inheritDoc
	 */
	public function getNewAuthCode() {
		// TODO: Implement getNewAuthCode() method.
		throw new \Exception('Not implemented');
	}

	/**
	 * @inheritDoc
	 */
	public function persistNewAuthCode(AuthCodeEntityInterface $authCodeEntity) {
		// TODO: Implement persistNewAuthCode() method.
	}

	/**
	 * @inheritDoc
	 */
	public function revokeAuthCode($codeId) {
		// TODO: Implement revokeAuthCode() method.
	}

	/**
	 * @inheritDoc
	 */
	public function isAuthCodeRevoked($codeId) {
		// TODO: Implement isAuthCodeRevoked() method.
		return false;
	}
}