<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\RefreshToken;

use App\Domain\OAuth2\AccessToken\AccessToken;
use App\Model\Database\Entity\AbstractEntity;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\RefreshTokenEntityInterface;
use League\OAuth2\Server\Entities\Traits\EntityTrait;
use League\OAuth2\Server\Entities\Traits\RefreshTokenTrait;

#[ORM\Entity(repositoryClass: RefreshTokenRepository::class)]
#[ORM\Table(name: 'api_refresh_token')]
class RefreshToken extends AbstractEntity implements RefreshTokenEntityInterface{

	use EntityTrait;
	use RefreshTokenTrait;

	#[ORM\Id]
	#[ORM\Column(type: 'string', nullable: false)]
	protected $identifier;

	#[ORM\ManyToOne(targetEntity: AccessToken::class)]
	#[ORM\JoinColumn(name: 'access_token_id', referencedColumnName: 'identifier', nullable: false, onDelete: 'CASCADE')]
	protected $accessToken;

	/**
	 * @var DateTimeImmutable
	 */
	#[ORM\Column(type: 'datetime_immutable', nullable: false)]
	protected $expiryDateTime;

}