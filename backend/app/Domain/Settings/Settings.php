<?php declare(strict_types = 1);

namespace App\Domain\Settings;

use App\Model\Utils\Caster;
use Nette\Utils\Json;

abstract class Settings {

	public const string PLAYER_POSITION_MAX_AGE 	= 'player_position_max_age';
	public const string GAME_PASS_SALT 				= 'game_pass_salt';
	public const string PLAYER_DEFAULT_RESOURCES 	= 'player_default_resources';
	public const string UNIVERSAL_PASS 				= 'universal_pass';
	public const string MIN_BUILDING_DISTANCE 		= 'min_building_distance';

	const array SETTINGS = [
		self::PLAYER_POSITION_MAX_AGE 	=> '3600',
		self::GAME_PASS_SALT 			=> 'chcesemisrat',
		self::PLAYER_DEFAULT_RESOURCES 	=> '{"food":500,"wood":500,"clay":500,"stone":500,"iron":500,"gold":500}',
		self::UNIVERSAL_PASS 			=> 'hovinko',
		self::MIN_BUILDING_DISTANCE 	=> '15',
	];

	public static function get(string $key) : string {
		return self::SETTINGS[$key];
	}

	public static function getInt(string $key) : int {
		return Caster::toInt(self::SETTINGS[$key]);
	}

	public static function getFloat(string $key) : float {
		return Caster::toFloat(self::SETTINGS[$key]);
	}

	/**
	 * @return array<string, mixed>
	 */
	public static function getArray(string $key) : array {
		$decoded = Json::decode(Caster::toString(self::SETTINGS[$key]), TRUE);
		return is_array($decoded) ? $decoded : [];
	}

}