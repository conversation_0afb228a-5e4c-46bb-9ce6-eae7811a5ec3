<?php declare(strict_types = 1);

namespace App\Domain\Resource;

enum ResourceType : string {

	case FOOD = 'food';
	case WOOD = 'wood';
	case CLAY = 'clay';
	case STONE = 'stone';
	case IRON = 'iron';
	case GOLD = 'gold';

	private const NAMES = [
		self::FOOD->value => 'Jídlo',
		self::WOOD->value => 'Dřevo',
		self::CLAY->value => 'Jíl',
		self::STONE->value => 'Kámen',
		self::IRON->value => 'Ž<PERSON><PERSON>',
		self::GOLD->value => 'Zlato',
	];

	/**
	 * @return ResourceType[]
	 */
	public static function getAll() : array {
		return [
			self::FOOD,
			self::WOOD,
			self::CLAY,
			self::STONE,
			self::IRON,
			self::GOLD,
		];
	}

	public function getName() : string {
		return self::NAMES[$this->value];
	}

	/**
	 * @return array{slug:string, name:string}
	 */
	public function toArray() : array {
		return [
			'slug' => $this->value,
			'name' => $this->getName(),
		];
	}

}