<?php declare(strict_types = 1);

namespace App\Domain\Hero;

use App\Domain\Hero\Repository\HeroItemRepository;
use App\Domain\Item\Item;
use App\Domain\Item\ItemType;
use App\Model\Database\Entity\AbstractEntity;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: HeroItemRepository::class)]
#[ORM\Table(name: 'game_hero_item')]
#[ORM\UniqueConstraint(name: 'hero_item_slot', fields: ['hero', 'slotKey'])]
class HeroItem extends AbstractEntity {

	public const string INVENTORY_PREFIX = 'i';
	public const array EQUIPMENT_KEYS = [
		'weapon',
		'ring1',
		'ring2',
		'head',
		'necklace',
		'chest',
		'legs',
		'feet',
	];

	public const array EQUIPMENT_ITEMS = [
		'weapon' 	=> [ItemType::WEAPON],
		'ring1' 	=> [ItemType::RING],
		'ring2' 	=> [ItemType::RING],
		'head' 		=> [ItemType::HAT],
		'necklace' 	=> [ItemType::NECKLACE],
		'chest' 	=> [ItemType::ARMOR],
		'legs' 		=> [ItemType::ARMOR],
		'feet' 		=> [ItemType::BOOTS],
	];

	#[ORM\ManyToOne(targetEntity: Hero::class, inversedBy: 'items')]
	#[ORM\JoinColumn(name: 'hero_id', referencedColumnName: 'id', nullable: false)]
	private Hero $hero;

	#[ORM\Id]
	#[ORM\OneToOne(targetEntity: Item::class)]
	#[ORM\JoinColumn(name: 'item_id', referencedColumnName: 'id', nullable: false)]
	private Item $item;

	#[ORM\Column(type: 'string', nullable: false)]
	private string $slotKey;

	public function __construct(Hero $hero, Item $itemInstance, string $slotKey) {
		$this->hero = $hero;
		$this->item = $itemInstance;
		$this->slotKey = $slotKey;
	}

	public function getHero() : Hero {
		return $this->hero;
	}

	public function setHero(Hero $hero) : void {
		$this->hero = $hero;
	}

	public function getItem() : Item {
		return $this->item;
	}

	public function setItem(Item $itemInstance) : void {
		$this->item = $itemInstance;
	}

	public function getSlotKey() : string {
		return $this->slotKey;
	}

	public function setSlotKey(string $slotKey) : void {
		$this->slotKey = $slotKey;
	}

	public function isInventorySlot() : bool {
		return self::isInventorySlotKey($this->slotKey);
	}

	public function isEquipmentSlot() : bool {
		return self::isEquipmentSlotKey($this->slotKey);
	}

	// ----- Static methods --------------------------------------------------------------------------------------------

	public static function isInventorySlotKey(string $slotKey) : bool {
		return str_starts_with($slotKey, self::INVENTORY_PREFIX);
	}

	public static function isEquipmentSlotKey(string $slotKey) : bool {
		return in_array($slotKey, self::EQUIPMENT_KEYS, true);
	}

}