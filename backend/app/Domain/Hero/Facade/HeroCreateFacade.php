<?php declare(strict_types = 1);

namespace App\Domain\Hero\Facade;

use App\Domain\Hero\Hero;
use App\Domain\Player\Player;
use App\Model\Database\EntityManager;

class HeroCreateFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	public function createHero(Player $player, string $name) : Hero {
		$hero = new Hero($player, $name);
		$this->em->persist($hero);

		$player->setHero($hero);

		$this->em->flush();

		return $hero;
	}

}