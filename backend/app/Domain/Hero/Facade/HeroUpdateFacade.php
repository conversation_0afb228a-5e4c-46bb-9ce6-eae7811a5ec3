<?php declare(strict_types = 1);

namespace App\Domain\Hero\Facade;

use App\Domain\Hero\Hero;
use App\Model\Database\EntityManager;

class HeroUpdateFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	public function updateHero(Hero $hero, ?string $name = NULL) : Hero {
		if ($name !== NULL) {
			$hero->setName($name);
		}

		$this->em->flush();

		return $hero;
	}

}