<?php declare(strict_types = 1);

namespace App\Domain\Hero;

use App\Domain\Item\Item;
use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class HeroItemQuery extends AbstractQuery {

	public static function ofHero(Hero $hero) : self {
		$self = new self();
		$self->ons[] = function (QueryBuilder $qb) use ($hero) : QueryBuilder {
			$qb->andWhere('pi.hero = :hero')
			   ->setParameter('hero', $hero);

			return $qb;
		};

		return $self;
	}

	public function withUuid(string $uuid) : self {
		$this->ons[] = function (QueryBuilder $qb) use ($uuid) : QueryBuilder {
			$qb->andWhere('i.id = :uuid')
			   ->setParameter('uuid', $uuid);

			return $qb;
		};

		return $this;
	}

	public function withSlotKey(string $slotKey) : self {
		$this->ons[] = function (QueryBuilder $qb) use ($slotKey) : QueryBuilder {
			$qb->andWhere('pi.slotKey = :slotKey')
			   ->setParameter('slotKey', $slotKey);

			return $qb;
		};

		return $this;
	}

	public function withEquipSlot() : self {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->andWhere('pi.slotKey IN (:slotKeys)')
			   ->setParameter('slotKeys', HeroItem::EQUIPMENT_KEYS);

			return $qb;
		};

		return $this;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('pi')
			   ->from(HeroItem::class, 'pi')
			   ->join(Item::class, 'i', 'WITH', 'pi.item = i');

			return $qb;
		};
	}

}
