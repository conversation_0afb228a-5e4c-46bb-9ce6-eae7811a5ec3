<?php declare(strict_types = 1);

namespace App\Domain\Hero;

use App\Domain\Combat\ICombatable;
use App\Domain\Combat\TCombatStatsWithBonus;
use App\Domain\Combat\THealthStats;
use App\Domain\Combat\TLevelStats;
use App\Domain\Item\Item;
use App\Domain\Player\Player;
use App\Domain\Unit\Unit;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_hero')]
#[ORM\HasLifecycleCallbacks]
class Hero extends AbstractEntity implements ICombatable {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;

	use THealthStats;
	use TCombatStatsWithBonus;
	use TLevelStats;

	const int EXPERIENCE_BASE = 500;

	#[ORM\OneToOne(inversedBy: 'hero', targetEntity: Player::class)]
	#[ORM\JoinColumn(name: 'player_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
	private Player $player;

	#[ORM\Column(type: 'string')]
	private string $name;

	#[ORM\Column(type: 'string', nullable: false, enumType: HeroSexType::class, options: ['default' => HeroSexType::NONE])]
	private HeroSexType $sex = HeroSexType::NONE;

	#[ORM\Column(type: 'string', nullable: true)]
	private ?string $description = NULL;

	/** @var Collection<string, HeroItem> */
	#[ORM\OneToMany(mappedBy: 'hero', targetEntity: HeroItem::class, cascade: ['persist'], orphanRemoval: true, indexBy: 'slotKey')]
	private Collection $items;

	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 25])]
	private int $maxItems = 25;

	#[ORM\Column(type: 'integer', options: ['default' => 100])]
	private int $maxHealth = 100;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $maxHealthBonus = 0;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $healingBonus = 0;
   
    #[ORM\Column(type: 'integer', options: ['default' => 10])]
    private int $maxUnits = 10;
   
    /**
     * @var Collection<int, Unit>
     */
    #[ORM\OneToMany(mappedBy: 'hero', targetEntity: Unit::class)]
    private Collection $units;
   
    public function __construct(Player $player, string $name) {
    	$this->player = $player;
    	$this->name = $name;
    	$this->units = new ArrayCollection();

		$this->items = new ArrayCollection();
	}

	public function getMaxUnits(): int
	{
		return $this->maxUnits;
	}

	public function setMaxUnits(int $maxUnits): void
	{
		$this->maxUnits = $maxUnits;
	}

	/**
	 * @return Collection<int, Unit>
	 */
	public function getUnits(): Collection
	{
		return $this->units;
	}

	public function addUnit(Unit $unit): void
	{
		if (!$this->units->contains($unit)) {
			$this->units->add($unit);
			$unit->setHero($this);
		}
	}
	
	public function removeUnit(Unit $unit): void
	{
		if ($this->units->contains($unit)) {
			$this->units->removeElement($unit);
			$unit->setHero(null);
		}
	}

	public function getPlayer() : Player {
		return $this->player;
	}

	public function getName() : string {
		return $this->name;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	public function getDescription() : ?string {
		return $this->description;
	}

	public function setDescription(?string $description) : void {
		$this->description = $description;
	}

	public function getSex() : HeroSexType {
		return $this->sex;
	}

	public function setSex(HeroSexType $sex) : void {
		$this->sex = $sex;
	}

	public function getExperienceBase() : int {
		return self::EXPERIENCE_BASE;
	}

	public function getStrength() : int {
		return $this->strength;
	}

	public function getMaxHealth() : int {
		return $this->maxHealth;
	}

	public function setMaxHealth(int $maxHealth) : void {
		$this->maxHealth = $maxHealth;
	}

    public function setMaxHealthBonus(int $maxHealthBonus) : void {
        $this->maxHealthBonus = $maxHealthBonus;
    }

	public function getMaxHealthBonus() : int {
		return $this->maxHealthBonus;
	}

    public function setHealingBonus(int $healingBonus) : void {
        $this->healingBonus = $healingBonus;
    }

	public function getHealingBonus() : int {
		return $this->healingBonus;
	}

	public function resetBonuses(): void
	{
		$this->setAgilityBonus(0);
		$this->setStrengthBonus(0);
		$this->setDefenseBonus(0);

		$this->setMaxHealthBonus(0);
		$this->setHealingBonus(0);
	}


	// ----- Items -----------------------------------------------------------------------------------------------------

	/**
	 * @return Collection<string, HeroItem>
	 */
	public function getItems() : Collection {
		return $this->items;
	}

	public function addItem(Item $item, ?string $slotKey = NULL) : HeroItem {
		$slotKey = $slotKey ?? $this->findEmptyInventorySlotKey();
		if ($slotKey === NULL) {
			throw new \InvalidArgumentException('No empty slot available');
		}

		$playerItem = new HeroItem($this, $item, $slotKey);

		$this->items->add($playerItem);

		return $playerItem;
	}

	public function removeItem(HeroItem $playerItem) : void {
		$this->items->removeElement($playerItem);
	}

	public function removeItemBySlotKey(string $slotKey) : bool {
		$playerItem = $this->findItemBySlotKey($slotKey);
		if ($playerItem === NULL) {
			return FALSE;
		}

		$this->removeItem($playerItem);

		return TRUE;
	}

	private function findEmptyInventorySlotKey() : ?string {
		$slots = [];
		foreach ($this->items as $playerItem) {
			$slots[$playerItem->getSlotKey()] = TRUE;
		}

		for ($i = 0; $i < $this->maxItems; $i++) {
			$slotKey = HeroItem::INVENTORY_PREFIX . $i;
			if (!isset($slots[$slotKey])) {
				return $slotKey;
			}
		}

		return NULL;
	}

	private function findItemBySlotKey(string $slotKey) : ?HeroItem {
		return $this->items->get($slotKey);
	}

	public function getMaxItems() : int {
		return $this->maxItems;
	}

	public function setMaxItems(int $maxItems) : void {
		$this->maxItems = $maxItems;
	}

	// ----- Items end -------------------------------------------------------------------------------------------------
}
