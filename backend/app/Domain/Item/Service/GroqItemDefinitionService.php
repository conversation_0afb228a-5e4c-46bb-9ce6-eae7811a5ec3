<?php declare(strict_types = 1);

namespace App\Domain\Item\Service;

use LucianoTonet\GroqPHP\Groq;
use LucianoTonet\GroqPHP\GroqException;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use <PERSON>\Debugger;

class GroqItemDefinitionService {

	private Groq $groq;

	private string $systemMessage = '
You are generator of items for game. Item definition must be in JSON format like: 
{
    "name": "Item name", 
    "type": "item_type", 
    "category": "item_category", 
    "description": "Item description", 
    "spawnable": true,
    "commonness": 15,
    "minLevel": 1,
    "attributes": {
        "agility": 2,
    },
}

### Values ###
 - type: weapon, armor, hat, necklace, ring, boots, none
 - category: common, rare, special, collectible
 - attribute: agility, strength, defense, max_health, healing
 - commonness: from 1 to 100

### Rules ###
 - Some items spawn in the world, most of the time they\'re more common. Few rare items can just appear.
 - The item type must be adequate to the description.
 - The item can be restricted to players up to a certain level. The default value is 1, the maximum is 50. Items with better properties are at a higher level
 - Some items can modify the stats of the player who wears them, both positively and negatively (heavy armor is durable, but it also slows you down) This is done with attributes, an item can have more attributes or none.
 - User provide you short description and you generate others.  
 - Make interesting but descriptive names. Names must make sense and not be too long
 - Name and description must be in Czech. Be careful of the correct use of language

Answer only formatted JSON data. 
	';

	public function __construct(string $apiKey) {
		$this->groq = new Groq($apiKey);
	}

	/**
	 * @return array<string, mixed>|null
	 */
	public function generate(string $prompt) : ?array {
		try {
			$response = $this->groq->chat()->completions()->create([
				'model' => 'deepseek-r1-distill-llama-70b',
				'messages' => [
					[
						'role' => 'system',
						'content' => $this->systemMessage,
					],
					[
						'role' => 'user',
						'content' => $prompt,
					],
				],
				'response_format' => ['type' => 'json_object']
			]);

			if (!is_array($response) || !isset($response['choices'][0]['message']['content'])) {
				throw new GroqException('Invalid response format', 0, '');
			}

			$content = $response['choices'][0]['message']['content'];
			if (!is_string($content)) {
				throw new GroqException('Invalid content type', 0, '');
			}

			$decoded = Json::decode($content, forceArrays: TRUE);
			return is_array($decoded) ? $decoded : null;

		} catch (JsonException|GroqException $err) {
			Debugger::log($err);

			return NULL;
		}
	}

}