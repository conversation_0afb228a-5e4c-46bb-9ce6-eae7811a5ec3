<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Domain\Region\Region;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TPosition;
use App\Model\Utils\Position;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ItemPlacedRepository::class)]
#[ORM\Table(name: 'game_item_placed')]
class ItemPlaced extends AbstractEntity {

	use TPosition;

	#[ORM\Id]
	#[ORM\OneToOne(targetEntity: Item::class)]
	#[ORM\JoinColumn(name: 'item_id', nullable: false, onDelete: 'CASCADE')]
	private Item $item;

	#[ORM\ManyToOne(targetEntity: Region::class)]
	#[ORM\JoinColumn(name: 'region_id', referencedColumnName: 'id', nullable: false)]
	private Region $region;

	public function __construct(Item $itemInstance, Position $position, Region $region) {
		$this->item = $itemInstance;
		$this->setPosition($position);
		$this->region = $region;
	}

	public function getRegion() : Region {
		return $this->region;
	}

	public function getItem() : Item {
		return $this->item;
	}

}