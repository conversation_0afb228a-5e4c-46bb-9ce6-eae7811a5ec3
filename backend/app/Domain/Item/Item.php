<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TUuid;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_item')]
class Item extends AbstractEntity {

	use TUuid;

	#[ORM\ManyToOne(targetEntity: ItemDefinition::class)]
	#[ORM\JoinColumn(name: 'item_definition_id', nullable: false, onDelete: 'CASCADE')]
	private ItemDefinition $itemDefinition;

	#[ORM\Column(type: 'integer', nullable: false)]
	protected int $durability = 100;

	public function __construct(ItemDefinition $item) {
		$this->itemDefinition = $item;
	}

	public function getItemDefinition() : ItemDefinition {
		return $this->itemDefinition;
	}

	public function getDurability() : int {
		return $this->durability;
	}

	public function setDurability(int $durability) : void {
		$this->durability = $durability;
	}

}