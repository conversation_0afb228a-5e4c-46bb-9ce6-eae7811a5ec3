<?php declare(strict_types = 1);

namespace App\Domain\Item\Facade;

use App\Domain\Hero\Hero;
use App\Domain\Hero\HeroItem;
use App\Domain\Item\ItemPlaced;
use App\Domain\Player\PlayerPositionProvider;
use App\Model\Database\EntityManager;
use Tracy\Debugger;

class PickUpItemFacade {

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {}

	public function pickUpItem(Hero $hero, ItemPlaced $itemPlaced) : ?HeroItem {
		$player = $hero->getPlayer();

		$this->playerPositionProvider->validateRange($player, $itemPlaced->getPosition(), $player->getRange());

		try {
			$this->em->beginTransaction();

			$playerItem = $hero->addItem($itemPlaced->getItem());

			$this->em->remove($itemPlaced);
			$this->em->flush();

			$this->em->commit();
		} catch (\Throwable $e) {
			$this->em->rollback();

			Debugger::log($e);

			return NULL;
		}

		return $playerItem;
	}

}