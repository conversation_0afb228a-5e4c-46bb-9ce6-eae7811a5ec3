<?php declare(strict_types = 1);

namespace App\Domain\Item\Facade;

use App\Domain\Hero\Hero;
use App\Domain\Hero\HeroItem;
use App\Domain\Hero\HeroItemQuery;
use App\Domain\Item\ItemAttribute;
use App\Model\Api\Exception\ApiException;
use App\Model\Database\EntityManager;
use App\Model\Database\QueryManager;

class MoveItemFacade {

	public function __construct(
		private readonly QueryManager 	$qm,
		private readonly EntityManager 	$em,
	) {}

	public function moveItem(Hero $hero, string $from, string $to) : void {
		if (!$this->validateSlotKey($hero, $from) || !$this->validateSlotKey($hero, $to)) {
			throw new ApiException("Invalid slot key");
		}

		/** @var HeroItem|null $fromItem */
		$fromItem = $this->qm->findOne(HeroItemQuery::ofHero($hero)->withSlot<PERSON>ey($from));

		if ($fromItem === null) {
			throw new ApiException("Nothing to move");
		}

		/** @var HeroItem|null $toItem */
		$toItem = $this->qm->findOne(HeroItemQuery::ofHero($hero)->withSlotKey($to));

		try {
			$this->em->beginTransaction();

			$fromItem->setSlotKey($to);

			$toItem?->setSlotKey($from);

			$this->em->flush();

			$this->recalculateHeroAttributes($hero);

			$this->em->flush();
			$this->em->commit();
		}
		catch (\Throwable $e) {
			$this->em->rollback();
			throw $e;
		}
	}

	private function validateSlotKey(Hero $hero, string $key) : bool {
		if (HeroItem::isInventorySlotKey($key)) {
			$index = (int) substr($key, 0, strlen(HeroItem::INVENTORY_PREFIX));
			return $index >= 0 && $index < $hero->getMaxItems();
		}

		return in_array($key, HeroItem::EQUIPMENT_KEYS, TRUE);
	}

	private function recalculateHeroAttributes(Hero $hero) : void {
		$heroItems = $this->qm->findAll(HeroItemQuery::ofHero($hero)->withEquipSlot());
		$hero->resetBonuses();

		if (!is_array($heroItems)) {
			return; // No items to process
		}

		foreach ($heroItems as $heroItem) {
			if (!$heroItem instanceof \App\Domain\Hero\HeroItem) {
				continue; // Skip invalid items
			}
			$attributes = $heroItem->getItem()->getItemDefinition()->getAttributes();

			foreach ($attributes as $key => $value) {
				$attribute = ItemAttribute::tryFrom($key);

				if ($attribute === NULL) {
					continue; // Skip unknown attributes
				}

				$modifier = $value;
				// Apply attribute bonus to hero
				switch ($attribute) {
					case ItemAttribute::AGILITY:
						$hero->setAgilityBonus($hero->getAgilityBonus() + $modifier);
						break;
					case ItemAttribute::STRENGTH:
						$hero->setStrengthBonus($hero->getStrengthBonus() + $modifier);
						break;
					case ItemAttribute::DEFENSE:
						$hero->setDefenseBonus($hero->getDefenseBonus() + $modifier);
						break;
					case ItemAttribute::MAX_HEALTH:
						$hero->setMaxHealthBonus($hero->getMaxHealthBonus() + $modifier);
						break;
					case ItemAttribute::HEALING:
						$hero->setHealingBonus($hero->getHealingBonus() + $modifier);
						break;
				}
			}
		}
	}
}
