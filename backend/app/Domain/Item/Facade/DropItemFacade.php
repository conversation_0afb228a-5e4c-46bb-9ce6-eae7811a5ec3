<?php declare(strict_types = 1);

namespace App\Domain\Item\Facade;

use App\Domain\Hero\Hero;
use App\Domain\Hero\HeroItem;
use App\Domain\Item\ItemPlaced;
use App\Model\Database\EntityManager;
use Tracy\Debugger;

class DropItemFacade {

	public function __construct(
		private readonly EntityManager 		$em,
		private readonly ItemSpawnFacade 	$itemSpawnFacade,
	) {}

	public function dropItem(Hero $hero, HeroItem $playerItem) : ?ItemPlaced {
		try {
			$this->em->beginTransaction();

			$itemPlaced = $this->itemSpawnFacade->spawnOnPlayerPos($playerItem->getItem(), $hero->getPlayer());

			$hero->removeItem($playerItem);
			$this->em->remove($playerItem);

			$this->em->flush();

			$this->em->commit();
		} catch (\Throwable $e) {
			$this->em->rollback();

			Debugger::log($e);

			return NULL;
		}

		return $itemPlaced;
	}

}