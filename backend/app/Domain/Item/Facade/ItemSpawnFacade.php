<?php declare(strict_types = 1);

namespace App\Domain\Item\Facade;

use App\Domain\Item\Item;
use App\Domain\Item\ItemDefinition;
use App\Domain\Item\ItemPlaced;
use App\Domain\Player\Exception\PlayerHasNoPositionException;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Region\Region;
use App\Model\Database\EntityManager;

class ItemSpawnFacade {

	const int MAX_ITEMS_IN_REGION = 10;

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {}

	public function spawnRandomInRegion(Region $region, bool $flush = TRUE) : void {
		$itemsCountInRegion = $this->em->getRepository(ItemPlaced::class)->count(['region' => $region]);

		if ($itemsCountInRegion >= self::MAX_ITEMS_IN_REGION) {
			return;
		}

		$randomItem = $this->em->getRepository(ItemDefinition::class)->findSpawnableRandomItem();

		if ($randomItem === null) {
			return;
		}

		$item = $randomItem->createItem();
		$this->em->persist($item);

		$randomPosition = $this->em->getRepository(Region::class)->getRandomPositionInRegion($region);

		$itemPlaced = new ItemPlaced($item, $randomPosition, $region);
		$this->em->persist($itemPlaced);

		if ($flush) {
			$this->em->flush();
		}
	}

	public function spawnOnPlayerPos(Item $item, Player $player, bool $flush = TRUE) : ItemPlaced {
		$position = $this->playerPositionProvider->getPosition($player);
		if ($position === null) {
			throw new PlayerHasNoPositionException('Player has no position or position is expired');
		}

		$region = $this->em->getRepository(Region::class)->findBaseRegionByPosition($position);
		if ($region === null) {
			throw new \InvalidArgumentException('Region not found');
		}

		$itemPlaced = new ItemPlaced($item, $position, $region);
		$this->em->persist($itemPlaced);

		if ($flush) {
			$this->em->flush();
		}

		return $itemPlaced;
	}

}