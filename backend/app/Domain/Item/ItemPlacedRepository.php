<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @extends AbstractRepository<ItemPlaced>
 *
 * @method ItemPlaced|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method ItemPlaced|null findOneBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null)
 * @method ItemPlaced[] findAll()
 * @method ItemPlaced[] findBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class ItemPlacedRepository extends AbstractRepository {

}