<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class ItemPlacedQuery extends AbstractQuery {

	public static function create() : self {
		return new self();
	}

	public function withUuid(string $uuid) : self {
		$this->ons[] = function (QueryBuilder $qb) use ($uuid) : QueryBuilder {
			$qb->andWhere('i.id = :uuid')
			   ->setParameter('uuid', $uuid);

			return $qb;
		};

		return $this;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('ip')
				->addSelect('id')
				->addSelect('i')
			    ->from(ItemPlaced::class, 'ip')
				->join('ip.item', 'i')
			    ->join('i.itemDefinition', 'id');

			return $qb;
		};
	}

}
