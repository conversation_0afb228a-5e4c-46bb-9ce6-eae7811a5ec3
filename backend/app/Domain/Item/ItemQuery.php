<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Domain\Building\Building;
use App\Domain\Player\Player;
use App\Model\Database\Query\AbstractQuery;
use Doctrine\ORM\QueryBuilder;

class ItemQuery extends AbstractQuery {

	public static function create() : self {
		return new self();
	}

	public function withUuid(string $uuid) : self {
		$this->ons[] = function (QueryBuilder $qb) use ($uuid) : QueryBuilder {
			$qb->andWhere('i.id = :uuid')
			   ->setParameter('uuid', $uuid);

			return $qb;
		};

		return $this;
	}

	public function setup() : void {
		$this->ons[] = function (QueryBuilder $qb) : QueryBuilder {
			$qb->select('i')
				->addSelect('id')
			    ->from(Item::class, 'i')
			    ->join(ItemDefinition::class, 'id', 'WITH', 'i.item = id');

			return $qb;
		};
	}

}
