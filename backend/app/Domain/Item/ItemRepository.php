<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @extends AbstractRepository<Item>
 * @method Item|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method Item|null findOneBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null)
 * @method Item[] findAll()
 * @method Item[] findBy(array<string, mixed> $criteria, array<string, mixed>|null $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class ItemRepository extends AbstractRepository {

}