<?php declare(strict_types = 1);

namespace App\Scheduler;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingRepository;
use App\Domain\Building\Facade\BuildingProductionFacade;
use App\Model\Database\EntityManager;
use App\Model\Scheduler\Job;
use Tracy\Debugger;

class BuildingProductionJob extends Job {

	private const BATCH_SIZE = 100;

	private BuildingRepository $buildingRepository;

	public function __construct(
		private readonly EntityManager $em,
		private readonly BuildingProductionFacade $buildingProductionFacade,
	) {
		$this->buildingRepository = $this->em->getRepository(Building::class);
	}

	public function run() : void {
		try {
			$offset = 0;
			while (TRUE) {
				$buildings = $this->buildingRepository->findToResourcesUpdate(self::BATCH_SIZE, $offset);
				if (count($buildings) === 0) {
					break;
				}

				$this->buildingProductionFacade->updateProductions($buildings);

				$offset += self::BATCH_SIZE;
			}
		} catch (\Throwable $e) {
			Debugger::log($e);
		}
	}

}