<?php declare(strict_types = 1);

namespace App\Scheduler;

use App\Domain\Unit\ProductionQueueItem;
use App\Domain\Unit\Unit;
use App\Model\Database\EntityManager;
use App\Model\Scheduler\Job;
use DateTime;
use Doctrine\ORM\EntityRepository;

class UnitProductionJob extends Job {
	/** @var EntityRepository<ProductionQueueItem> */
	private EntityRepository $queueItemRepository;

	public function __construct(
		private readonly EntityManager $entityManager,
	) {
		$this->queueItemRepository = $this->entityManager->getRepository(ProductionQueueItem::class);
	}

	public function run() : void {
		$now = new DateTime('now');
		$queueItems = $this->queueItemRepository->createQueryBuilder('qi')
			->where('qi.endTime <= :now')
			->setParameter('now', $now)
			->setMaxResults(100) // Limit to 100 items for processing
			->getQuery()
			->getResult();

		foreach ($queueItems as $queueItem) {
			/** @var ProductionQueueItem $queueItem */
			$unit = new Unit($queueItem->getUnitDefinition());
			$unit->setBuilding($queueItem->getBuilding());
			$unit->setHero(NULL);

			$this->entityManager->persist($unit);
			$this->entityManager->remove($queueItem);
		}

		$this->entityManager->flush();
	}
}