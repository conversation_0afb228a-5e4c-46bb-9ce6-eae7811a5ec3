<?php declare(strict_types = 1);

namespace App\Scheduler;

use App\Domain\Item\Facade\ItemSpawnFacade;
use App\Domain\Region\Region;
use App\Domain\Region\RegionLevel;
use App\Domain\Region\Repository\RegionRepository;
use App\Model\Database\EntityManager;
use App\Model\Scheduler\Job;
use Tracy\Debugger;

class ItemSpawningJob extends Job {

	private const int BATCH_SIZE = 1000;

	private RegionRepository $regionRepository;

	public function __construct(
		private readonly EntityManager $em,
		private readonly ItemSpawnFacade $itemSpawningFacade,
	) {
		$this->regionRepository = $this->em->getRepository(Region::class);
	}

	public function run() : void {
		try {
			$offset = 0;
			while (TRUE) {
				$regions = $this->regionRepository->findBy(['level' => RegionLevel::lvl5], ['id' => 'ASC'], self::BATCH_SIZE, $offset);
				if (count($regions) === 0) {
					break;
				}

				foreach ($regions as $region) {
					$this->itemSpawningFacade->spawnRandomInRegion($region, FALSE);
				}

				$this->em->flush();

				$offset += self::BATCH_SIZE;
			}
		} catch (\Throwable $e) {
			Debugger::log($e);
		}
	}

}