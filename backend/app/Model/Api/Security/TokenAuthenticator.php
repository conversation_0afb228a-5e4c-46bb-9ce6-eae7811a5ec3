<?php declare(strict_types = 1);

namespace App\Model\Api\Security;

use App\Domain\User\User;
use App\Model\Database\EntityManager;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\ResourceServer;
use Psr\Http\Message\ServerRequestInterface;

class TokenAuthenticator extends AbstractAuthenticator {

	public function __construct(
		private readonly EntityManager $em,
		private readonly ResourceServer $server,
	) {}

	/**
	 * @throws OAuthServerException
	 */
	public function authenticate(ServerRequestInterface $request) : ?User {
		$request = $this->server->validateAuthenticatedRequest($request);
		$userId = $request->getAttribute('oauth_user_id');

		return $this->em->getRepository(User::class)->find($userId);
	}

}
