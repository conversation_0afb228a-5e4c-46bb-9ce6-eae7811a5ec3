<?php declare(strict_types = 1);

namespace App\Model\Api\Exception;

use Apitte\Core\Http\ApiResponse;

class ApiException extends \Apitte\Core\Exception\ApiException {

	protected string $error;

	/**
	 * @param array<string, mixed>|null $context
	 */
	public function __construct(string $error = 'error', string $message = '', int $code = ApiResponse::S400_BAD_REQUEST, ?array $context = null) {
		parent::__construct($message, $code);
		$this->error = $error;
		$this->context = $context;
	}

	public function withError(string $error) : self {
		$this->error = $error;
		return $this;
	}

	public function getError() : string {
		return $this->error;
	}

}