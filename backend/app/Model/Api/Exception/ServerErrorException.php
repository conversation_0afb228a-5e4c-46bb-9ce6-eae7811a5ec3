<?php declare(strict_types = 1);

namespace App\Model\Api\Exception;

use Apitte\Core\Exception\Logical\InvalidArgumentException;
use Throwable;

class ServerErrorException extends ApiException {

	public static string $defaultMessage = 'Application encountered an internal error. Please try again later.';

	public function __construct(string $message = '', int $code = 500, ?array $context = null)
	{
		if ($code < 500 || $code > 599) {
			throw new InvalidArgumentException(sprintf('%s code could be only in range from 500 to 599', static::class));
		}

		parent::__construct('server_error', $message !== '' ? $message : static::$defaultMessage, $code, $context);
	}

}