<?php declare(strict_types = 1);

namespace App\Model\Api\ErrorHandler;

use Apitte\Core\Dispatcher\DispatchError;
use Apitte\Core\ErrorHandler\SimpleErrorHandler;
use Apitte\Core\Http\ApiResponse;
use App\Model\Api\Exception\ApiException;
use App\Model\Api\Exception\ServerErrorException;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Utils;
use Nette\Utils\Json;
use Tracy\Debugger;

class ThrowingErrorHandler extends SimpleErrorHandler {

	public function handle(DispatchError $dispatchError) : ApiResponse {
		$exception = $dispatchError->getError();

		if (method_exists($exception, 'toApiException')) {
			$exception = $exception->toApiException();
		}

		// Log only non-API exceptions
		if (!$exception instanceof ApiException) {
			Debugger::log($exception, Debugger::ERROR);
		}

		$code = $exception instanceof ApiException ? $exception->getCode() : 500;
		$error = $exception instanceof ApiException ? $exception->getError() : 'server_error';
		$message = $exception instanceof ApiException  || Debugger::isEnabled() ? $exception->getMessage() : ServerErrorException::$defaultMessage;

		$data = [
			'error'		=> $error,
			'message' 	=> $message,
		];

		if ($exception instanceof ApiException && ($context = $exception->getContext()) !== null) {
			$data['context'] = $context;
		}

		$body = Utils::streamFor(Json::encode($data));

		$response = new ApiResponse(new Response());

		return $response
			->withStatus($code)
			->withHeader('Content-Type', 'application/json')
			->withBody($body);
	}

}
