<?php declare(strict_types = 1);

namespace App\Model\Utils;

use App\Model\Exception\Runtime\InvalidStateException;

final class Caster {

	public static function toString(mixed $value) : string {
		if (is_string($value) || is_int($value) || is_float($value)) {
			return strval($value);
		}

		throw new InvalidStateException('Cannot cast to string');
	}

	public static function toNString(mixed $value) : ?string {
		return $value === NULL ? NULL : self::toString($value);
	}

	public static function toInt(mixed $value) : int {
		if (is_string($value) || is_int($value) || is_float($value)) {
			return intval($value);
		}

		throw new InvalidStateException('Cannot cast to integer');
	}

	public static function toNInt(mixed $value) : ?int {
		return $value === NULL ? NULL : self::toInt($value);
	}

	public static function toFloat(mixed $value) : float {
		if (is_string($value) || is_int($value) || is_float($value)) {
			return floatval($value);
		}

		throw new InvalidStateException('Cannot cast to float');
	}

	public static function toNFloat(mixed $value) : ?float {
		return $value === NULL ? NULL : self::toFloat($value);
	}

	public static function toBool(mixed $value) : bool {
		if (is_bool($value)) {
			return $value;
		}
		elseif (is_numeric($value)) {
			return $value > 0;
		}
		elseif (is_string($value)) {
			return $value === 'true';
		}

		throw new InvalidStateException('Cannot cast to bool');
	}

	public static function toNBool(mixed $value) : ?bool {
		return $value === NULL ? NULL : self::toBool($value);
	}

}
