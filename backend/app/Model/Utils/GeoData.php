<?php declare(strict_types = 1);

namespace App\Model\Utils;

final class GeoData {

	private const EARTH_RADIUS = 6371000.0;

	/**
	 * Calculate distance between two points on Earth
	 *
	 * @return float Distance in meters
	 */
	public static function distance(
		float $longitudeFrom,
		float $latitudeFrom,
		float $longitudeTo,
		float $latitudeTo,
	) : float {
		// Convert latitude and longitude from degrees to radians
		$lonFrom = deg2rad($longitudeFrom);
		$latFrom = deg2rad($latitudeFrom);
		$lonTo = deg2rad($longitudeTo);
		$latTo = deg2rad($latitudeTo);

		$lonDelta = $lonTo - $lonFrom;
		$latDelta = $latTo - $latFrom;

		// Haversine formula
		$angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) + cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));

		return $angle * self::EARTH_RADIUS;
	}

}