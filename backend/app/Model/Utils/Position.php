<?php declare(strict_types = 1);

namespace App\Model\Utils;

use App\Domain\Api\Request\PositionReqDto;

class Position implements \JsonSerializable {

	private float $longitude;

	private float $latitude;

	public function __construct(float $longitude, float $latitude) {
		$this->longitude = $longitude;
		$this->latitude = $latitude;
	}

	public static function create(float $lng, float $lat) : self {
		return new self($lng, $lat);
	}

	/**
	 * @param array{lng: string, lat: string} $data
	 */
	public static function fromArray(array $data) : self {
		if ($data['lng'] === '' || $data['lat'] === '') {
			throw new \InvalidArgumentException('Invalid position data');
		}

		return new self(Caster::toFloat($data['lng']), Caster::toFloat($data['lat']));
	}

	public static function fromDto(PositionReqDto $dto) : self {
		return new self($dto->lng, $dto->lat);
	}

	public function getLng() : float {
		return $this->longitude;
	}

	public function getLat() : float {
		return $this->latitude;
	}

	/**
	 * @return array{lng: float, lat: float}
	 */
	public function jsonSerialize() : array {
		return [
			'lng' => $this->longitude,
			'lat' => $this->latitude,
		];
	}

	public function distance(Position $playerPosition) : float {
		return GeoData::distance($this->longitude, $this->latitude, $playerPosition->getLng(), $playerPosition->getLat());
	}

}