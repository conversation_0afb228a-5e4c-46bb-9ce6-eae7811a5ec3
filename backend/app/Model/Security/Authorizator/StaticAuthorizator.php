<?php declare(strict_types = 1);

namespace App\Model\Security\Authorizator;

use App\Domain\User\User;
use App\Domain\User\UserRole;
use Nette\Security\Permission;

final class StaticAuthorizator extends Permission {

	/**
	 * Create ACL
	 */
	public function __construct() {
		$this->addRoles();
		$this->addResources();
		$this->addPermissions();
	}

	/**
	 * Setup roles
	 */
	protected function addRoles() : void {
		$this->addRole(UserRole::ADMIN->value);
	}

	/**
	 * Setup resources
	 */
	protected function addResources() : void {
		$this->addResource('Admin:Dashboard');
	}

	/**
	 * Setup ACL
	 */
	protected function addPermissions() : void {
		$this->allow(UserRole::ADMIN->value, [
			'Admin:Dashboard',
		]);
	}

}
