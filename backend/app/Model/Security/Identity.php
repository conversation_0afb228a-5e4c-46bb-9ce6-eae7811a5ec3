<?php declare(strict_types = 1);

namespace App\Model\Security;

use Nette\Security\SimpleIdentity as NetteIdentity;

class Identity extends NetteIdentity {

	/** @var array<string, mixed> */
	private array $userData;

	/**
	 * @param mixed $id
	 * @param mixed $roles
	 * @param array<string, mixed> $data
	 */
	public function __construct($id, $roles = null, array $data = []) {
		parent::__construct($id, $roles, $data);
		$this->userData = $data;
	}

	public function getFullname() : string {
		$name = $this->userData['name'] ?? '';
		$surname = $this->userData['surname'] ?? '';
		return sprintf('%s %s', (string) $name, (string) $surname);
	}

}
