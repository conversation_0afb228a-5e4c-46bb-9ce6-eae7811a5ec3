<?php declare(strict_types = 1);

namespace App\Model\Database\Entity;

use Doctrine\ORM\Mapping as ORM;
use <PERSON>\Uuid\Doctrine\UuidGenerator;
use <PERSON>\Uuid\UuidInterface;

trait TUuid {

	#[ORM\Id]
	#[ORM\GeneratedValue(strategy: 'CUSTOM')]
	#[ORM\Column(type: 'uuid', unique: true, nullable: false)]
	#[ORM\CustomIdGenerator(class: UuidGenerator::class)]
	private UuidInterface $id;

	public function getId() : UuidInterface {
		return $this->id;
	}

	public function __clone() {
		unset($this->id);
	}

}
