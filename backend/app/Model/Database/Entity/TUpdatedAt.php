<?php declare(strict_types = 1);

namespace App\Model\Database\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

trait TUpdatedAt {

	#[ORM\Column(type: 'datetime', nullable: TRUE)]
	protected ?DateTime $updatedAt = NULL;

	public function getUpdatedAt() : ?DateTime {
		return $this->updatedAt;
	}

	/**
	 * @internal
	 */
	#[ORM\PreUpdate]
	public function setUpdatedAt() : void {
		$this->updatedAt = new DateTime();
	}

}
