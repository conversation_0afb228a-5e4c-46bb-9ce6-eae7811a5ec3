<?php declare(strict_types = 1);

namespace App\Model\Database\Entity;

use App\Model\Utils\Position;
use Doctrine\ORM\Mapping as ORM;
use Jsor\Doctrine\PostGIS\Types\PostGISType;

/** @phpstan-ignore-next-line */
trait TNPosition {

	#[ORM\Column(
		type    : PostGISType::GEOMETRY,
		nullable: false,
		options : ['geometry_type' => 'POINT', 'srid' => 4326]
	)]
	protected ?string $position;

	public function getPosition() : ?Position {
		if ($this->position === NULL) {
			return NULL;
		}
		[$lng, $lat] = \sscanf($this->position, 'SRID=4326;POINT(%f %f)');
		return Position::create($lng, $lat);
	}

	public function setPosition(?Position $position) : void {
		if ($position === NULL) {
			$this->position = NULL;
			return;
		}

		$this->position = \sprintf('SRID=4326;POINT(%F %F)', $position->getLng(), $position->getLat());
	}

}
