<?php declare(strict_types = 1);

namespace App\API\OAuth;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\API\BaseController;
use GuzzleHttp\Psr7\Utils;
use League\OAuth2\Server\AuthorizationServer;
use League\OAuth2\Server\Exception\OAuthServerException;
use Psr\Http\Message\ResponseInterface;
use Throwable;

/**
 * @Apitte\Path("/oauth2")
 * @Apitte\Tag("OAuth2")
 */
class OAuthController extends BaseController {

	public function __construct(
		private readonly AuthorizationServer $authorizationServer,
	) {}

	/**
	 * @Apitte\Path("/access_token")
	 * @Apitte\Method("POST")
	 * @Apitte\OpenApi("
	 * 		summary: Authenticate user
	 * ")
	 */
	public function accessToken(ApiRequest $request, ApiResponse $response) : ResponseInterface {
		try {
			$response = $this->authorizationServer->respondToAccessTokenRequest($request, $response);
		} catch (OAuthServerException $exception) {
			$response = $exception->generateHttpResponse($response);
		} catch (Throwable $exception) {
			$body = Utils::streamFor('php://temp');
			$body->write($exception->getMessage());
			return $response->withStatus(500)->withBody($body);
		}

		return $response;
	}

}