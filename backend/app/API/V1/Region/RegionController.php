<?php declare(strict_types = 1);

namespace App\API\V1\Region;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\RegionFacade;
use App\Domain\Api\Response\RegionResDto;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/region")
 * @Apitte\Id("region")
 */
class RegionController extends BaseV1Controller {

	public function __construct(
		private readonly RegionFacade $mapRegionFacade,
	) {}


	/**
	 * @Apitte\Path("/{id}")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="id", type="int", in="path", description="Region ID", required=true),
	 * })
	 */
	public function get(ApiRequest $request) : RegionResDto {
		$id = Caster::toInt($request->getParameter('id'));
		$region = $this->mapRegionFacade->find($id);

		if ($region === null) {
			throw new NotFoundException('Region not found');
		}

		return $region;
	}

}