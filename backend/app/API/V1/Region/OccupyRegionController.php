<?php declare(strict_types = 1);

namespace App\API\V1\Region;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\OccupyRegionFacade;
use App\Domain\Api\Response\RegionResDto;

/**
 * @Apitte\Path("/occupy-region")
 * @Apitte\Id("occupy-region")
 */
class OccupyRegionController extends BaseV1Controller {

	public function __construct(
		private readonly OccupyRegionFacade $occupyRegionFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 *    summary: Occupy current region.
	 *  ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("POST")
	 */
	public function occupy(ApiRequest $request) : RegionResDto {
		$player = $this->getPlayer($request);
		return $this->occupyRegionFacade->occupyCurrentRegion($player);
	}

}