<?php declare(strict_types = 1);

namespace App\API\V1\Region;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\RegionFacade;
use App\Domain\Api\Response\RegionResDto;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/player-regions")
 * @Apitte\Id("region")
 */
class PlayerRegionsController extends BaseV1Controller {

	public function __construct(
		private readonly RegionFacade $mapRegionFacade,
	) {}


	/**
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET")
	 *
	 * @return RegionResDto[]
	 */
	public function index(ApiRequest $request) : array {
		return $this->mapRegionFacade->findByPlayer($this->getPlayer($request));
	}

}