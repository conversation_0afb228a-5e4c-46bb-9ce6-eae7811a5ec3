<?php declare(strict_types = 1);

namespace App\API\V1\Region;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\ResourceRegionFacade;
use App\Domain\Api\Response\ResourceRegionResDto;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/resource")
 * @Apitte\Id("resource")
 */
class ResourceRegionController extends BaseV1Controller {

	public function __construct(
		private readonly ResourceRegionFacade $resourceRegionFacade,
	) {}


	/**
	 * @Apitte\Path("/{id}")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="id", type="int", in="path", description="Resource ID", required=true),
	 * })
	 */
	public function get(ApiRequest $request) : ResourceRegionResDto {
		$id = Caster::toInt($request->getParameter('id'));
		$region = $this->resourceRegionFacade->find($id);

		if ($region === null) {
			throw new NotFoundException('Resource not found');
		}

		return $region;
	}

}