<?php declare(strict_types = 1);

namespace App\API\V1\Building;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\ResourceRegionFacade;
use App\Domain\Api\Response\ResourceInRangeResDto;
use App\Domain\Building\BuildingType;
use App\Domain\Building\BuildingTypeQuery;
use App\Model\Api\Exception\ApiException;
use App\Model\Api\Exception\NotFoundException;
use App\Model\Database\QueryManager;
use App\Model\Utils\Caster;
use App\Model\Utils\Position;

/**
 * @Apitte\Path("/building-type")
 * @Apitte\Id("building")
 */
class BuildingTypeController extends BaseV1Controller {

	public function __construct(
		private readonly QueryManager 			$qm,
		private readonly ResourceRegionFacade 	$createBuildingFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Resources in range of the building type on player's position
	 * ")
	 * @Apitte\Path("/{buildingType}/resources-in-range")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="buildingType", type="string",  description="Building type slug",       in="path",  required=true),
	 *     @Apitte\RequestParameter(name="lvl",          type="int",     description="Level of building",        in="query", required=false),
	 *     @Apitte\RequestParameter(name="position",     type="string",  description="Position of the building", in="query", required=false)
	 * })
	 * @return ResourceInRangeResDto[]
	 */
	public function resourcesInRange(ApiRequest $request) : array {
		$player = $this->getPlayer($request);

		/** @var array{lng:string, lat:string}|null $position */
		$position = $request->getParameter('position');
		if ($position !== null) {
			try {
				$position = Position::fromArray($position);
			} catch (\InvalidArgumentException) {
				throw new ApiException('invalid_position', 'Invalid position');
			}
		}

		/** @var BuildingType|null $buildingType */
		$buildingType = $this->qm->findOne(BuildingTypeQuery::ofSlug(Caster::toString($request->getParameter('buildingType'))));

		/** @var int $buildingLevel */
		$buildingLevel = $request->getParameter('lvl', 1);

		if ($buildingType === null) {
			throw new NotFoundException('Building type not found');
		}

		return $this->createBuildingFacade->findResourcesInRangeForBuild($player, $buildingType, $buildingLevel, $position);
	}

}