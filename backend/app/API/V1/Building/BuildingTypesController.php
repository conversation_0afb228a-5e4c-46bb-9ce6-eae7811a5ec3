<?php declare(strict_types = 1);

namespace App\API\V1\Building;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\BuildingTypeFacade;
use App\Domain\Api\Response\BuildingTypeResDto;

/**
 * @Apitte\Path("/building-types")
 * @Apitte\Id("building")
 */
class BuildingTypesController extends BaseV1Controller {

	public function __construct(
		private readonly BuildingTypeFacade	$buildingTypeFacade,
	) {}


	/**
	 * @Apitte\OpenApi("
	 * 		summary: List building types
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET")
	 * @Apitte\Responses({
	 * 		@Apitte\Response(code="200", entity=BuildingTypeResDto::class, description="List of building types"),
	 * })
	 *
	 * @return BuildingTypeResDto[]
	 */
	public function index(ApiRequest $request, ApiResponse $response) : array {
		return $this->buildingTypeFacade->findAll();
	}

}