<?php declare(strict_types = 1);

namespace App\API\V1\Player;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\PlayersFacade;
use App\Domain\Api\Response\PlayerResDto;
use App\Model\Exception\Runtime\Database\EntityNotFoundException;
use App\Model\Utils\Caster;
use Nette\Http\IResponse;

/**
 * @Apitte\Path("/players")
 * @Apitte\Tag("Players")
 */
class PlayersController extends BaseV1Controller {

	private PlayersFacade $playersFacade;

	public function __construct(PlayersFacade $playersFacade) {
		$this->playersFacade = $playersFacade;
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: List players.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 * 		@Apitte\RequestParameter(name="limit", type="int", in="query", required=false, description="Data limit"),
	 * 		@Apitte\RequestParameter(name="offset", type="int", in="query", required=false, description="Data offset")
	 * })
	 * @return PlayerResDto[]
	 */
	public function index(ApiRequest $request) : array {
		return $this->playersFacade->findAll(
			Caster::toInt($request->getParameter('limit', 10)),
			Caster::toInt($request->getParameter('offset', 0)),
		);
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Get player by nickname.
	 * ")
	 * @Apitte\Path("/nickname")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *      @Apitte\RequestParameter(name="nickname", in="query", type="string", description="Player nickname")
	 * })
	 */
	public function byNickname(ApiRequest $request) : PlayerResDto {
		try {
			return $this->playersFacade->findOneBy(['nickname' => $request->getParameter('nickname')]);
		}
		catch (EntityNotFoundException) {
			throw ClientErrorException::create()
									  ->withMessage('Player not found')
									  ->withCode(IResponse::S404_NotFound);
		}
	}

}