<?php declare(strict_types = 1);

namespace App\API\V1\Player;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Request\RegistrationReqDto;
use App\Domain\Service\RegistrationService;
use App\Domain\Settings\Settings;
use App\Domain\User\Exception\UserExistsException;
use App\Model\Api\Exception\ApiException;

/**
 * @Apitte\Path("/player-registration")
 * @Apitte\Tag("Player Registration")
 */
class RegistrationController extends BaseV1Controller {

	public function __construct(
		private readonly RegistrationService $registrationService,
	) {}

	/**
	 * @Apitte\OpenApi("
	 *  summary: Register new player.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("PUT")
	 * @Apitte\RequestBody(entity=RegistrationReqDto::class)
	 */
	public function index(ApiRequest $request, ApiResponse $response) : ApiResponse {
		/** @var RegistrationReqDto $dto */
		$dto = $request->getParsedBody();

		$universalPassword = Settings::get(Settings::UNIVERSAL_PASS);

		if ($universalPassword !== $dto->gamePassword && substr(md5($dto->email . Settings::get(Settings::GAME_PASS_SALT)), 6, 15) !== $dto->gamePassword) {
			throw new ApiException('registration_error', 'Invalid game password', ApiResponse::S400_BAD_REQUEST);
		}

		try {
			$this->registrationService->registerPlayer($dto);
		} catch (UserExistsException $e) {
			throw new ApiException('registration_error', $e->getMessage(), ApiResponse::S409_CONFLICT);
		}

		return $response->withStatus(ApiResponse::S201_CREATED);
	}

}