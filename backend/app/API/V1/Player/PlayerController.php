<?php declare(strict_types = 1);

namespace App\API\V1\Player;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\PlayerHearthBeatFacade;
use App\Domain\Api\Request\PlayerHearthBeatReqDto;
use App\Domain\Api\Response\CurrentPlayerResDto;
use App\Domain\Api\Response\HearthBeatResDto;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/player")
 * @Apitte\Tag("Player")
 */
class PlayerController extends BaseV1Controller {

	public function __construct(
		private readonly PlayerHearthBeatFacade $playerHearthBeatFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Get current player.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET"),
	 * @Apitte\Responses({
	 *       @Apitte\Response(code="200", description="Success"),
	 *  })
	 */
	public function index(ApiRequest $request) : CurrentPlayerResDto {
		$player = $this->getPlayer($request);
		return CurrentPlayerResDto::from($player);
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Sent player hearth beat.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("POST")
	 * @Apitte\RequestBody(entity=PlayerHearthBeatReqDto::class, description="Player hearth beat data", required=true,
	 *                                                           validation=true),
	 * @Apitte\Responses({
	 *      @Apitte\Response(code="200", description="Success", entity=HearthBeatResDto::class),
	 * })
	 */
	public function hearthBeat(ApiRequest $request) : HearthBeatResDto {
		$player = $this->getPlayer($request);

		/** @var PlayerHearthBeatReqDto $dto */
		$dto = $request->getParsedBody();

		$useAgent = $request->getHeader('User-Agent')[0] ?? '';
		$ip = Caster::toString($request->getServerParams()['REMOTE_ADDR'] ?? '');

		return $this->playerHearthBeatFacade->processPlayerHearthBeat($player, $dto, $useAgent, $ip);
	}

}