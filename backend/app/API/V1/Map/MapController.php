<?php declare(strict_types = 1);

namespace App\API\V1\Map;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\MapDataFacade;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/map")
 * @Apitte\Id("map")
 */
class MapController extends BaseV1Controller {

	public function __construct(
		private readonly MapDataFacade $mapDataFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 * summary: Get GeoJSON data for the given bounding box
	 * ")
	 * @Apitte\Path("/geojson")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="bbox", type="string", in="query", description="Bounding box in format 'xmin,ymin,xmax,ymax'", required=true),
	 * })
	 */
	public function geoJson(ApiRequest $request, ApiResponse $response) : ApiResponse {
		$player = $this->getPlayer($request);
		[$xMin, $yMin, $xMax, $yMax] = explode(',', Caster::toString($request->getParameter('bbox')));
		$data = $this->mapDataFacade->getGeoJsonByPlayerBbox($player, (float) $xMin, (float) $yMin, (float) $xMax, (float) $yMax);

		return $response
			->withHeader('Content-Type', 'application/json')
			->writeBody($data);
	}

}