<?php declare(strict_types = 1);

namespace App\API\V1\Test;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\User\User;

/**
 * @Apitte\Path("/test")
 * @Apitte\Tag("Test")
 */
class TestController extends BaseV1Controller {

	/**
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET")
	 *
	 * @return array{message: string, user: User}
	 */
	public function index(ApiRequest $request) : array {
		return [
			'message' => 'Test',
			'user' => $this->getUser($request),
		];
	}

}