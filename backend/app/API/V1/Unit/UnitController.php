<?php declare(strict_types = 1);

namespace App\API\V1\Unit;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\UnitFacade;
use App\Domain\Api\Response\UnitResDto;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/unit")
 * @Apitte\Id("unit")
 */
class UnitController extends BaseV1Controller {
	public function __construct(
		private readonly UnitFacade $unitFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 * summary: Find unit by ID
	 * ")
	 * @Apitte\Path("/{uuid}")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="uuid", type="string", description="Unit UUID", in="path", required=true),
	 * })
	 */
	public function get(ApiRequest $request) : UnitResDto {
		$player = $this->getPlayer($request);
		$uuid = Caster::toString($request->getParameter('uuid'));

		return $this->unitFacade->getOneForPlayer($uuid, $player);
	}

	/**
	 * @Apitte\OpenApi("
	 * summary: List of player's units
	 * ")
	 * @Apitte\Path("/list")
	 * @Apitte\Method("GET")
	 * @return array<UnitResDto>
	 */
	public function list(ApiRequest $request) : array {
		$player = $this->getPlayer($request);
		return $this->unitFacade->findAllByPlayer($player);
	}

}