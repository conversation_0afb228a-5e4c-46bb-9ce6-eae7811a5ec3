<?php declare(strict_types = 1);

namespace App\API\V1;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\API\BaseController;
use App\Domain\Player\Player;
use App\Domain\User\User;
use App\Model\Api\Exception\ApiException;
use App\Model\Api\RequestAttributes;

/**
 * @Apitte\Path("/v1")
 * @Apitte\Id("v1")
 */
abstract class BaseV1Controller extends BaseController {

	protected function tryGetUser(ApiRequest $request) : ?User {
		$user = $request->getAttribute(RequestAttributes::APP_LOGGED_USER);
		return $user instanceof User ? $user : NULL;
	}

	protected function getUser(ApiRequest $request) : User {
		$user = $this->tryGetUser($request);

		return $user ?? throw new ApiException('user_not_found', 'User not found', ApiResponse::S401_UNAUTHORIZED);
	}

	protected function getPlayer(ApiRequest $request) : Player {
		$user = $this->tryGetUser($request);

		return $user?->getPlayer() ?? throw new ApiException('player_not_found', 'Player not found', ApiResponse::S401_UNAUTHORIZED);
	}

}
