<?php declare(strict_types = 1);

namespace App\API\V1\Hero;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\Domain\Api\Facade\HeroFacade;
use App\Domain\Api\Request\CreateHeroReqDto;
use App\Domain\Api\Request\UpdateHeroReqDto;
use App\Domain\Api\Response\HeroResDto;

/**
 * @Apitte\Path("/")
 * @Apitte\Id("hero")
 */
class HeroController extends BaseHeroController {

	public function __construct(
		private readonly HeroFacade $heroFacade,
	) {}


	/**
	 * @Apitte\OpenApi("
	 *   summary: Get hero.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET"),
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=HeroResDto::class),
	 *     @Apitte\Response(code="400", description="Bad request"),
	 * })
	 */
	public function index(ApiRequest $request) : HeroResDto {
		$hero = $this->getHero($request);
		return HeroResDto::from($hero);
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Hire hero.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("PUT"),
	 * @Apitte\RequestBody(entity=CreateHeroReqDto::class, required=true),
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=HeroResDto::class),
	 *     @Apitte\Response(code="400", description="Bad request"),
	 * })
	 */
	public function hireHero(ApiRequest $request) : HeroResDto {
		$player = $this->getPlayer($request);

		/** @var CreateHeroReqDto $heroData */
		$heroData = $request->getParsedBody();

		return $this->heroFacade->createHero($player, $heroData);
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Update hero.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("POST"),
	 * @Apitte\RequestBody(entity=UpdateHeroReqDto::class, required=true),
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=HeroResDto::class),
	 *     @Apitte\Response(code="400", description="Bad request"),
	 * })
	 */
	public function updateHero(ApiRequest $request) : HeroResDto {
		$hero = $this->getHero($request);

		/** @var UpdateHeroReqDto $heroData */
		$heroData = $request->getParsedBody();

		return $this->heroFacade->updateHero($hero, $heroData);
	}

}