<?php declare(strict_types = 1);

namespace App\API\V1\Hero;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Hero\Hero;
use App\Model\Api\Exception\NotFoundException;

/**
 * @Apitte\Path("/hero")
 * @Apitte\Tag("hero")
 */
abstract class BaseHeroController extends BaseV1Controller {

	protected function getHero(ApiRequest $request) : Hero {
		$player = $this->getPlayer($request);
		$hero = $player->getHero();

		if ($hero === null) {
			throw new NotFoundException("Hero not found");
		}

		return $hero;
	}

}