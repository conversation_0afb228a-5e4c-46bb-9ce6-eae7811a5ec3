<?php declare(strict_types = 1);

namespace App\API\V1\Hero;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\Domain\Api\Facade\HeroItemFacade;
use App\Domain\Api\Response\HeroItemsResDto;
use App\Domain\Api\Response\ItemPlacedResDto;
use App\Domain\Api\Response\ItemResDto;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/item")
 * @Apitte\Id("hero-item")
 */
class HeroItemController extends BaseHeroController {

	public function __construct(
		private readonly HeroItemFacade $heroItemFacade,
	) {}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Get hero items indexed by slot index.
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("GET"),
	 * @Apitte\Responses({
	 *       @Apitte\Response(code="200", description="Success"),
	 *  })
	 */
	public function items(ApiRequest $request) : HeroItemsResDto {
		$hero = $this->getHero($request);
		return $this->heroItemFacade->getAllItems($hero);
	}



	/**
	 * @Apitte\OpenApi("
	 *   summary: Move player item to another slot.
	 * ")
	 * @Apitte\Path("/move")
	 * @Apitte\Method("POST"),
	 * @Apitte\RequestParameters({
	 *      @Apitte\RequestParameter(name="from", type="int", description="From slot index", in="query", required=true),
	 *      @Apitte\RequestParameter(name="to", type="int", description="To slot index", in="query", required=true),
	 *  }),
	 * @Apitte\Responses({
	 *      @Apitte\Response(code="200", description="Success"),
	 *  })
	 */
	public function move(ApiRequest $request) : HeroItemsResDto {
		$hero = $this->getHero($request);
		$from = Caster::toString($request->getParameter('from'));
		$to = Caster::toString($request->getParameter('to'));

		return $this->heroItemFacade->moveItem($hero, $from, $to);
	}

	/**
	 * @Apitte\OpenApi("
	 *   summary: Get player item.
	 * ")
	 * @Apitte\Path("/{uuid}")
	 * @Apitte\Method("GET"),
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="uuid", type="string", description="Item UUID", in="path", required=true),
	 * })
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=ItemResDto::class),
	 *     @Apitte\Response(code="404", description="Not found"),
	 * })
	 */
	public function item(ApiRequest $request) : ItemResDto {
		$hero = $this->getHero($request);
		$uuid = Caster::toString($request->getParameter('uuid'));

		return $this->heroItemFacade->findItem($hero, $uuid);
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Pick up item
	 * ")
	 * @Apitte\Path("/{uuid}/pick-up")
	 * @Apitte\Method("POST")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="uuid", type="string", description="Item UUID", in="path", required=true),
	 * })
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=ItemResDto::class),
	 *     @Apitte\Response(code="404", description="Not found"),
	 * })
	 */
	public function pickUp(ApiRequest $request) : ItemResDto {
		$hero = $this->getHero($request);
		$uuid = Caster::toString($request->getParameter('uuid'));

		return $this->heroItemFacade->pickUp($hero, $uuid);
	}


	/**
	 * @Apitte\OpenApi("
	 * 		summary: Drop item
	 * ")
	 * @Apitte\Path("/{uuid}/drop")
	 * @Apitte\Method("POST")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="uuid", type="string", description="Item UUID", in="path", required=true),
	 * })
	 * @Apitte\Responses({
	 *     @Apitte\Response(code="200", description="Success", entity=ItemPlacedResDto::class),
	 *     @Apitte\Response(code="404", description="Not found"),
	 * })
	 */
	public function dropItem(ApiRequest $request) : ItemPlacedResDto {
		$hero = $this->getHero($request);
		$uuid = Caster::toString($request->getParameter('uuid'));

		return $this->heroItemFacade->dropItem($hero, $uuid);
	}

}