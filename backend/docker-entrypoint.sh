#!/bin/bash
set -e

# Function to log messages with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check if we're running as root (which we should be in the container)
if [ "$(id -u)" != "0" ]; then
    log_message "Warning: Not running as root, some operations might fail"
fi

# Create necessary directories with proper permissions
log_message "Creating necessary directories"
mkdir -p /app/var/log /app/var/temp /app/www/files
chown -R www-data:www-data /app/var/log /app/var/temp /app/www/files
chmod -R 0777 /app/var/log /app/var/temp /app/www/files

# Create the private directory for OAuth keys and generate keys if they don't exist
export APP_OAUTH_KEYS_PATH=${APP_OAUTH_KEYS_PATH:-"/app/resources/oauth"}

if [ -z "$APP_OAUTH_KEY" ]; then
  log_message "Error: APP_OAUTH_KEY is not set in the environment"
  exit 1
fi

if [ -z "$APP_OAUTH_KEY_PASS_PHRASE" ]; then
  log_message "Error: APP_OAUTH_KEY_PASS_PHRASE is not set in the environment"
  exit 1
fi

if [ -d "$APP_OAUTH_KEYS_PATH" ]; then
  log_message "OAuth keys directory already exists"
else
  log_message "Creating OAuth keys directory"
  mkdir -p "$APP_OAUTH_KEYS_PATH"
  chown -R www-data:www-data "$APP_OAUTH_KEYS_PATH"
  chmod -R 0777 "$APP_OAUTH_KEYS_PATH"
fi

if [ -f "$APP_OAUTH_KEYS_PATH/private.key" ]; then
  log_message "OAuth private key already exists"
else
  log_message "Generating OAuth private key"
  openssl genrsa -aes128 -passout pass:$APP_OAUTH_KEY_PASS_PHRASE -out "$APP_OAUTH_KEYS_PATH/private.key" 2048
fi

if [ -f "$APP_OAUTH_KEYS_PATH/public.key" ]; then
  log_message "OAuth public key already exists"
else
  log_message "Generating OAuth public key"
  openssl rsa -in "$APP_OAUTH_KEYS_PATH/private.key" -passin pass:$APP_OAUTH_KEY_PASS_PHRASE -pubout -out "$APP_OAUTH_KEYS_PATH/public.key"
fi

# Set permissions for the OAuth keys
chown -R www-data:www-data "$APP_OAUTH_KEYS_PATH"

# Clear cache
log_message "Clearing cache"
rm -rf /app/var/temp/cache/*

# Run database migrations if needed
if [ -f "/app/bin/console" ]; then
    log_message "Running database migrations"
    su -s /bin/bash -c "cd /app && php ./bin/console mi:mi --allow-no-migration --no-interaction" www-data || {
        log_message "Warning: Migrations failed, but continuing"
    }

    log_message "Generating proxies"
    su -s /bin/bash -c "cd /app && php ./bin/console orm:generate-proxies" www-data || {
        log_message "Warning: Proxy generation failed, but continuing"
    }
else
    log_message "Skipping migrations: bin/console not found"
fi

# Start PHP-FPM
log_message "Starting PHP-FPM"
exec php-fpm
