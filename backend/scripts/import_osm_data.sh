#!/bin/sh

base_path=$(dirname "$0")
force=${1:-false}

mkdir -p "${base_path}/overpass-api/data"

if [ "$force" = true ]; then
  echo "Forcing download of OSM data..."
  rm -rf "${base_path}/overpass-api/data/*.osm"
fi

APP_DB_HOST=${APP_DB_HOST:-"localhost"}
APP_DB_PORT=${APP_DB_PORT:-"5432"}
APP_DB_USER=${APP_DB_USER:-"kokume"}
APP_DB_PASSWORD=${APP_DB_PASSWORD:-"C9WRkn3ULb49Bih8YsR2k4aR"}
APP_DB_NAME=${APP_DB_NAME:-"kokume_1"}

DB_PREFIX="osm"

# Set PostgreSQL connection parameters
export PGPASSWORD="${APP_DB_PASSWORD}"

echo "Downloading OSM data from Overpass API..."

for query_file in `ls ${base_path}/overpass-api/*.txt`; do
  overpass_query=`cat "$query_file"`
  output_file="$base_path/overpass-api/data/$(basename $query_file .txt).osm"

  if [ -f "$output_file" ]; then
    echo " - $query_file -> $output_file (already downloaded)"
    continue
  fi

  echo " - $query_file -> $output_file"
  curl -X GET --data-urlencode "data=${overpass_query}" --progress-bar -o "${output_file}"  "https://overpass-api.de/api/interpreter"
done

echo "Importing OSM data to PostgreSQL..."

osm2pgsql --latlong -p ${DB_PREFIX} -H "${APP_DB_HOST}" -s -c -U "${APP_DB_USER}" -d "${APP_DB_NAME}" "${base_path}"/overpass-api/data/*.osm

echo "OSM data imported successfully."

# Clean up downloaded OSM data
echo "Cleaning up downloaded OSM data..."
rm -rf "${base_path}"/overpass-api/data/*.osm
echo "Done."