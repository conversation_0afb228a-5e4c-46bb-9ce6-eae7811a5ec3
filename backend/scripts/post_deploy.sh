#!/bin/bash

echo "Checking directories"
mkdir -p var/log var/temp

echo "Clearing cache"
rm -rf var/temp/cache/*

echo "Running migrations"
if php8.3 ./bin/console mi:mi --allow-no-migration --no-interaction ; then
    echo "Migrations ran successfully"
else
    echo "Migrations failed 🤯"
    exit 1
fi

echo "Generating proxies"
if php8.3 ./bin/console orm:generate-proxies ; then
    echo "Proxies generated successfully"
else
    echo "Proxies generation failed 🤯"
    exit 1
fi

echo ""
echo "**************************"
echo "**- Deploy complete 🥰 -**"
echo "**************************"
echo ""

exit 0
