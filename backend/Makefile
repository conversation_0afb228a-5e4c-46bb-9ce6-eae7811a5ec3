#!make

include dev.env

############################################################
# PROJECT ##################################################
############################################################
.PHONY: project
project: install setup

.PHONY: init
init:
	cp config/local.neon.example config/local.neon

.PHONY: install
install:
	composer install
	npm ci

.PHONY: setup
setup:
	mkdir -p var/tmp var/log
	chmod 0777 var/tmp var/log

.PHONY: clean
clean:
	find var/temp -mindepth 1 ! -name '.gitignore' -type f -or -type d -exec rm -rf {} +
	find var/log -mindepth 1 ! -name '.gitignore' -type f -or -type d -exec rm -rf {} +

############################################################
# DEVELOPMENT ##############################################
############################################################
.PHONY: qa
qa: cs phpstan

.PHONY: cs
cs:
	vendor/bin/codesniffer app tests

.PHONY: csf
csf:
	vendor/bin/codefixer app tests

.PHONY: phpstan
phpstan:
	vendor/bin/phpstan analyse -c phpstan.neon --memory-limit=512M

.PHONY: tests
tests:
	vendor/bin/tester -s -p php --colors 1 -C tests

.PHONY: coverage
coverage:
	vendor/bin/tester -s -p phpdbg --colors 1 -C --coverage ./coverage.xml --coverage-src ./app tests

.PHONY: dev
dev:
	XDEBUG_MODE=debug NETTE_DEBUG=1 NETTE_ENV=dev php -S 0.0.0.0:8000 -t www

.PHONY: build
build:
	NETTE_DEBUG=1 bin/console orm:schema-tool:drop --force --full-database
	NETTE_DEBUG=1 bin/console migrations:migrate --no-interaction
	NETTE_DEBUG=1 bin/console doctrine:fixtures:load --no-interaction --append
	npm run build

.PHONY: webpack
webpack:
	npm run dev

############################################################
# DOCKER ###################################################
############################################################

.PHONY: docker-up
docker-up:
	docker compose up -d

overpass_query=`cat overpass-data-query.txt`
.PHONY: docker-init
docker-init:
	curl -X GET --data-urlencode "data=$(overpass_query)" --progress-bar -o .docker/data/data.osm "https://overpass-api.de/api/interpreter"
	docker run --volume .:/app --env-file dev.env iboates/osm2pgsql:latest --latlong -p "osm" -H $(APP_DB_HOST) -s -U $(APP_DB_USER) -d $(APP_DB_NAME) -c .docker/data/data.osm
	docker compose run osm2pgsql --latlong -p "osm" -H $(APP_DB_HOST) -s -U $(APP_DB_USER) -d $(APP_DB_NAME) -c /app/.docker/data/data.osm
	docker compose exec php /app/bin/console o:s:u --force
	docker compose exec php /app/bin/console kokume:init-game

.PHONY: docker-down
docker-down:
	docker compose down