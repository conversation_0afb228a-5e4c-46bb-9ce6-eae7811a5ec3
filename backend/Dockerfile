FROM registry.kickme.cz/docker/ci-cd/php:8.3-fpm

# Copy the application files
COPY --chown=www-data:www-data . /app

# Copy and set up the entrypoint script
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Create directories for persistent data
RUN mkdir -p /app/var/log /app/var/temp /app/www/files \
    && chown -R www-data:www-data /app/var/log /app/var/temp /app/www/files \
    && chmod -R 0777 /app/var/log /app/var/temp /app/www/files

# Set the working directory
WORKDIR /app

VOLUME ["/app/var/log", "/app/var/temp", "/app/www/files", "/app/resources/oauth"]

# Set the entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]