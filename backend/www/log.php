<?php
/**
 *
 * <AUTHOR> ( <EMAIL> )
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-type: text/html; charset=utf-8');

const LOG_DIR = __DIR__ . '/../var/log';
const ALLOWED_IP = [
	'************', #ws
	'**************', #haho
	'127.0.0.1',
	'::1'
];

if(!in_array($_SERVER['REMOTE_ADDR'], ALLOWED_IP)) {
	header("HTTP/1.0 404 Not Found");
	exit;
}

if(isset($_GET['show-file']) && $file = $_GET['show-file']) {
	echo file_get_contents(LOG_DIR . '/' . $file);
	exit;
}

if(isset($_GET['remove-file']) && $file = $_GET['remove-file']) {
	unlink(LOG_DIR . '/' . $file);
	header('Location: ' . strtok($_SERVER['REQUEST_URI'], '?'));
}

if(isset($_GET['remove-all']) && $_GET['remove-all']) {
	removeAll();
	header('Location: ' . strtok($_SERVER['REQUEST_URI'], '?'));
}

echo '<table border="1" style="width: 90%; margin: auto;">';
echo '<thead><tr>';
echo '<th>file name</th><th>last change</th><th>size</th><th><a href="?remove-all=1" onclick="if(!confirm(\'Opravdu chceš vše smazat?\')) event.preventDefault();">REMOVE ALL</a></th>';
echo '</tr></thead>';
echo '<tbody>';

$files = array_diff(scandir(LOG_DIR), ['.', '..']);

foreach ($files as $file) {
	$filePath = LOG_DIR . '/' . $file;
	echo '<tr>';
	echo '<td><a href="?show-file=' . $file . '">' . $file . '</a></td>';
	echo '<td>' . date("F d Y H:i:s.", filemtime($filePath)) . '</td>';
	echo '<td>' . humanFilesize(filesize($filePath)) . '</td>';
	if(!is_dir($filePath)) {
		echo '<td><a onclick="if(!confirm(\'Opravdu chceš smazat ' . $file . '?\')) event.preventDefault();" href="?remove-file=' . $file . '">remove</a></td>';
	}
	echo '</tr>';
}

echo '</tbody>';
echo '</table>';


function humanFilesize($bytes, $decimals = 2) {
	$sz = 'BKMGTP';
	$factor = floor((strlen($bytes) - 1) / 3);
	return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . @$sz[$factor];
}

function removeAll() {
	$files = new RecursiveIteratorIterator(
		new RecursiveDirectoryIterator(LOG_DIR, RecursiveDirectoryIterator::SKIP_DOTS),
		RecursiveIteratorIterator::CHILD_FIRST
	);

	foreach ($files as $file) {
		$todo = ($file->isDir() ? 'rmdir' : 'unlink');
		$todo($file->getRealPath());
	}
}