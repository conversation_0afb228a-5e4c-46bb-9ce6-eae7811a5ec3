import { createRequire } from 'node:module'
const require = createRequire(import.meta.url)

// Node
const path = require("path");

// Webpack
const webpack = require("webpack");
const { merge } = require("webpack-merge");

// Webpack plugins
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
const { WebpackManifestPlugin } = require('webpack-manifest-plugin');

// Other
const devMode = process.env.NODE_ENV !== "production";

// Webpack abilities
const WEBPACK_DEV_SERVER_HOST = process.env.WEBPACK_DEV_SERVER_HOST || "localhost";
const WEBPACK_DEV_SERVER_PORT = parseInt(process.env.WEBPACK_DEV_SERVER_PORT, 10) || 8080;
const WEBPACK_DEV_SERVER_PROXY_HOST = process.env.WEBPACK_DEV_SERVER_PROXY_HOST || "localhost";
const WEBPACK_DEV_SERVER_PROXY_PORT = parseInt(process.env.WEBPACK_DEV_SERVER_PROXY_PORT, 10) || 8000;
const WEBPACK_REPORT = process.env.WEBPACK_REPORT || false;

// Config
const ROOT_PATH = import.meta.dirname || process.cwd();
const CACHE_PATH = ROOT_PATH + "/var/temp/webpack";

let config = {
	mode: devMode ? "development" : "production",
	context: path.join(ROOT_PATH, "assets"),
	entry: {
		admin: path.join(ROOT_PATH, "assets/admin.ts"),
		logo: path.join(ROOT_PATH, "assets/images/logo.webp"),
		datagrid: ["ublaboo-datagrid", "ublaboo-datagrid/assets/datagrid.css"],
		fontawesome: "font-awesome/scss/font-awesome.scss",
	},
	output: {
		path: path.join(ROOT_PATH, "www/admin"),
		publicPath: "/admin/",
		filename: '[name].bundle.js',
	},
	cache: {
		type: 'filesystem',
		cacheDirectory: path.resolve(CACHE_PATH, 'cache'),
	},
	watchOptions: {
		ignored: /node_modules/,
	},
	module: {
		rules: [
			// Js files
			{
				test: /\.js$/,
				exclude: file => (
					/node_modules/.test(file)
				),
				loader: 'esbuild-loader',
				options: {
					loader: 'js',
				}
			},

			// Typescript files
			{
				test: /\.ts$/,
				loader: 'esbuild-loader',
				options: {
					loader: 'ts',
				}
			},

			// Font files
			{
				test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/i,
				type: 'asset/resource',
				generator: {
					filename: 'fonts/[name].[hash:8].[ext]',
				},
			},

			// Image files
			{
				test: /\.(jpe?g|png|gif|svg|ico|webp)$/i,
				type: 'asset/resource',
				generator: {
					filename: 'images/minified/[name]_[hash:8][ext]',
				},
			},

			// CSS files
			{
				test: /\.(css|scss|sass)$/,
				use: [
					MiniCssExtractPlugin.loader,
					{
						loader: 'css-loader',
						options: {
							sourceMap: false,
							importLoaders: 2,
							modules: false
						}
					},
					{
						loader: "postcss-loader",
						options: {
							postcssOptions: {
								ident: "postcss",
								plugins: [require("autoprefixer"), require('postcss-preset-env')]
							}
						}
					},
					"sass-loader"
				],
			},
		]
	},
	resolve: {
		alias: {
			"@": path.resolve(ROOT_PATH),
		},
		extensions: [".js", ".ts"],
		modules: [
			'node_modules',
		],
		fallback: {
			"child_process": false,
			buffer: require.resolve('buffer/'),
		},
	},
	plugins: [
		// clean dist folder
		new CleanWebpackPlugin(),

		// polyfill node
		new NodePolyfillPlugin(), new webpack.DefinePlugin({
			__VUE_OPTIONS_API__: true,
			__VUE_PROD_DEVTOOLS__: devMode,
			__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: devMode,
		}),

		// fix legacy jQuery plugins which depend on globals
		new webpack.ProvidePlugin({
			// fix legacy jQuery plugins which depend on globals
			$: "jquery",
			jQuery: "jquery",
			"window.jQuery": "jquery",
			"window.$": "jquery",

			// popper is required by bootstrap
			Popper: ["popper.js", "default"],

			// naja global
			naja: ['naja', 'default'],
		}),

		// extract css
		new MiniCssExtractPlugin({
			filename: !devMode ? "[name].[chunkhash:8].bundle.css" : "[name].bundle.css",
		}),

		// manifest
		new WebpackManifestPlugin({}),

		// fallback for buffer
		new webpack.ProvidePlugin({
			Buffer: ['buffer', 'Buffer'],
		}),
	],
};

if (WEBPACK_REPORT) {
	config.plugins.push(
		new BundleAnalyzerPlugin({
			analyzerMode: "static",
			generateStatsFile: true,
			openAnalyzer: false,
			reportFilename: path.join(CACHE_PATH, "webpack-report/index.html"),
			statsFilename: path.join(CACHE_PATH, "webpack-report/stats.json"),
		})
	);
}

if (process.env.NODE_ENV === "development") {
	const development = {
		output: {
			globalObject: 'this'
		},
	};

	config = merge(config, development);
}

if (process.env.NODE_ENV === "production") {
	const production = {
		output: {
			filename: '[name].[contenthash:8].bundle.js',
			chunkFilename: '[name].[contenthash:8].chunk.js'
		},
		devtool: "source-map",
		plugins: [],
	};

	config = merge(config, production);
}

export default config;