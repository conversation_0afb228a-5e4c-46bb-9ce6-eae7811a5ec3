<?php

namespace PHPSTORM_META {

	override(\App\Model\Database\EntityManager::getRepository(0), map([
		'\App\Domain\User\User' => \App\Domain\User\UserRepository::class,

		'\App\Domain\ApiClient\ApiClient' => \App\Domain\OAuth2\Client\ClientRepository::class,
		'\App\Domain\ApiAccessToken\ApiAccessToken' => \App\Domain\OAuth2\AccessToken\AccessTokenRepository::class,
		'\App\Domain\ApiRefreshToken\ApiRefreshToken' => \App\Domain\OAuth2\RefreshToken\RefreshTokenRepository::class,

		'\App\Domain\Region\ResourceRegion' => \App\Domain\Region\Repository\ResourceRegionRepository::class,
		'\App\Domain\Region\Region' => \App\Domain\Region\Repository\RegionRepository::class,

		'\App\Domain\Building\Building' => \App\Domain\Building\BuildingRepository::class,
		'\App\Domain\Building\BuildingType' => \App\Domain\Building\BuildingTypeRepository::class,

		'\App\Domain\Player\Player' => \App\Domain\Player\Repository\PlayerRepository::class,
		'\App\Domain\Player\PlayerPosition' => \App\Domain\Player\Repository\PlayerPositionRepository::class,

		'\App\Domain\Item\ItemDefinition' => \App\Domain\Item\ItemDefinitionRepository::class,
		'\App\Domain\Item\ItemPlaced' => \App\Domain\Item\ItemPlacedRepository::class,
		'\App\Domain\Item\Item' => \App\Domain\Item\ItemRepository::class,
	]));
}
